#!/bin/bash

# CVE补丁收集运行脚本
# 功能：执行2023年8月23日到2025年7月24日期间的CVE补丁对收集

set -e  # 遇到错误立即退出

echo "=================================================="
echo "NVD补丁收集器 - CVE补丁对收集任务"
echo "时间范围：2023-08-23 到 2025-07-24"
echo "=================================================="

# 脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/home/<USER>"

# 配置参数
OUTPUT_DIR="$SCRIPT_DIR"
CONFIG_FILE="$PROJECT_ROOT/src/data_collection/config/sources.json"
START_DATE="2023-08-23"
END_DATE="2025-07-24"
# MAX_CVES=100  # 注释掉CVE数量限制，处理所有可用的CVE
MAX_DEPTH=5  # 增加跟踪深度以发现更多补丁
CONFIDENCE=0.7

# 分段处理参数（NVD API限制最多查询120天）
SEGMENT_DAYS=90  # 每次查询90天，留出缓冲

# 检查Python环境
echo "检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到python3命令"
    exit 1
fi

# 检查项目目录
if [ ! -d "$PROJECT_ROOT" ]; then
    echo "错误：项目根目录不存在: $PROJECT_ROOT"
    exit 1
fi

# 检查配置文件
if [ ! -f "$CONFIG_FILE" ]; then
    echo "警告：配置文件不存在: $CONFIG_FILE"
    echo "将尝试不使用配置文件运行"
    CONFIG_FILE=""
fi

# 检查收集脚本
COLLECTOR_SCRIPT="$SCRIPT_DIR/nvd_patch_collector.py"
if [ ! -f "$COLLECTOR_SCRIPT" ]; then
    echo "错误：收集脚本不存在: $COLLECTOR_SCRIPT"
    exit 1
fi

# 创建日志目录
mkdir -p "$OUTPUT_DIR/logs"

# 输出运行参数
echo ""
echo "运行参数："
echo "  输出目录: $OUTPUT_DIR"
echo "  配置文件: $CONFIG_FILE"
echo "  开始日期: $START_DATE"
echo "  结束日期: $END_DATE"
echo "  最大CVE数量: 无限制（处理所有可用CVE）"
echo "  最大跟踪深度: $MAX_DEPTH"
echo "  置信度阈值: $CONFIDENCE"
echo ""

# 设置Python路径
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 构建命令行参数
CMD_ARGS=(
    --output-dir "$OUTPUT_DIR"
    --start-date "$START_DATE"
    --end-date "$END_DATE"
    --max-cves "$MAX_CVES"
    --max-depth "$MAX_DEPTH"
    --confidence "$CONFIDENCE"
)

# 如果配置文件存在，添加配置参数
if [ -n "$CONFIG_FILE" ]; then
    CMD_ARGS+=(--config "$CONFIG_FILE")
fi

# 如果设置了GitHub令牌，添加到参数中
if [ -n "$GITHUB_TOKEN" ]; then
    CMD_ARGS+=(--github-token "$GITHUB_TOKEN")
    echo "  GitHub令牌: 已设置 (${GITHUB_TOKEN:0:4}...)"
else
    echo "  GitHub令牌: 未设置 (某些功能可能受限)"
fi

echo ""
echo "开始执行CVE补丁收集..."
echo "命令: python3 $COLLECTOR_SCRIPT ${CMD_ARGS[*]}"
echo ""

# 记录开始时间
START_TIME=$(date +%s)

# 分段处理时间范围
echo "开始分段处理时间范围（每段${SEGMENT_DAYS}天）..."

# 将日期转换为秒数进行计算
start_epoch=$(date -d "$START_DATE" +%s)
end_epoch=$(date -d "$END_DATE" +%s)
segment_seconds=$((SEGMENT_DAYS * 24 * 3600))

current_start=$start_epoch
segment_count=0
total_patches=0

while [ $current_start -lt $end_epoch ]; do
    segment_count=$((segment_count + 1))
    current_end=$((current_start + segment_seconds))
    
    # 确保不超过结束日期
    if [ $current_end -gt $end_epoch ]; then
        current_end=$end_epoch
    fi
    
    # 转换回日期格式
    segment_start_date=$(date -d "@$current_start" +%Y-%m-%d)
    segment_end_date=$(date -d "@$current_end" +%Y-%m-%d)
    
    echo ""
    echo "========== 段 $segment_count =========="
    echo "时间范围: $segment_start_date 到 $segment_end_date"
    echo "=================================="
    
    # 创建当前段的参数数组
    SEGMENT_ARGS=(
        --output-dir "$OUTPUT_DIR/segment_${segment_count}"
        --start-date "$segment_start_date"
        --end-date "$segment_end_date"
        # --max-cves "$MAX_CVES"  # 注释掉以处理所有CVE
        --max-depth "$MAX_DEPTH"
        --confidence "$CONFIDENCE"
    )
    
    # 添加配置文件参数
    if [ -n "$CONFIG_FILE" ]; then
        SEGMENT_ARGS+=(--config "$CONFIG_FILE")
    fi
    
    # 添加GitHub令牌参数
    if [ -n "$GITHUB_TOKEN" ]; then
        SEGMENT_ARGS+=(--github-token "$GITHUB_TOKEN")
    fi
    
    # 创建段目录
    mkdir -p "$OUTPUT_DIR/segment_${segment_count}"
    
    # 执行当前段的收集
    echo "执行命令: python3 $COLLECTOR_SCRIPT ${SEGMENT_ARGS[*]}"
    
    if python3 "$COLLECTOR_SCRIPT" "${SEGMENT_ARGS[@]}"; then
        echo "✅ 段 $segment_count 收集完成"
        
        # 统计当前段的补丁数量
        segment_result_file="$OUTPUT_DIR/segment_${segment_count}/nvd_patch_pairs.json"
        if [ -f "$segment_result_file" ]; then
            segment_patches=$(grep -o '"total_patch_pairs": [0-9]*' "$segment_result_file" | grep -o '[0-9]*' || echo "0")
            total_patches=$((total_patches + segment_patches))
            echo "   当前段收集到 $segment_patches 个补丁对"
        fi
    else
        echo "❌ 段 $segment_count 收集失败，继续下一段"
    fi
    
    # 移动到下一段
    current_start=$((current_end + 1))
done

# 合并所有段的结果
echo ""
echo "=========================================="
echo "合并所有段的收集结果..."
echo "=========================================="

# 创建合并后的结果文件
MERGED_RESULT="$OUTPUT_DIR/nvd_patch_pairs_merged.json"
echo "{" > "$MERGED_RESULT"
echo '  "collection_info": {' >> "$MERGED_RESULT"
echo "    \"total_patch_pairs\": $total_patches," >> "$MERGED_RESULT"
echo "    \"collection_date\": \"$(date -Iseconds)\"," >> "$MERGED_RESULT"
echo "    \"time_range\": {" >> "$MERGED_RESULT"
echo "      \"start\": \"$START_DATE\"," >> "$MERGED_RESULT"
echo "      \"end\": \"$END_DATE\"" >> "$MERGED_RESULT"
echo "    }," >> "$MERGED_RESULT"
echo '    "criteria": {' >> "$MERGED_RESULT"
echo '      "language": "C/C++",' >> "$MERGED_RESULT"
echo '      "patch_selection": "mystique_standard",' >> "$MERGED_RESULT"
echo '      "min_patches_per_cve": 2' >> "$MERGED_RESULT"
echo "    }," >> "$MERGED_RESULT"
echo "    \"segments_processed\": $segment_count" >> "$MERGED_RESULT"
echo "  }," >> "$MERGED_RESULT"
echo '  "patch_pairs": [' >> "$MERGED_RESULT"

# 合并所有段的补丁对
first_entry=true
for i in $(seq 1 $segment_count); do
    segment_file="$OUTPUT_DIR/segment_${i}/nvd_patch_pairs.json"
    if [ -f "$segment_file" ]; then
        # 提取patch_pairs数组内容
        segment_patches=$(grep -A 999999 '"patch_pairs":' "$segment_file" | sed '1d' | sed '$d' | sed '$d')
        if [ -n "$segment_patches" ] && [ "$segment_patches" != "  []" ]; then
            if [ "$first_entry" = false ]; then
                echo "," >> "$MERGED_RESULT"
            fi
            echo "$segment_patches" >> "$MERGED_RESULT"
            first_entry=false
        fi
    fi
done

echo "" >> "$MERGED_RESULT"
echo "  ]" >> "$MERGED_RESULT"
echo "}" >> "$MERGED_RESULT"

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

echo ""
echo "=================================================="
echo "CVE补丁收集任务完成"
echo "处理了 $segment_count 个时间段"
echo "总共收集到 $total_patches 个补丁对"
echo "运行时间: ${DURATION}秒"
echo "=================================================="

# 显示结果文件
echo ""
echo "生成的文件："
echo "  合并结果: $MERGED_RESULT"
find "$OUTPUT_DIR" -name "*.json" -o -name "*.log" | head -10

exit 0 