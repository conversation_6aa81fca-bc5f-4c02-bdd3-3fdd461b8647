nohup: ignoring input
🚀 启动内存优化版CVE补丁收集器
处理所有可用CVE，支持多线程并发，优化内存使用
开始时间: Sat Jul 26 18:40:33 UTC 2025
内存限制: 4.0GB
批处理大小: 20
段大小: 7天
📦 检查Python依赖...
📊 计划处理 101 个段，每段 7 天

========== 段 1/101 ==========
时间范围: 2023-08-23 到 2023-08-30
模式: 内存优化 + 批处理 + 并发处理
段开始前内存状态:
               total        used        free      shared  buff/cache   available
Mem:           629Gi       5.4Gi       598Gi        15Mi        25Gi       619Gi
Swap:           30Gi          0B        30Gi
执行: python3 nvd_patch_collector.py --output-dir /home/<USER>/202300823nvdpatched_collected/segment_1 --start-date 2023-08-23 --end-date 2023-08-30 --max-depth 5 --confidence 0.7 --config /home/<USER>/src/data_collection/config/sources.json
[32m2025-07-26 18:40:35.101[0m | [1mINFO    [0m | [36m__main__[0m:[36m__init__[0m:[36m173[0m | [1mNVD补丁收集器初始化完成[0m
[32m2025-07-26 18:40:35.101[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m初始化后 内存使用: RSS=223.4MB, VMS=3235.4MB, 百分比=0.0%, 可用=634431.6MB[0m
[32m2025-07-26 18:40:35.102[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m438[0m | [1m开始收集 2023-08-23 到 2023-08-30 期间的CVE补丁对[0m
[32m2025-07-26 18:40:35.102[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m收集开始前 内存使用: RSS=223.4MB, VMS=3235.4MB, 百分比=0.0%, 可用=634431.6MB[0m
[32m2025-07-26 18:40:35.102[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m442[0m | [1m📊 API调用参数:[0m
[32m2025-07-26 18:40:35.103[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m443[0m | [1m  - 开始日期: 2023-08-23T00:00:00.000[0m
[32m2025-07-26 18:40:35.103[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m444[0m | [1m  - 结束日期: 2023-08-30T00:00:00.999[0m
[32m2025-07-26 18:40:35.103[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m445[0m | [1m  - 日期类型: published[0m
[32m2025-07-26 18:40:35.103[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m446[0m | [1m  - 最大CVE数量: 无限制[0m
[32m2025-07-26 18:40:35.104[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m447[0m | [1m  - 批处理大小: 50[0m
[32m2025-07-26 18:40:35.104[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m450[0m | [1m🔍 正在调用NVD API获取CVE列表...[0m
[32m2025-07-26 18:40:47.136[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m462[0m | [1m找到 354 个CVE[0m
[32m2025-07-26 18:40:47.139[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m471[0m | [1m过滤后需要处理的CVE数量: 354[0m
[32m2025-07-26 18:40:47.139[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 1/8 (CVE 1-50)[0m
[32m2025-07-26 18:40:47.140[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 1 开始前 内存使用: RSS=232.7MB, VMS=3247.6MB, 百分比=0.0%, 可用=634411.2MB[0m
[32m2025-07-26 18:40:47.140[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 1 使用 16 个线程处理 50 个CVE[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:40:53.637[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:53.765[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 1/50] CVE-2023-39986: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:40:53.781[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:53.914[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 2/50] CVE-2023-39985: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:40:53.939[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:54.057[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 3/50] CVE-2023-39984: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:40:55.197[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:55.330[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 4/50] CVE-2023-3495: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:40:57.123[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:57.305[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 5/50] CVE-2023-4041: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:40:57.833[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:58.016[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 6/50] CVE-2023-41098: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:40:58.926[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:59.086[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 7/50] CVE-2023-4404: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:40:59.220[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:59.327[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 8/50] CVE-2023-38585: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:40:59.480[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:40:59.640[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 9/50] CVE-2023-40144: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:00.129[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:00.298[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 10/50] CVE-2023-40158: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:00.360[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度10/50 内存使用: RSS=1806.0MB, VMS=5747.5MB, 百分比=0.3%, 可用=632461.0MB[0m
[32m2025-07-26 18:41:00.784[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:01.079[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 11/50] CVE-2023-41100: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:01.967[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:02.155[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 12/50] CVE-2023-4431: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:02.408[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:02.617[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 13/50] CVE-2023-4429: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:04.188[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:04.430[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 14/50] CVE-2023-32119: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
/home/<USER>/src/data_collection/services/patch_tracer/reference_network.py:446: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`). Consider using `matplotlib.pyplot.close()`.
  plt.figure(figsize=(18, 12), dpi=300)  # 增加图像尺寸
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:06.627[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:06.900[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 15/50] CVE-2023-32236: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:07.595[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:07.754[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 16/50] CVE-2023-32496: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:08.338[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:08.523[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 17/50] CVE-2023-32497: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:09.281[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:09.446[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 18/50] CVE-2023-32498: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:10.012[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:10.208[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 19/50] CVE-2023-4042: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:10.256[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:10.490[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 20/50] CVE-2023-32499: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:10.495[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度20/50 内存使用: RSS=3162.5MB, VMS=6973.0MB, 百分比=0.5%, 可用=631263.1MB[0m
[32m2025-07-26 18:41:10.729[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:10.941[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 21/50] CVE-2023-28994: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:11.100[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:11.303[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 22/50] CVE-2023-32300: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:12.266[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:12.498[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 23/50] CVE-2023-32505: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:12.638[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:12.845[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 24/50] CVE-2023-3899: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:13.210[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:41:13.389[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 25/50] CVE-2023-41104: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:16.236[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:16.488[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 26/50] CVE-2023-32509: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:18.983[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:19.250[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 27/50] CVE-2023-1409: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:19.565[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:41:19.785[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:19.973[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 28/50] CVE-2023-41105: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:19.992[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 29/50] CVE-2023-20169: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:20.024[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:20.281[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:20.543[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 30/50] CVE-2023-20230: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:20.660[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度30/50 内存使用: RSS=3393.9MB, VMS=7131.4MB, 百分比=0.5%, 可用=630933.3MB[0m
[32m2025-07-26 18:41:20.683[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 31/50] CVE-2023-20168: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:21.608[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:21.875[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 32/50] CVE-2023-20200: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:22.049[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:22.323[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 33/50] CVE-2023-20115: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:23.755[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:24.010[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 34/50] CVE-2023-20234: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:41:29.867[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:30.126[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 35/50] CVE-2023-40025: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:30.174[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:30.438[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 36/50] CVE-2023-37379: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:30.804[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:31.077[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 37/50] CVE-2023-40273: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:31.731[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:32.016[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 38/50] CVE-2023-39441: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:33.593[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:41:33.807[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 39/50] CVE-2023-40176: 未找到符合条件的补丁对[0m
未提供GitHub API token，可能会受到API速率限制
未提供GitHub API token，可能会受到API速率限制
[32m2025-07-26 18:41:46.308[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:41:46.505[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 40/50] CVE-2023-40612: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:41:46.506[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度40/50 内存使用: RSS=3509.4MB, VMS=7320.9MB, 百分比=0.5%, 可用=630911.7MB[0m
🧹 清理资源...
