{"cve_id": "CVE-2025-7484", "published_date": "2025-07-12T18:15:21.450", "last_modified_date": "2025-07-15T18:06:44.620", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Vehicle Parking Management System 1.13. Affected is an unknown function of the file /admin/view-outgoingvehicle-detail.php. The manipulation of the argument viewid leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Vehicle Parking Management System 1.13. Se ve afectada una función desconocida del archivo /admin/view-outgoingvehicle-detail.php. La manipulación del argumento viewid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/115", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316134", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316134", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610572", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}