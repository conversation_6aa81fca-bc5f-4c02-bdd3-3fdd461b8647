{"cve_id": "CVE-2025-53101", "published_date": "2025-07-14T20:15:29.180", "last_modified_date": "2025-07-15T20:15:49.550", "descriptions": [{"lang": "en", "value": "ImageMagick is free and open-source software used for editing and manipulating digital images. In versions prior to 7.1.2-0 and 6.9.13-26, in ImageMagick's `magick mogrify` command, specifying multiple consecutive `%d` format specifiers in a filename template causes internal pointer arithmetic to generate an address below the beginning of the stack buffer, resulting in a stack overflow through `vsnprintf()`. Versions 7.1.2-0 and 6.9.13-26 fix the issue."}, {"lang": "es", "value": "ImageMagick es un software gratuito y de código abierto que se utiliza para editar y manipular imágenes digitales. En versiones anteriores a la 7.1.2-0 y la 6.9.13-26, en el comando `magick mogrify` de ImageMagick, especificar varios especificadores de formato `%d` consecutivos en una plantilla de nombre de archivo provoca que la aritmética interna de punteros genere una dirección por debajo del inicio del búfer de pila, lo que provoca un desbordamiento de pila mediante `vsnprintf()`. Las versiones 7.1.2-0 y 6.9.13-26 solucionan este problema."}], "references": [{"url": "https://github.com/ImageMagick/ImageMagick/commit/66dc8f51c11b0ae1f1cdeacd381c3e9a4de69774", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ImageMagick/ImageMagick/security/advisories/GHSA-qh3h-j545-h8c9", "source": "<EMAIL>", "tags": []}]}