{"cve_id": "CVE-2025-50063", "published_date": "2025-07-15T20:15:40.850", "last_modified_date": "2025-07-16T19:15:26.200", "descriptions": [{"lang": "en", "value": "Vulnerability in Oracle Java SE (component: Install).   The supported version that is affected is Oracle Java SE: 8u451. Easily exploitable vulnerability allows low privileged attacker with logon to the infrastructure where Oracle Java SE executes to compromise Oracle Java SE.  Successful attacks require human interaction from a person other than the attacker. Successful attacks of this vulnerability can result in takeover of Oracle Java SE. Note: Applies to installation process on client deployment of Java. CVSS 3.1 Base Score 7.3 (Confidentiality, Integrity and Availability impacts).  CVSS Vector: (CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:H)."}, {"lang": "es", "value": "Vulnerabilidad en Oracle Java SE (componente: Instalación). Las versiones compatibles afectadas son Oracle Java SE: 8u451 y 8u451-perf. Esta vulnerabilidad, fácilmente explotable, permite a un atacante con pocos privilegios, con acceso a la infraestructura donde se ejecuta Oracle Java SE, comprometer Oracle Java SE. Los ataques exitosos requieren la interacción humana de una persona distinta al atacante. Los ataques exitosos de esta vulnerabilidad pueden resultar en la toma de control de Oracle Java SE. Nota: Aplica al proceso de instalación en la implementación de cliente de Java. Puntuación base de CVSS 3.1: 7.3 (Afecta a la confidencialidad, integridad y disponibilidad). Vector CVSS: (CVSS:3.1/AV:L/AC:L/PR:L/UI:R/S:U/C:H/I:H/A:H)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": []}]}