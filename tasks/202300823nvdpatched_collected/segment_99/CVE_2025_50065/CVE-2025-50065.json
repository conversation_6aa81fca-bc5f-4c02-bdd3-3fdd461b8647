{"cve_id": "CVE-2025-50065", "published_date": "2025-07-15T20:15:41.090", "last_modified_date": "2025-07-16T16:15:26.960", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle GraalVM for JDK product of Oracle Java SE (component: Native Image).   The supported version that is affected is Oracle GraalVM for JDK: 24.0.1. <PERSON><PERSON><PERSON><PERSON> to exploit vulnerability allows unauthenticated attacker with network access via HTTP to compromise Oracle GraalVM for JDK.  Successful attacks of this vulnerability can result in unauthorized ability to cause a partial denial of service (partial DOS) of Oracle GraalVM for JDK. CVSS 3.1 Base Score 3.7 (Availability impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:L)."}, {"lang": "es", "value": "Vulnerabilidad en Oracle GraalVM para JDK de Oracle Java SE (componente: Imagen Nativa). La versión compatible afectada es Oracle GraalVM para JDK: 24.0.1. Esta vulnerabilidad, difícil de explotar, permite a un atacante no autenticado con acceso a la red vía HTTP comprometer Oracle GraalVM para JDK. Los ataques con éxito de esta vulnerabilidad pueden permitir que personas no autorizadas causen una denegación de servicio parcial (DOS parcial) de Oracle GraalVM para JDK. Puntuación base de CVSS 3.1: 3.7 (Afecta a la disponibilidad). Vector CVSS: (CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:N/I:N/A:L)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": []}]}