{"cve_id": "CVE-2025-7380", "published_date": "2025-07-14T06:15:28.193", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A stored Cross-Site Scripting (XSS) vulnerability exists in the Access Control of ADM, the issue allows an attacker to inject malicious scripts into the folder name field while creating a new shared folder. These scripts are not properly sanitized and will be executed when the folder name is subsequently displayed in the user interface. This allows attackers to execute arbitrary JavaScript in the context of another user's session, potentially accessing session cookies or other sensitive data.\nAffected products and versions include: from ADM 4.1.0 to ADM 4.3.3.RH61 as well as ADM 5.0.0.RIN1 and earlier."}, {"lang": "es", "value": "Existe una vulnerabilidad de cross-site scripting (XSS) almacenado en Access Control of ADM. Este problema permite a un atacante inyectar scripts maliciosos en el campo de nombre de carpeta al crear una nueva carpeta compartida. Estos scripts no se depuran correctamente y se ejecutan cuando el nombre de la carpeta se muestra posteriormente en la interfaz de usuario. Esto permite a los atacantes ejecutar JavaScript arbitrario en la sesión de otro usuario, lo que podría acceder a cookies de sesión u otros datos confidenciales. Los productos y versiones afectados incluyen: desde ADM 4.1.0 hasta ADM 4.3.3.RH61, así como ADM 5.0.0.RIN1 y anteriores."}], "references": [{"url": "https://www.asustor.com/security/security_advisory_detail?id=44", "source": "<EMAIL>", "tags": []}]}