{"cve_id": "CVE-2025-53639", "published_date": "2025-07-14T20:15:29.487", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "MeterSphere is an open source continuous testing platform. Prior to version 3.6.5-lts, the sortField parameter in certain API endpoints is not properly validated or sanitized. An attacker can supply crafted input to inject and execute arbitrary SQL statements through the sorting functionality. This could result in modification or deletion of database contents, with a potential full compromise of the application’s database integrity and availability. Version 3.6.5-lts fixes the issue."}, {"lang": "es", "value": "MeterSphere es una plataforma de pruebas continuas de código abierto. Antes de la versión 3.6.5-lts, el parámetro sortField en ciertos endpoints de la API no se validaba ni depuraba correctamente. Un atacante puede proporcionar una entrada manipulada para inyectar y ejecutar sentencias SQL arbitrarias mediante la función de ordenación. Esto podría provocar la modificación o eliminación del contenido de la base de datos, con la posible vulneración total de la integridad y disponibilidad de la base de datos de la aplicación. La versión 3.6.5-lts corrige este problema."}], "references": [{"url": "https://github.com/metersphere/metersphere/security/advisories/GHSA-vcm3-5w3f-9f45", "source": "<EMAIL>", "tags": []}]}