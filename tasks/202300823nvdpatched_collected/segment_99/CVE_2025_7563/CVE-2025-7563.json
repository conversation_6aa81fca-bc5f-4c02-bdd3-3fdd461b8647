{"cve_id": "CVE-2025-7563", "published_date": "2025-07-14T03:15:24.360", "last_modified_date": "2025-07-16T14:35:01.203", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Online Fire Reporting System 1.2. Affected by this vulnerability is an unknown functionality of the file /admin/completed-requests.php. The manipulation of the argument teamid leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Online Fire Reporting System 1.2. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/completed-requests.php. La manipulación del argumento teamid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/129", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316261", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316261", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615036", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}