{"cve_id": "CVE-2025-7522", "published_date": "2025-07-13T07:15:22.133", "last_modified_date": "2025-07-15T20:15:51.977", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Vehicle Parking Management System 1.13 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /admin/bwdates-reports-details.php. The manipulation of the argument fromdate/todate leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/bwdates-reports-details.php. La manipulación del argumento fromdate/todate provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/124", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316219", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316219", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610582", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}