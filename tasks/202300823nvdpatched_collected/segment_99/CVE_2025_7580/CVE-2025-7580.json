{"cve_id": "CVE-2025-7580", "published_date": "2025-07-14T07:15:24.587", "last_modified_date": "2025-07-16T14:34:48.500", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in code-projects Voting System 1.0. Affected by this vulnerability is an unknown functionality of the file /admin/positions_row.php. The manipulation of the argument ID leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad crítica en code-projects Voting System 1.0. Esta vulnerabilidad afecta una funcionalidad desconocida del archivo /admin/positions_row.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/i-Corner/cve/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316279", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316279", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615028", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}