{"cve_id": "CVE-2025-7625", "published_date": "2025-07-14T17:15:39.017", "last_modified_date": "2025-07-15T20:16:00.813", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in YiJiuSmile kkFileViewOfficeEdit up to 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd. Affected is the function Download of the file /download. The manipulation of the argument url leads to path traversal. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Continious delivery with rolling releases is used by this product. Therefore, no version details of affected nor updated releases are available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en YiJiuSmile kkFileViewOfficeEdit hasta 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd. La función \"Descargar\" del archivo /download se ve afectada. La manipulación del argumento URL provoca un path traversal. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza un sistema de entrega continua con versiones continuas. Por lo tanto, no se dispone de detalles de las versiones afectadas ni de versiones actualizadas."}], "references": [{"url": "https://github.com/YiJiuSmile/kkFileViewOfficeEdit/issues/12", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316326", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316326", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609076", "source": "<EMAIL>", "tags": []}]}