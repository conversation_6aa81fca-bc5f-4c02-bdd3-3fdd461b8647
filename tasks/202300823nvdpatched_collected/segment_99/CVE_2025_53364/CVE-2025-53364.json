{"cve_id": "CVE-2025-53364", "published_date": "2025-07-10T16:15:24.777", "last_modified_date": "2025-07-15T13:24:41.097", "descriptions": [{"lang": "en", "value": "Parse Server is an open source backend that can be deployed to any infrastructure that can run Node.js. Starting in 5.3.0 and before 7.5.3 and 8.2.2, the Parse Server GraphQL API previously allowed public access to the GraphQL schema without requiring a session token or the master key. While schema introspection reveals only metadata and not actual data, this metadata can still expand the potential attack surface. This vulnerability is fixed in 7.5.3 and 8.2.2."}, {"lang": "es", "value": "Parse Server es un backend de código abierto que puede implementarse en cualquier infraestructura que ejecute Node.js. A partir de la versión 5.3.0 y anteriores a las versiones 7.5.3 y 8.2.2, la API GraphQL de Parse Server permitía el acceso público al esquema GraphQL sin necesidad de un token de sesión ni de la clave maestra. Si bien la introspección del esquema solo revela metadatos y no datos reales, estos metadatos pueden ampliar la superficie de ataque potencial. Esta vulnerabilidad se ha corregido en las versiones 7.5.3 y 8.2.2."}], "references": [{"url": "https://github.com/parse-community/parse-server/pull/9819", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/pull/9820", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/parse-community/parse-server/security/advisories/GHSA-48q3-prgv-gm4w", "source": "<EMAIL>", "tags": []}]}