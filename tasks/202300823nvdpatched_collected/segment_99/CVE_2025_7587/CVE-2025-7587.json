{"cve_id": "CVE-2025-7587", "published_date": "2025-07-14T09:15:23.823", "last_modified_date": "2025-07-16T16:59:36.273", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Online Appointment Booking System 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /cover.php. The manipulation of the argument uname/psw leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Online Appointment Booking System 1.0. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /cover.php. La manipulación del argumento uname/psw provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/BalanceLee/CVE/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316286", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316286", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615212", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615377", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}