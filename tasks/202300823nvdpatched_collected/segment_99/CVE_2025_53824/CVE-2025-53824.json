{"cve_id": "CVE-2025-53824", "published_date": "2025-07-14T23:15:24.427", "last_modified_date": "2025-07-15T20:15:50.440", "descriptions": [{"lang": "en", "value": "WeGIA is an open source web manager with a focus on the Portuguese language and charitable institutions. A Reflected Cross-Site Scripting (XSS) vulnerability was identified in the editar_permissoes.php endpoint of the WeGIA application prior to version 3.4.4. This vulnerability allows attackers to inject malicious scripts in the msg_c parameter. Version 3.4.4 fixes the issue."}, {"lang": "es", "value": "WeGIA es un gestor web de código abierto centrado en el idioma portugués y las instituciones benéficas. Se identificó una vulnerabilidad de Cross-Site Scripting (XSS) Reflejado en el endpoint editar_permissoes.php de la aplicación WeGIA antes de la versión 3.4.4. Esta vulnerabilidad permite a los atacantes inyectar scripts maliciosos en el parámetro msg_c. La versión 3.4.4 corrige el problema."}], "references": [{"url": "https://github.com/LabRedesCefetRJ/WeGIA/security/advisories/GHSA-86r7-gc8h-63gh", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}