{"cve_id": "CVE-2025-7597", "published_date": "2025-07-14T11:15:23.410", "last_modified_date": "2025-07-15T18:27:31.957", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in Tenda AX1803 *******. Affected is the function formSetMacFilterCfg of the file /goform/setMacFilterCfg. The manipulation of the argument deviceList leads to stack-based buffer overflow. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en Tenda AX1803 *******. La función formSetMacFilterCfg del archivo /goform/setMacFilterCfg está afectada. La manipulación del argumento deviceList provoca un desbordamiento del búfer en la pila. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/panda666-888/vuls/blob/main/tenda/ax1803/formSetMacFilterCfg.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/panda666-888/vuls/blob/main/tenda/ax1803/formSetMacFilterCfg.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316296", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316296", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615268", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}