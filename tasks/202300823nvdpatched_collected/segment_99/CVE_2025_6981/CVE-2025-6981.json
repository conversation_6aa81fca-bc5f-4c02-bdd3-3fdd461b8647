{"cve_id": "CVE-2025-6981", "published_date": "2025-07-15T21:15:34.630", "last_modified_date": "2025-07-16T14:58:59.837", "descriptions": [{"lang": "en", "value": "An incorrect authorization vulnerability allowed unauthorized read access to the contents of internal repositories for contractor accounts when the Contractors API feature was enabled. The Contractors API is a rarely-enabled feature in private preview. This vulnerability affected all versions of GitHub Enterprise Server prior to 3.18 and was fixed in versions 3.14.15, 3.15.10, 3.16.6 and 3.17.3"}, {"lang": "es", "value": "Una vulnerabilidad de autorización incorrecta permitía el acceso de lectura no autorizado al contenido de los repositorios internos de las cuentas de contratistas cuando la API de Contratistas estaba habilitada. Esta API es una función que rara vez se habilita en la vista previa privada. Esta vulnerabilidad afectó a todas las versiones de GitHub Enterprise Server anteriores a la 3.18 y se corrigió en las versiones 3.14.15, 3.15.10, 3.16.6 y 3.17.3."}], "references": [{"url": "https://docs.github.com/en/enterprise-server@3.14/admin/release-notes#3.14.15", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.github.com/en/enterprise-server@3.15/admin/release-notes#3.15.10", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.github.com/en/enterprise-server@3.16/admin/release-notes#3.16.6", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.github.com/en/enterprise-server@3.17/admin/release-notes#3.17.3", "source": "<EMAIL>", "tags": []}]}