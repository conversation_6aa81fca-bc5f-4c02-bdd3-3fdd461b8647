{"cve_id": "CVE-2025-53515", "published_date": "2025-07-11T00:15:28.547", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A vulnerability exists in Advantech iView that allows for SQL injection \nand remote code execution through NetworkServlet.archiveTrap(). This \nissue requires an authenticated attacker with at least user-level \nprivileges. Certain input parameters are not sanitized, allowing an \nattacker to perform SQL injection and potentially execute code in the \ncontext of the 'nt authority\\local service' account."}, {"lang": "es", "value": "Existe una vulnerabilidad en Advantech iView que permite la inyección SQL y la ejecución remota de código mediante NetworkServlet.archiveTrap(). Este problema requiere un atacante autenticado con al menos privilegios de usuario. Ciertos parámetros de entrada no se sanean, lo que permite a un atacante realizar una inyección SQL y potencialmente ejecutar código en el contexto de la cuenta 'nt authority\\local service'."}], "references": [{"url": "https://www.advantech.com/en/support/details/firmware-?id=1-HIPU-183", "source": "<EMAIL>", "tags": []}, {"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-25-191-08", "source": "<EMAIL>", "tags": []}]}