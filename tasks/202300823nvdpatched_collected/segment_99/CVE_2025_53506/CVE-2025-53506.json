{"cve_id": "CVE-2025-53506", "published_date": "2025-07-10T20:15:26.970", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Uncontrolled Resource Consumption vulnerability in Apache Tomcat if an HTTP/2 client did not acknowledge the initial settings frame that reduces the maximum permitted concurrent streams.\n\nThis issue affects Apache Tomcat: from 11.0.0-M1 through 11.0.8, from 10.1.0-M1 through 10.1.42, from 9.0.0.M1 through 9.0.106.\n\nUsers are recommended to upgrade to version 11.0.9, 10.1.43 or 9.0.107, which fix the issue."}, {"lang": "es", "value": "Vulnerabilidad de consumo incontrolado de recursos en Apache Tomcat si un cliente HTTP/2 no reconoce el marco de configuración inicial que reduce el máximo de transmisiones simultáneas permitidas. Este problema afecta a Apache Tomcat: de 11.0.0-M1 a 11.0.8, de 10.1.0-M1 a 10.1.42, y de 9.0.0.M1 a 9.0.106. Se recomienda actualizar a las versiones 11.0.9, 10.1.43 o 9.0.107, que solucionan el problema."}], "references": [{"url": "https://lists.apache.org/thread/p09775q0rd185m6zz98krg0fp45j8kr0", "source": "<EMAIL>", "tags": []}]}