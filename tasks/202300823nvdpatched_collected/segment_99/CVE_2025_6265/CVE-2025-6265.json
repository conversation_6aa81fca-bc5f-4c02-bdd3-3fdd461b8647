{"cve_id": "CVE-2025-6265", "published_date": "2025-07-15T02:15:28.080", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A path traversal vulnerability in the file_upload-cgi CGI program of Zyxel NWA50AX PRO firmware version 7.10(ACGE.2) and earlier could allow an authenticated attacker with administrator privileges to access specific directories and delete files, such as the configuration file, on the affected device."}, {"lang": "es", "value": "Una vulnerabilidad de path traversal en el programa CGI file_upload-cgi del firmware Zyxel NWA50AX PRO versión 7.10 (ACGE.2) y anteriores podría permitir que un atacante autenticado con privilegios de administrador acceda a directorios específicos y elimine archivos, como el archivo de configuración, en el dispositivo afectado."}], "references": [{"url": "https://www.zyxel.com/global/en/support/security-advisories/zyxel-security-advisory-for-path-traversal-vulnerability-in-aps-07-15-2025", "source": "<EMAIL>", "tags": []}]}