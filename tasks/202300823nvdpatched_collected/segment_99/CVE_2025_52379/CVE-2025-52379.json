{"cve_id": "CVE-2025-52379", "published_date": "2025-07-15T15:15:25.227", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "Nexxt Solutions NCM-X1800 Mesh Router firmware UV1.2.7 and below contains an authenticated command injection vulnerability in the firmware update feature. The /web/um_fileName_set.cgi and /web/um_web_upgrade.cgi endpoints fail to properly sanitize the upgradeFileName parameter, allowing authenticated attackers to execute arbitrary OS commands on the device, resulting in remote code execution."}, {"lang": "es", "value": "El firmware UV1.2.7 y anteriores del router en malla Nexxt Solutions NCM-X1800 contiene una vulnerabilidad de inyección de comandos autenticados en la función de actualización de firmware. Los endpoints /web/um_fileName_set.cgi y /web/um_web_upgrade.cgi no desinfectan correctamente el parámetro upgradeFileName, lo que permite a atacantes autenticados ejecutar comandos arbitrarios del sistema operativo en el dispositivo, lo que resulta en la ejecución remota de código."}], "references": [{"url": "https://github.com/Vagebondcur/nexxt-solutions-NCM-X1800-exploits", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Vagebondcur/nexxt-solutions-NCM-X1800-exploits/blob/main/CVE-2025-52379/writeup.md", "source": "<EMAIL>", "tags": []}]}