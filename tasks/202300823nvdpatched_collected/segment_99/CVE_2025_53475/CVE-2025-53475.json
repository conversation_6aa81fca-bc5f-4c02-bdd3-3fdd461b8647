{"cve_id": "CVE-2025-53475", "published_date": "2025-07-11T00:15:27.107", "last_modified_date": "2025-07-23T19:19:37.853", "descriptions": [{"lang": "en", "value": "A vulnerability exists in Advantech iView that could allow for SQL \ninjection and remote code execution through \nNetworkServlet.getNextTrapPage(). This issue requires an authenticated \nattacker with at least user-level privileges. Certain parameters in this\n function are not properly sanitized, allowing an attacker to perform \nSQL injection and potentially execute code in the context of the 'nt \nauthority\\local service' account."}, {"lang": "es", "value": "Existe una vulnerabilidad en Advantech iView que podría permitir la inyección SQL y la ejecución remota de código mediante NetworkServlet.getNextTrapPage(). Este problema requiere un atacante autenticado con al menos privilegios de usuario. Ciertos parámetros de esta función no se depuran correctamente, lo que permite a un atacante realizar una inyección SQL y potencialmente ejecutar código en el contexto de la cuenta 'nt authority\\local service'."}], "references": [{"url": "https://www.advantech.com/en/support/details/firmware-?id=1-HIPU-183", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-25-191-08", "source": "<EMAIL>", "tags": ["Third Party Advisory", "US Government Resource"]}]}