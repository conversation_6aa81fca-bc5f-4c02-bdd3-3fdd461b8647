{"cve_id": "CVE-2025-53822", "published_date": "2025-07-14T23:15:24.163", "last_modified_date": "2025-07-15T20:15:50.230", "descriptions": [{"lang": "en", "value": "WeGIA is an open source web manager with a focus on the Portuguese language and charitable institutions. A Reflected Cross-Site Scripting (XSS) vulnerability was identified in the `relatorio_geracao.php` endpoint of the WeGIA application prior to version 3.4.5. This vulnerability allows attackers to inject malicious scripts in the `tipo_relatorio` parameter. Version 3.4.5 has a patch for the issue."}, {"lang": "es", "value": "WeGIA es un gestor web de código abierto centrado en el idioma portugués y las instituciones benéficas. Se identificó una vulnerabilidad de Cross-Site Scripting (XSS) Reflejado en el endpoint `relatorio_geracao.php` de la aplicación WeGIA antes de la versión 3.4.5. Esta vulnerabilidad permite a los atacantes inyectar scripts maliciosos en el parámetro `tipo_relatorio`. La versión 3.4.5 incluye un parche para este problema."}], "references": [{"url": "https://github.com/LabRedesCefetRJ/WeGIA/security/advisories/GHSA-f5xr-4g63-pc9r", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}