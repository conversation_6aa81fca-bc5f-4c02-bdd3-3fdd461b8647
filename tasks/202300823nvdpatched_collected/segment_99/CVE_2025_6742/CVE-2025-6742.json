{"cve_id": "CVE-2025-6742", "published_date": "2025-07-09T06:15:25.220", "last_modified_date": "2025-07-11T21:21:48.027", "descriptions": [{"lang": "en", "value": "The SureForms – Drag and Drop Form Builder for WordPress plugin for WordPress is vulnerable to PHP Object Injection in all versions up to, and including, 1.7.3 via the use of file_exists() in the delete_entry_files() function without restriction on the path provided. This makes it possible for unauthenticated attackers to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present."}, {"lang": "es", "value": "El complemento SureForms – Drag and Drop Form Builder for WordPress para WordPress es vulnerable a la inyección de objetos PHP en todas las versiones hasta la 1.7.3 incluida, mediante el uso de file_exists() en la función delete_entry_files() sin restricción en la ruta proporcionada. Esto permite a atacantes no autenticados inyectar un objeto PHP. No se conoce ninguna cadena POP presente en el software vulnerable, lo que significa que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si una cadena POP está presente a través de un complemento o tema adicional instalado en el sistema objetivo, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código, dependiendo de la cadena POP presente."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3319753%40sureforms&new=3319753%40sureforms&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/sureforms/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1de12d1c-5ac4-4f80-b33d-a689a6916ee0?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}