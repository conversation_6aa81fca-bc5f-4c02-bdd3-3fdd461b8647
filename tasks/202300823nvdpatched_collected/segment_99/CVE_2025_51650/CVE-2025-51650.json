{"cve_id": "CVE-2025-51650", "published_date": "2025-07-14T17:15:33.270", "last_modified_date": "2025-07-15T16:57:46.893", "descriptions": [{"lang": "en", "value": "An arbitrary file upload vulnerability in the component /controller/PicManager.php of FoxCMS v1.2.6 allows attackers to execute arbitrary code via uploading a crafted template file."}, {"lang": "es", "value": "Una vulnerabilidad de carga de archivos arbitrarios en el componente /controller/PicManager.php de FoxCMS v1.2.6 permite a los atacantes ejecutar código arbitrario mediante la carga de un archivo de plantilla manipulado."}], "references": [{"url": "https://github.com/Y4y17/Foxcms/blob/main/Foxcms%20%3C%3D%20v1.2.6%20RCE.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}