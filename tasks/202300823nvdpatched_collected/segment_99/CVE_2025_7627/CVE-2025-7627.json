{"cve_id": "CVE-2025-7627", "published_date": "2025-07-14T18:15:24.127", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability was found in YiJiuSmile kkFileViewOfficeEdit up to 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd and classified as critical. Affected by this issue is the function fileUpload of the file /fileUpload. The manipulation of the argument File leads to unrestricted upload. The attack may be launched remotely. The exploit has been disclosed to the public and may be used. This product is using a rolling release to provide continious delivery. Therefore, no version details for affected nor updated releases are available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en YiJiuSmile kkFileViewOfficeEdit hasta 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd, clasificada como crítica. Este problema afecta a la función fileUpload del archivo /fileUpload. La manipulación del argumento File permite una carga sin restricciones. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza una versión continua para garantizar una entrega continua. Por lo tanto, no se dispone de detalles de las versiones afectadas ni de las versiones actualizadas."}], "references": [{"url": "https://github.com/YiJiuSmile/kkFileViewOfficeEdit/issues/14", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316328", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316328", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609095", "source": "<EMAIL>", "tags": []}]}