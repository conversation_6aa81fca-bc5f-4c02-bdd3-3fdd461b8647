{"cve_id": "CVE-2025-50756", "published_date": "2025-07-14T15:15:24.077", "last_modified_date": "2025-07-16T19:15:26.303", "descriptions": [{"lang": "en", "value": "Wavlink WN535K3 20191010 was found to contain a command injection vulnerability in the set_sys_adm function via the newpass parameter. This vulnerability allows attackers to execute arbitrary commands via a crafted request."}, {"lang": "es", "value": "Se descubrió que Wavlink WN535K3 20191010 contenía una vulnerabilidad de inyección de comandos en la función set_sys_adm mediante el parámetro newpass. Esta vulnerabilidad permite a los atacantes ejecutar comandos arbitrarios mediante una solicitud manipulada."}], "references": [{"url": "https://github.com/Summermu/VulnForIoT/tree/main/Wavlink_WN535K3/set_sys_adm_newpass/readme.md", "source": "<EMAIL>", "tags": []}]}