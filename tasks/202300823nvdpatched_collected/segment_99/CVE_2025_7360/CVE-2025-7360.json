{"cve_id": "CVE-2025-7360", "published_date": "2025-07-15T05:15:30.070", "last_modified_date": "2025-07-16T14:29:35.580", "descriptions": [{"lang": "en", "value": "The HT Contact Form Widget For Elementor Page Builder & Gutenberg Blocks & Form Builder. plugin for WordPress is vulnerable to arbitrary file moving due to insufficient file path validation in the handle_files_upload() function in all versions up to, and including, 2.2.1. This makes it possible for unauthenticated attackers to move arbitrary files on the server, which can easily lead to remote code execution when the right file is moved (such as wp-config.php)."}, {"lang": "es", "value": "El complemento HT Contact Form Widget para Elementor Page Builder y Gutenberg Blocks &amp; Form Builder para WordPress es vulnerable a la transferencia arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función handle_files_upload() en todas las versiones hasta la 2.2.1 incluida. Esto permite que atacantes no autenticados transfieran archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código al transferir el archivo correcto (como wp-config.php)."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3326887/ht-contactform/trunk/admin/Includes/Api/Endpoints/Submission.php?contextall=1&old=3316109&old_path=%2Fht-contactform%2Ftrunk%2Fadmin%2FIncludes%2FApi%2FEndpoints%2FSubmission.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/ht-contactform/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/dd42c83c-c51c-45a5-8ad5-0df2c0cc411d?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}