{"cve_id": "CVE-2025-52954", "published_date": "2025-07-11T15:15:26.270", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A Missing Authorization vulnerability in the internal virtual routing and forwarding (VRF) of Juniper Networks Junos OS Evolved allows a local, low-privileged user to gain root privileges, leading to a system compromise.\n\nAny low-privileged user with the capability to send packets over the internal VRF can execute arbitrary Junos commands and modify the configuration, and thus compromise the system. \n\nThis issue affects Junos OS Evolved: \n\n\n\n  *  All versions before 22.2R3-S7-EVO, \n  *  from 22.4 before 22.4R3-S7-EVO, \n  *  from 23.2 before 23.2R2-S4-EVO, \n  *  from 23.4 before 23.4R2-S5-EVO, \n  *  from 24.2 before 24.2R2-S1-EVO\n  *  from 24.4 before 24.4R1-S2-<PERSON><PERSON>, 24.4R2-EVO."}, {"lang": "es", "value": "Una vulnerabilidad de falta de autorización en el enrutamiento y reenvío virtual (VRF) interno de Juniper Networks Junos OS Evolved permite que un usuario local con pocos privilegios obtenga permisos de root, lo que compromete el sistema. Cualquier usuario con pocos privilegios capaz de enviar paquetes a través del VRF interno puede ejecutar comandos arbitrarios de Junos y modificar la configuración, comprometiendo así el sistema. Este problema afecta a Junos OS Evolved: * Todas las versiones anteriores a 22.2R3-S7-EVO, * desde la versión 22.4 hasta la 22.4R3-S7-EVO, * desde la versión 23.2 hasta la 23.2R2-S4-<PERSON><PERSON>, * desde la versión 23.4 hasta la 23.4R2-S5-EVO, * desde la versión 24.2 hasta la 24.2R2-S1-EVO, * desde la versión 24.4 hasta la 24.4R1-S2-<PERSON><PERSON>, 24.4R2-<PERSON><PERSON>."}], "references": [{"url": "https://supportportal.juniper.net/JSA100060", "source": "<EMAIL>", "tags": []}]}