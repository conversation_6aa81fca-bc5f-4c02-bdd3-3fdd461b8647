{"cve_id": "CVE-2025-7539", "published_date": "2025-07-13T20:15:26.097", "last_modified_date": "2025-07-16T14:54:28.197", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in code-projects Online Appointment Booking System 1.0. This issue affects some unknown processing of the file /getdoctordaybooking.php. The manipulation of the argument cid leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en code-projects Online Appointment Booking System 1.0. Este problema afecta a un procesamiento desconocido del archivo /getdoctordaybooking.php. La manipulación del argumento cid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/bit001020/cve/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316235", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316235", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613689", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}