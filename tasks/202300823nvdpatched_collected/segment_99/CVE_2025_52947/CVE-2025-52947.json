{"cve_id": "CVE-2025-52947", "published_date": "2025-07-11T15:15:25.000", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Improper Handling of Exceptional Conditions vulnerability in route processing of Juniper Networks Junos OS on specific end-of-life (EOL) ACX Series platforms allows an attacker to crash the Forwarding Engine Board (FEB) by flapping an interface, leading to a Denial of Service (DoS).\n\nOn ACX1000, ACX1100, ACX2000, ACX2100, ACX2200, ACX4000, ACX5048, and ACX5096 devices, FEB0 will crash when the primary path port of the L2 circuit IGP (Interior Gateway Protocol) on the local device goes down. This issue is seen only when 'hot-standby' mode is configured for the L2 circuit.\n\nThis issue affects Junos OS on ACX1000, ACX1100, ACX2000, ACX2100, ACX2200, ACX4000, ACX5048, and ACX5096: \n\n\n\n  *  all versions before 21.2R3-S9."}, {"lang": "es", "value": "Una vulnerabilidad de Manejo Inadecuado de Condiciones Excepcionales en el procesamiento de rutas de Juniper Networks Junos OS en plataformas específicas de la serie ACX al final de su vida útil (EOL) permite a un atacante bloquear la placa del motor de reenvío (FEB) mediante la fluctuación de una interfaz, lo que provoca una denegación de servicio (DoS). En los dispositivos ACX1000, ACX1100, ACX2000, ACX2100, ACX2200, ACX4000, ACX5048 y ACX5096, FEB0 se bloquea cuando falla el puerto de ruta principal del protocolo de puerta de enlace interior (IGP) del circuito L2 del dispositivo local. Este problema solo se observa cuando se configura el modo de espera activa para el circuito L2. Este problema afecta a Junos OS en ACX1000, ACX1100, ACX2000, ACX2100, ACX2200, ACX4000, ACX5048 y ACX5096: * todas las versiones anteriores a 21.2R3-S9."}], "references": [{"url": "https://supportportal.juniper.net/JSA100051", "source": "<EMAIL>", "tags": []}]}