{"cve_id": "CVE-2025-7042", "published_date": "2025-07-15T15:15:27.020", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "Use After Free vulnerability exists in the IPT file reading procedure in SOLIDWORKS eDrawings on Release SOLIDWORKS Desktop 2025. This vulnerability could allow an attacker to execute arbitrary code while opening a specially crafted IPT file."}, {"lang": "es", "value": "Existe una vulnerabilidad de Use After Free en el procedimiento de lectura de archivos IPT en SOLIDWORKS eDrawings en la versión SOLIDWORKS Desktop 2025. Esta vulnerabilidad podría permitir a un atacante ejecutar código arbitrario al abrir un archivo IPT especialmente manipulado."}], "references": [{"url": "https://www.3ds.com/trust-center/security/security-advisories/cve-2025-7042", "source": "<EMAIL>", "tags": []}]}