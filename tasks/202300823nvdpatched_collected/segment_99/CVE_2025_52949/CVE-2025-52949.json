{"cve_id": "CVE-2025-52949", "published_date": "2025-07-11T15:15:25.373", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Improper Handling of Length Parameter Inconsistency vulnerability in the routing protocol daemon (rpd) of Juniper Networks Junos OS and Junos OS Evolved allows a logically adjacent BGP peer sending a specifically malformed BGP packet to cause rpd to crash and restart, resulting in a Denial of Service (DoS). Continued receipt and processing of this packet will create a sustained Denial of Service (DoS) condition.\n\nOnly systems configured for Ethernet Virtual Private Networking (EVPN) signaling are vulnerable to this issue. \n\nThis issue affects iBGP and eBGP, and both IPv4 and IPv6 are affected by this vulnerability.This issue affects:\n\nJunos OS: \n\n\n\n  *  all versions before 21.4R3-S11, \n  *  from 22.2 before 22.2R3-S7, \n  *  from 22.4 before 22.4R3-S7, \n  *  from 23.2 before 23.2R2-S4, \n  *  from 23.4 before 23.4R2-S5, \n  *  from 24.2 before 24.2R2-S1, \n  *  from 24.4 before 24.4R1-S3, 24.4R2; \n\n\n\n\nJunos OS Evolved: \n\n\n\n  *  all versions before 22.2R3-S7-EVO, \n  *  from 22.4-EVO before 22.4R3-S7-<PERSON>VO, \n  *  from 23.2-<PERSON>VO before 23.2R2-S4-<PERSON><PERSON>, \n  *  from 23.4-<PERSON>VO before 23.4R2-S5-<PERSON><PERSON>, \n  *  from 24.2-EVO before 24.2R2-S1-<PERSON>VO, \n  *  from 24.4-<PERSON><PERSON> before 24.4R1-S3-EVO, 24.4R2-EVO."}, {"lang": "es", "value": "Una vulnerabilidad de manejo inadecuado de la inconsistencia del parámetro de longitud en el daemon de protocolo de enrutamiento (rpd) de Juniper Networks Junos OS y Junos OS Evolved permite que un par BGP lógicamente adyacente envíe un paquete BGP específicamente malformado y provoque el bloqueo y reinicio del rpd, lo que resulta en una denegación de servicio (DoS). La recepción y el procesamiento continuos de este paquete generarán una denegación de servicio (DoS) sostenida. Solo los sistemas configurados para la señalización de redes privadas virtuales Ethernet (EVPN) son vulnerables a este problema. Este problema afecta a iBGP y eBGP, y tanto IPv4 como IPv6 se ven afectados por esta vulnerabilidad. Este problema afecta a: Junos OS: * todas las versiones anteriores a 21.4R3-S11, * desde 22.2 hasta 22.2R3-S7, * desde 22.4 hasta 22.4R3-S7, * desde 23.2 hasta 23.2R2-S4, * desde 23.4 hasta 23.4R2-S5, * desde 24.2 hasta 24.2R2-S1, * desde 24.4 hasta 24.4R1-S3, 24.4R2; Junos OS Evolved: * todas las versiones anteriores a 22.2R3-S7-EVO, * desde 22.4-EVO hasta 22.4R3-S7-EVO, * desde 23.2-EVO hasta 23.2R2-S4-EVO, * desde 23.4-EVO hasta 23.4R2-S5-EVO, * desde 24.2-EVO hasta 24.2R2-S1-EVO, * desde 24.4-EVO hasta 24.4R1-S3-EVO, 24.4R2-EVO."}], "references": [{"url": "https://supportportal.juniper.net/JSA100053", "source": "<EMAIL>", "tags": []}]}