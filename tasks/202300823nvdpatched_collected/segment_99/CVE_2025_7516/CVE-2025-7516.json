{"cve_id": "CVE-2025-7516", "published_date": "2025-07-13T04:15:51.480", "last_modified_date": "2025-07-15T18:34:22.330", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in code-projects Online Appointment Booking System 1.0. This vulnerability affects unknown code of the file /cancelbookingpatient.php. The manipulation of the argument appointment leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en code-projects Online Appointment Booking System 1.0. Esta vulnerabilidad afecta al código desconocido del archivo /cancelbookingpatient.php. La manipulación del argumento \"citation\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/cccc88/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316198", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316198", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.612926", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}