{"cve_id": "CVE-2025-50125", "published_date": "2025-07-11T11:15:23.750", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A\n\n\n\nCWE-918: Server-Side Request Forgery (SSRF) vulnerability exists that could cause unauthenticated remote\ncode execution when the server is accessed via the network with knowledge of hidden URLs and manipulation\nof host request header."}, {"lang": "es", "value": "Existe una vulnerabilidad CWE-918: Server-Side Request Forgery (SSRF) que podría provocar la ejecución de código remoto no autenticado cuando se accede al servidor a través de la red con conocimiento de URL ocultas y manipulación del encabezado de solicitud del host."}], "references": [{"url": "https://download.schneider-electric.com/files?p_Doc_Ref=SEVD-2025-189-01&p_enDocType=Security+and+Safety+Notice&p_File_Name=SEVD-2025-189-01.pdf", "source": "<EMAIL>", "tags": []}]}