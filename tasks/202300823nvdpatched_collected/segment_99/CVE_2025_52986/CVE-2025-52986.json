{"cve_id": "CVE-2025-52986", "published_date": "2025-07-11T16:15:26.020", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A Missing Release of Memory after Effective Lifetime vulnerability in the routing protocol daemon (rpd) of Juniper Networks Junos OS and Junos OS Evolved allows a local, low privileged user to cause an impact to the availability of the device.\n\nWhen RIB sharding is enabled and a user executes one of several routing related 'show' commands, a certain amount of memory is leaked. When all available memory has been consumed rpd will crash and restart.\n\nThe leak can be monitored with the CLI command:\n\n\n\nshow task memory detail | match task_shard_mgmt_cookie\n\n\n\nwhere the allocated memory in bytes can be seen to continuously increase with each exploitation.\n\n\n\nThis issue affects:\n\nJunos OS:\n\n  *  all versions before 21.2R3-S9,\n  *  21.4 versions before 21.4R3-S11,\n  *  22.2 versions before 22.2R3-S7,\n  *  22.4 versions before 22.4R3-S7,\n  *  23.2 versions before 23.2R2-S4, \n  *  23.4 versions before 23.4R2-S4,\n  *  24.2 versions before 24.2R2,\n  *  24.4 versions before 24.4R1-S2, 24.4R2;\n\n\nJunos OS Evolved:\n\n  *  all versions before 22.2R3-S7-EVO\n  *  22.4-EVO versions before 22.4R3-S7-<PERSON>VO,\n  *  23.2-EVO versions before 23.2R2-S4-<PERSON><PERSON>,\n  *  23.4-EVO versions before 23.4R2-S4-<PERSON><PERSON>,\n  *  24.2-EVO versions before 24.2R2-<PERSON>VO, \n  *  24.4-EVO versions before 24.4R2-EVO."}, {"lang": "es", "value": "Una vulnerabilidad de falta de liberación de memoria tras el tiempo de vida útil efectivo en el daemon del protocolo de enrutamiento (rpd) de Juniper Networks Junos OS y Junos OS Evolved permite que un usuario local con pocos privilegios afecte la disponibilidad del dispositivo. Cuando se habilita la fragmentación RIB y un usuario ejecuta uno de los varios comandos \"show\" relacionados con el enrutamiento, se pierde cierta cantidad de memoria. Cuando se consume toda la memoria disponible, rpd se bloquea y se reinicia. La pérdida se puede monitorizar con el comando de la CLI: show task memory detail | match task_shard_mgmt_cookie, donde se observa que la memoria asignada en bytes aumenta continuamente con cada explotación. Este problema afecta a: Junos OS: * todas las versiones anteriores a 21.2R3-S9, * versiones 21.4 anteriores a 21.4R3-S11, * versiones 22.2 anteriores a 22.2R3-S7, * versiones 22.4 anteriores a 22.4R3-S7, * versiones 23.2 anteriores a 23.2R2-S4, * versiones 23.4 anteriores a 23.4R2-S4, * versiones 24.2 anteriores a 24.2R2, * versiones 24.4 anteriores a 24.4R1-S2, 24.4R2; Junos OS Evolved: * todas las versiones anteriores a 22.2R3-S7-EVO * versiones 22.4-EVO anteriores a 22.4R3-S7-EVO, * versiones 23.2-EVO anteriores a 23.2R2-S4-EVO, * versiones 23.4-EVO anteriores a 23.4R2-S4-EVO, * versiones 24.2-EVO anteriores a 24.2R2-EVO, * versiones 24.4-EVO anteriores a 24.4R2-EVO."}], "references": [{"url": "https://supportportal.juniper.net/JSA100092", "source": "<EMAIL>", "tags": []}]}