{"cve_id": "CVE-2025-52377", "published_date": "2025-07-15T15:15:24.990", "last_modified_date": "2025-07-15T21:15:33.743", "descriptions": [{"lang": "en", "value": "Command injection vulnerability in Nexxt Solutions NCM-X1800 Mesh Router versions UV1.2.7 and below, allowing authenticated attackers to execute arbitrary commands on the device. The vulnerability is present in the web management interface's ping and traceroute functionality, specifically in the /web/um_ping_set.cgi endpoint. The application fails to properly sanitize user input in the `Ping_host_text` parameter before passing it to the underlying system command, allowing attackers to inject and execute arbitrary shell commands as the root user."}, {"lang": "es", "value": "Vulnerabilidad de inyección de comandos en el router de malla NCM-X1800 de Nexxt Solutions, versiones UV1.2.7 y anteriores, que permite a atacantes autenticados ejecutar comandos arbitrarios en el dispositivo. La vulnerabilidad se presenta en las funciones de ping y traceroute de la interfaz de administración web, concretamente en el endpoint /web/um_ping_set.cgi. La aplicación no depura correctamente la entrada del usuario en el parámetro `Ping_host_text` antes de pasarla al comando del sistema subyacente, lo que permite a los atacantes inyectar y ejecutar comandos de shell arbitrarios como usuario root."}], "references": [{"url": "https://github.com/Vagebondcur/nexxt-solutions-NCM-X1800-exploits", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Vagebondcur/nexxt-solutions-NCM-X1800-exploits/blob/main/CVE-2025-52377/writeup.md", "source": "<EMAIL>", "tags": []}]}