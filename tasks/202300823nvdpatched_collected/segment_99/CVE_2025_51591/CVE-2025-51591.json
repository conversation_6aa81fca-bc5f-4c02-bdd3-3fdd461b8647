{"cve_id": "CVE-2025-51591", "published_date": "2025-07-11T14:15:27.347", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A Server-Side Request Forgery (SSRF) in JGM Pandoc v3.6.4 allows attackers to gain access to and compromise the whole infrastructure via injecting a crafted iframe."}, {"lang": "es", "value": "Server-Side Request Forgery (SSRF) en JGM Pandoc v3.6.4 permite a los atacantes obtener acceso y comprometer toda la infraestructura mediante la inyección de un iframe manipulado específicamente."}], "references": [{"url": "http://jgm.com", "source": "<EMAIL>", "tags": []}, {"url": "http://pandoc.com", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RealestName/Vulnerability-Research/tree/main/CVE-2025-51591", "source": "<EMAIL>", "tags": []}]}