{"cve_id": "CVE-2025-53634", "published_date": "2025-07-10T20:15:27.987", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Chall-Manager is a platform-agnostic system able to start Challenges on Demand of a player. The HTTP Gateway processes headers, but with no timeout set. With a slow loris attack, an attacker could cause Denial of Service (DoS). Exploitation does not require authentication nor authorization, so anyone can exploit it. It should nonetheless not be exploitable as it is highly recommended to bury Chall-Manager deep within the infrastructure due to its large capabilities, so no users could reach the system. Patch has been implemented by commit 1385bd8 and shipped in v0.1.4."}, {"lang": "es", "value": "Chall-Manager es un sistema independiente de la plataforma, capaz de iniciar desafíos a petición de un jugador. La puerta de enlace HTTP procesa los encabezados, pero sin tiempo de espera establecido. Con un ataque lento de Loris, un atacante podría causar una denegación de servicio (DoS). Su explotación no requiere autenticación ni autorización, por lo que cualquiera puede hacerlo. Sin embargo, no debería ser explotable, ya que se recomienda encarecidamente integrar Chall-Manager en la infraestructura debido a sus amplias capacidades, de modo que ningún usuario pueda acceder al sistema. El parche se implementó mediante el commit 1385bd8 y se publicó en la versión v0.1.4."}], "references": [{"url": "https://github.com/ctfer-io/chall-manager/commit/1385bd869142651146cd0b123085f91cec698636", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ctfer-io/chall-manager/releases/tag/v0.1.4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ctfer-io/chall-manager/security/advisories/GHSA-ggmv-j932-q89q", "source": "<EMAIL>", "tags": []}]}