{"cve_id": "CVE-2025-7575", "published_date": "2025-07-14T06:15:28.470", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Zavy86 WikiDocs up to 1.0.77 and classified as critical. Affected by this vulnerability is the function image_drop_upload_ajax/image_delete_ajax of the file submit.php. The manipulation leads to path traversal. The attack can be launched remotely. Upgrading to version 1.0.78 is able to address this issue. The identifier of the patch is 98ea9ee4a2052c4327f89d2f7688cc1b5749450d. It is recommended to upgrade the affected component."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en Zavy86 WikiDocs hasta la versión 1.0.77, clasificada como crítica. Esta vulnerabilidad afecta a la función image_drop_upload_ajax/image_delete_ajax del archivo submit.php. La manipulación provoca un path traversal. El ataque puede ejecutarse en remoto. Actualizar a la versión 1.0.78 puede solucionar este problema. El identificador del parche es 98ea9ee4a2052c4327f89d2f7688cc1b5749450d. Se recomienda actualizar el componente afectado."}], "references": [{"url": "https://github.com/Zavy86/WikiDocs/commit/98ea9ee4a2052c4327f89d2f7688cc1b5749450d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Zavy86/WikiDocs/pull/258", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Zavy86/WikiDocs/releases/tag/1.0.78", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316273", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316273", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609096", "source": "<EMAIL>", "tags": []}]}