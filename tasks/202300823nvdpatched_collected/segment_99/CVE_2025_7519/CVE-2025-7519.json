{"cve_id": "CVE-2025-7519", "published_date": "2025-07-14T14:15:25.593", "last_modified_date": "2025-07-25T18:15:27.173", "descriptions": [{"lang": "en", "value": "A flaw was found in polkit. When processing an XML policy with 32 or more nested elements in depth, an out-of-bounds write can be triggered. This issue can lead to a crash or other unexpected behavior, and arbitrary code execution is not discarded. To exploit this flaw, a high-privilege account is needed as it's required to place the malicious policy file properly."}, {"lang": "es", "value": "Se detectó una falla en polkit. Al procesar una política XML con 32 o más elementos anidados en profundidad, se puede activar una escritura fuera de los límites. Este problema puede provocar un bloqueo u otro comportamiento inesperado, y la ejecución de código arbitrario no se descarta. Para explotar esta falla, se requiere una cuenta con privilegios elevados, ya que es necesaria para colocar correctamente el archivo de política maliciosa."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2025-7519", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2379675", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/polkit-org/polkit/commit/107d3801361b9f9084f78710178e683391f1d245", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/polkit-org/polkit/pull/570", "source": "<EMAIL>", "tags": []}]}