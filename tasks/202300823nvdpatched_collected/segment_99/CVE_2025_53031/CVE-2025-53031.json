{"cve_id": "CVE-2025-53031", "published_date": "2025-07-15T20:15:49.317", "last_modified_date": "2025-07-24T20:34:58.503", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle Financial Services Analytical Applications Infrastructure product of Oracle Financial Services Applications (component: Platform).  Supported versions that are affected are *******, *******, *******, ******* and  *******. Easily exploitable vulnerability allows unauthenticated attacker with network access via HTTP to compromise Oracle Financial Services Analytical Applications Infrastructure.  Successful attacks of this vulnerability can result in  unauthorized read access to a subset of Oracle Financial Services Analytical Applications Infrastructure accessible data. CVSS 3.1 Base Score 5.3 (Confidentiality impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en el producto Oracle Financial Services Analytical Applications Infrastructure de Oracle Financial Services Applications (componente: Plataforma). Las versiones compatibles afectadas son *******, *******, *******, ******* y *******. <PERSON><PERSON> vulnerabilidad, fácilmente explotable, permite a un atacante no autenticado con acceso a la red a través de HTTP comprometer Oracle Financial Services Analytical Applications Infrastructure. Los ataques con éxito de esta vulnerabilidad pueden resultar en acceso de lectura no autorizado a un subconjunto de datos accesibles de Oracle Financial Services Analytical Applications Infrastructure. Puntuación base de CVSS 3.1: 5.3 (Afecta a la confidencialidad). Vector CVSS: (CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}