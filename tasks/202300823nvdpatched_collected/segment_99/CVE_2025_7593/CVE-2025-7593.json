{"cve_id": "CVE-2025-7593", "published_date": "2025-07-14T10:15:28.987", "last_modified_date": "2025-07-15T20:15:59.853", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Job Diary 1.0 and classified as critical. Affected by this issue is some unknown functionality of the file /view-all.php. The manipulation of the argument ID leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Job Diary 1.0, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /view-all.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/yihaofuweng/cve/issues/19", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316292", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316292", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615238", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}