{"cve_id": "CVE-2025-6974", "published_date": "2025-07-15T15:15:26.220", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "Use of Uninitialized Variable vulnerability exists in the JT file reading procedure in SOLIDWORKS eDrawings on Release SOLIDWORKS Desktop 2025. This vulnerability could allow an attacker to execute arbitrary code while opening a specially crafted JT file."}, {"lang": "es", "value": "Existe una vulnerabilidad de uso de variable no inicializada en el procedimiento de lectura de archivos JT en SOLIDWORKS eDrawings en la versión SOLIDWORKS Desktop 2025. Esta vulnerabilidad podría permitir que un atacante ejecute código arbitrario al abrir un archivo JT especialmente manipulado."}], "references": [{"url": "https://www.3ds.com/trust-center/security/security-advisories/cve-2025-6974", "source": "<EMAIL>", "tags": []}]}