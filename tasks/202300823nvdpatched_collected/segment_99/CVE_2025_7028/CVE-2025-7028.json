{"cve_id": "CVE-2025-7028", "published_date": "2025-07-11T16:15:27.143", "last_modified_date": "2025-07-15T20:15:51.317", "descriptions": [{"lang": "en", "value": "A vulnerability in the Software SMI handler (SwSmiInputValue 0x20) allows a local attacker to supply a crafted pointer (FuncBlock) through RBX and RCX register values. This pointer is passed unchecked into multiple flash management functions (ReadFlash, WriteFlash, EraseFlash, and GetFlashInfo) that dereference both the structure and its nested members, such as BufAddr. This enables arbitrary read/write access to System Management RAM (SMRAM), allowing an attacker to corrupt firmware memory, exfiltrate SMRAM content via flash, or install persistent implants."}, {"lang": "es", "value": "Una vulnerabilidad en el controlador Software SMI (SwSmiInputValue 0x20) permite a un atacante local proporcionar un puntero manipulado (FuncBlock) a través de los valores de los registros RBX y RCX. Este puntero se pasa sin control a múltiples funciones de gestión de memoria flash (ReadFlash, WriteFlash, EraseFlash y GetFlashInfo) que desreferencian tanto la estructura como sus miembros anidados, como BufAddr. Esto permite acceso arbitrario de lectura/escritura a la RAM de gestión del sistema (SMRAM), lo que permite a un atacante corromper la memoria del firmware, extraer contenido de la SMRAM a través de la memoria flash o instalar implantes persistentes."}], "references": [{"url": "https://kb.cert.org/vuls/id/746790", "source": "<EMAIL>", "tags": []}, {"url": "https://www.binarly.io/advisories/brly-dva-2025-010", "source": "<EMAIL>", "tags": []}, {"url": "https://www.gigabyte.com/Support/Security", "source": "<EMAIL>", "tags": []}]}