{"cve_id": "CVE-2025-7021", "published_date": "2025-07-10T20:15:28.380", "last_modified_date": "2025-07-24T19:13:23.363", "descriptions": [{"lang": "en", "value": "Fullscreen API Spoofing and UI Redressing in the handling of Fullscreen API and UI rendering in OpenAI Operator SaaS on Web allows a remote attacker to capture sensitive user input (e.g., login credentials, email addresses) via displaying a deceptive fullscreen interface with overlaid fake browser controls and a distracting element (like a cookie consent screen) to obscure fullscreen notifications, tricking the user into interacting with the malicious site."}, {"lang": "es", "value": "La suplantación de API de pantalla completa y la reparación de la interfaz de usuario en el manejo de la API de pantalla completa y la representación de la interfaz de usuario en OpenAI Operator SaaS en la Web permite a un atacante remoto capturar información confidencial del usuario (por ejemplo, credenciales de inicio de sesión, direcciones de correo electrónico) al mostrar una interfaz de pantalla completa engañosa con controles de navegador falsos superpuestos y un elemento que distrae (como una pantalla de consentimiento de cookies) para ocultar las notificaciones de pantalla completa, engañando al usuario para que interactúe con el sitio malicioso."}], "references": [{"url": "https://github.com/google/security-research/security/advisories/GHSA-mmgx-755h-wr74", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}