{"cve_id": "CVE-2025-6970", "published_date": "2025-07-09T23:15:24.713", "last_modified_date": "2025-07-11T17:27:31.933", "descriptions": [{"lang": "en", "value": "The Events Manager – Calendar, Bookings, Tickets, and more! plugin for WordPress is vulnerable to time-based SQL Injection via the ‘orderby’ parameter in all versions up to, and including, 7.0.3 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento Events Manager – Calendar, Bookings, Tickets, and more! para WordPress es vulnerable a la inyección SQL basada en tiempo mediante el parámetro \"orderby\" en todas las versiones hasta la 7.0.3 incluida, debido a un escape insuficiente del parámetro proporcionado por el usuario y a la falta de preparación de la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas existentes, las cuales pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/events-manager/tags/7.0.3/classes/em-object.php#L1060", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3321403/events-manager", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/7320c06e-a7a7-4ed0-93cd-e85d74bae73f?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}