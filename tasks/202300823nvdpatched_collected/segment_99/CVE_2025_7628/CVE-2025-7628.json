{"cve_id": "CVE-2025-7628", "published_date": "2025-07-14T18:15:24.313", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability was found in YiJiuSmile kkFileViewOfficeEdit up to 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd. It has been classified as critical. This affects the function deleteFile of the file /deleteFile. The manipulation of the argument fileName leads to path traversal. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en YiJiuSmile kkFileViewOfficeEdit hasta 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd. Se ha clasificado como crítica. Afecta a la función deleteFile del archivo /deleteFile. La manipulación del argumento fileName provoca un path traversal. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza el enfoque de lanzamiento continuo para garantizar una distribución continua. Por lo tanto, no se dispone de información sobre las versiones afectadas ni sobre las actualizadas."}], "references": [{"url": "https://github.com/YiJiuSmile/kkFileViewOfficeEdit/issues/15", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316329", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316329", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609098", "source": "<EMAIL>", "tags": []}]}