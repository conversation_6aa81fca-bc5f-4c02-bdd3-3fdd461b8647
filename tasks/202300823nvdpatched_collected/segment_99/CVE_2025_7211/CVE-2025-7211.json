{"cve_id": "CVE-2025-7211", "published_date": "2025-07-09T03:15:31.197", "last_modified_date": "2025-07-11T16:37:06.833", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects LifeStyle Store 1.0. It has been declared as critical. This vulnerability affects unknown code of the file /cart_add.php. The manipulation of the argument ID leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects LifeStyle Store 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /cart_add.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/18889016001/cve/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315160", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315160", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.607821", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}