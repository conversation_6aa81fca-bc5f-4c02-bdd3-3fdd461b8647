{"cve_id": "CVE-2025-52950", "published_date": "2025-07-11T15:15:25.570", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A Missing Authorization vulnerability in Juniper Networks Security Director allows an unauthenticated network-based attacker to read or tamper with multiple sensitive resources via the web interface.\n\nNumerous endpoints on the Juniper Security Director appliance do not validate authorization and will deliver information to the caller that is outside their authorization level. An attacker can access data that is outside the user's authorization level. The information obtained can be used to gain access to additional information or perpetrate other attacks, impacting downstream managed devices.\n\n\n\nThis issue affects Security Director version 24.4.1."}, {"lang": "es", "value": "Una vulnerabilidad de falta de autorización en Juniper Networks Security Director permite a un atacante no autenticado acceder o manipular múltiples recursos confidenciales a través de la interfaz web. Numerosos endpoints del dispositivo Juniper Security Director no validan la autorización y entregan al usuario información que excede su nivel de autorización. Un atacante puede acceder a datos que exceden el nivel de autorización del usuario. La información obtenida puede utilizarse para obtener acceso a información adicional o perpetrar otros ataques, afectando a los dispositivos administrados en sentido descendente. Este problema afecta a la versión 24.4.1 de Security Director."}], "references": [{"url": "https://supportportal.juniper.net/JSA100054", "source": "<EMAIL>", "tags": []}]}