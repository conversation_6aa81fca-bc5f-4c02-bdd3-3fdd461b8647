{"cve_id": "CVE-2025-7474", "published_date": "2025-07-12T12:15:26.293", "last_modified_date": "2025-07-15T18:08:34.633", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Job Diary 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /search.php. The manipulation of the argument Search leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Job Diary 1.0. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /search.php. La manipulación del argumento \"Search\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/yihaofuweng/cve/issues/18", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316124", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316124", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610135", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}