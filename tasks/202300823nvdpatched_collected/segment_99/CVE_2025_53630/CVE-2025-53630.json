{"cve_id": "CVE-2025-53630", "published_date": "2025-07-10T20:15:27.523", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "llama.cpp is an inference of several LLM models in C/C++. Integer Overflow in the gguf_init_from_file_impl function in ggml/src/gguf.cpp can lead to Heap Out-of-Bounds Read/Write. This vulnerability is fixed in commit 26a48ad699d50b6268900062661bd22f3e792579."}, {"lang": "es", "value": "llama.cpp es una inferencia de varios modelos LLM en C/C++. Un desbordamiento de enteros en la función gguf_init_from_file_impl de ggml/src/gguf.cpp puede provocar lecturas/escrituras fuera de los límites en el montón. Esta vulnerabilidad se corrigió en el commit 26a48ad699d50b6268900062661bd22f3e792579."}], "references": [{"url": "https://github.com/ggml-org/llama.cpp/commit/26a48ad699d50b6268900062661bd22f3e792579", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ggml-org/llama.cpp/security/advisories/GHSA-vgg9-87g3-85w8", "source": "<EMAIL>", "tags": []}]}