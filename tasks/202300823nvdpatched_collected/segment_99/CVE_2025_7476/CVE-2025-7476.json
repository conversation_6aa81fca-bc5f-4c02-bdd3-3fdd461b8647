{"cve_id": "CVE-2025-7476", "published_date": "2025-07-12T14:15:21.297", "last_modified_date": "2025-07-18T19:10:12.320", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in code-projects Simple Car Rental System 1.0. This vulnerability affects unknown code of the file /admin/approve.php. The manipulation of the argument ID leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en code-projects Simple Car Rental System 1.0. Esta vulnerabilidad afecta al código desconocido del archivo /admin/approve.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/y2xsec324/cve/issues/13", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316126", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316126", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610433", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}