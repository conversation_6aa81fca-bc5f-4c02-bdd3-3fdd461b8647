{"cve_id": "CVE-2025-53660", "published_date": "2025-07-09T16:15:25.433", "last_modified_date": "2025-07-18T17:38:13.730", "descriptions": [{"lang": "en", "value": "Jenkins QMetry Test Management Plugin 1.13 and earlier does not mask Qmetry Automation API Keys displayed on the job configuration form, increasing the potential for attackers to observe and capture them."}, {"lang": "es", "value": "Jenkins QMetry Test Management Plugin 1.13 y versiones anteriores no enmascara las claves de API de automatización de Qmetry que se muestran en el formulario de configuración del trabajo, lo que aumenta la posibilidad de que los atacantes las observen y capturen."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3532", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}