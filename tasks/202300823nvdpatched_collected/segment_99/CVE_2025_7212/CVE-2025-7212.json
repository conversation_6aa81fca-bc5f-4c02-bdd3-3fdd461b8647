{"cve_id": "CVE-2025-7212", "published_date": "2025-07-09T03:15:31.380", "last_modified_date": "2025-07-11T18:42:43.287", "descriptions": [{"lang": "en", "value": "A vulnerability was found in itsourcecode Insurance Management System up to 1.0. It has been rated as critical. This issue affects some unknown processing of the file /insertAgent.php. The manipulation of the argument agent_id leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en itsourcecode Insurance Management System hasta la versión 1.0. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /insertAgent.php. La manipulación del argumento agent_id provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/wishoper/CVE/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://itsourcecode.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.315161", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315161", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.607909", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}