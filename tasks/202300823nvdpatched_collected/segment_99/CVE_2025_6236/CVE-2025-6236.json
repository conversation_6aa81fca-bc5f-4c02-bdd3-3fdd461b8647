{"cve_id": "CVE-2025-6236", "published_date": "2025-07-10T06:15:22.350", "last_modified_date": "2025-07-11T18:28:45.253", "descriptions": [{"lang": "en", "value": "The Hostel WordPress plugin before ******* does not sanitise and escape some of its settings, which could allow high privilege users such as admin to perform Stored Cross-Site Scripting attacks even when the unfiltered_html capability is disallowed (for example in multisite setup)."}, {"lang": "es", "value": "El complemento Hostel para WordPress anterior a la versión ******* no depura ni escapa de algunas de sus configuraciones, lo que podría permitir a usuarios con privilegios elevados, como el administrador, realizar ataques de Cross-Site Scripting almacenado incluso cuando la capacidad unfiltered_html no está permitida (por ejemplo, en una configuración de varios sitios)."}], "references": [{"url": "https://wpscan.com/vulnerability/ff4d312b-a4d1-40cd-a555-a0a1b46f9959/", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}]}