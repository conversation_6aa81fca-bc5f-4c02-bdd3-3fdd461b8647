{"cve_id": "CVE-2025-7503", "published_date": "2025-07-11T19:15:24.217", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An OEM IP camera manufactured by Shenzhen Liandian Communication Technology LTD exposes a Telnet service (port 23) with undocumented, default credentials. The Telnet service is enabled by default and is not disclosed or configurable via the device’s web interface or user manual. An attacker with network access can authenticate using default credentials and gain root-level shell access to the device. The affected firmware version is AppFHE1_V1.0.6.0 (Kernel: KerFHE1_PTZ_WIFI_V3.1.1, Hardware: HwFHE1_WF6_PTZ_WIFI_20201218). No official fix or firmware update is available, and the vendor could not be contacted. This vulnerability allows for remote code execution and privilege escalation."}, {"lang": "es", "value": "Una cámara IP OEM fabricada por Shenzhen Liandian Communication Technology LTD expone un servicio Telnet (puerto 23) con credenciales predeterminadas no documentadas. El servicio Telnet está habilitado por defecto y no se divulga ni se puede configurar a través de la interfaz web del dispositivo ni del manual de usuario. Un atacante con acceso a la red puede autenticarse con las credenciales predeterminadas y obtener acceso root al dispositivo. La versión de firmware afectada es AppFHE1_V1.0.6.0 (Kernel: KerFHE1_PTZ_WIFI_V3.1.1, Hardware: HwFHE1_WF6_PTZ_WIFI_20201218). No existe ninguna corrección ni actualización de firmware oficial disponible, y no se pudo contactar con el proveedor. Esta vulnerabilidad permite la ejecución remota de código y la escalada de privilegios."}], "references": [{"url": "https://github.com/AounShAh/Research-on-v380-cctv-ip-camera", "source": "1c6b5737-9389-4011-8117-89fa251edfb2", "tags": []}]}