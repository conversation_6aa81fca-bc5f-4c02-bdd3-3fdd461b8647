{"cve_id": "CVE-2025-7560", "published_date": "2025-07-14T02:15:22.320", "last_modified_date": "2025-07-16T14:35:18.450", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Fire Reporting System 1.2. It has been declared as critical. This vulnerability affects unknown code of the file /admin/workin-progress-requests.php. The manipulation of the argument teamid leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Fire Reporting System 1.2. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/workin-progress-requests.php. La manipulación del argumento teamid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/126", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316258", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316258", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615033", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}