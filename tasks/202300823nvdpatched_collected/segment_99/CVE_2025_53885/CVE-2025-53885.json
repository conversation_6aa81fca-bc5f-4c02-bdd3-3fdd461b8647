{"cve_id": "CVE-2025-53885", "published_date": "2025-07-15T00:15:23.533", "last_modified_date": "2025-07-16T14:18:18.337", "descriptions": [{"lang": "en", "value": "Directus is a real-time API and App dashboard for managing SQL database content. Starting in version 9.0.0 and prior to version 11.9.0, when using Directus Flows to handle CRUD events for users it is possible to log the incoming data to console using the \"Log to Console\" operation and a template string. Malicious admins can log sensitive data from other users when they are created or updated. Version 11.9.0 contains a fix for the issue. As a workaround, avoid logging sensitive data to the console outside the context of development."}, {"lang": "es", "value": "Directus es una API en tiempo real y un panel de control para aplicaciones que permite gestionar el contenido de bases de datos SQL. A partir de la versión 9.0.0 y anteriores a la 11.9.0, al usar flujos de Directus para gestionar eventos CRUD de los usuarios, es posible registrar los datos entrantes en la consola mediante la operación \"Registrar en consola\" y una cadena de plantilla. Administradores malintencionados pueden registrar datos confidenciales de otros usuarios al crearlos o actualizarlos. La versión 11.9.0 incluye una solución para este problema. Como solución alternativa, evite registrar datos confidenciales en la consola fuera del contexto del desarrollo."}], "references": [{"url": "https://github.com/directus/directus/commit/859f664f56fb50401c407b095889cea38ff580e5", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/pull/25355", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/releases/tag/v11.9.0", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/directus/directus/security/advisories/GHSA-x3vm-88hf-gpxp", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}