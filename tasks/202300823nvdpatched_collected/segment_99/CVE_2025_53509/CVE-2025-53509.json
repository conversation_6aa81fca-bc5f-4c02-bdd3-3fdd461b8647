{"cve_id": "CVE-2025-53509", "published_date": "2025-07-11T00:15:28.357", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A vulnerability exists in Advantech iView that allows for argument \ninjection in the NetworkServlet.restoreDatabase(). This issue requires \nan authenticated attacker with at least user-level privileges. An input \nparameter can be used directly in a command without proper sanitization,\n allowing arbitrary arguments to be injected. This can result in \ninformation disclosure, including sensitive database credentials."}, {"lang": "es", "value": "Existe una vulnerabilidad en Advantech iView que permite la inyección de argumentos en NetworkServlet.restoreDatabase(). Este problema requiere un atacante autenticado con al menos privilegios de usuario. Un parámetro de entrada puede usarse directamente en un comando sin la debida limpieza, lo que permite la inyección de argumentos arbitrarios. Esto puede resultar en la divulgación de información, incluyendo credenciales confidenciales de la base de datos."}], "references": [{"url": "https://www.advantech.com/en/support/details/firmware-?id=1-HIPU-183", "source": "<EMAIL>", "tags": []}, {"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-25-191-08", "source": "<EMAIL>", "tags": []}]}