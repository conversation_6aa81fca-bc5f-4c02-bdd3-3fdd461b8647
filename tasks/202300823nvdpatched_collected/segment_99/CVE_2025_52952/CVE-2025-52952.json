{"cve_id": "CVE-2025-52952", "published_date": "2025-07-11T15:15:25.930", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Out-of-bounds Write vulnerability in the connectivity fault management (CFM) daemon of Juniper Networks Junos OS on MX Series with MPC-BUILTIN, MPC1 through MPC9 line cards allows an unauthenticated adjacent attacker to send a malformed packet to the device, leading to an FPC crash and restart, resulting in a Denial of Service (DoS).\n\nContinued receipt and processing of this packet will create a sustained Denial of Service (DoS) condition.\n\nThis issue affects Juniper Networks:\nJunos OS:\n  *  All versions before 22.2R3-S1,\n  *  from 22.4 before 22.4R2.\n\n\nThis feature is not enabled by default."}, {"lang": "es", "value": "Una vulnerabilidad de escritura fuera de los límites en el daemon de gestión de fallos de conectividad (CFM) de Juniper Networks Junos OS en la serie MX con tarjetas de línea MPC-BUILTIN, MPC1 a MPC9, permite que un atacante adyacente no autenticado envíe un paquete malformado al dispositivo, lo que provoca un bloqueo y reinicio de FPC, lo que resulta en una denegación de servicio (DoS). La recepción y el procesamiento continuos de este paquete crearán una condición de denegación de servicio (DoS) sostenida. Este problema afecta a Juniper Networks: Sistema operativo Junos: * Todas las versiones anteriores a 22.2R3-S1, * Desde la versión 22.4 hasta la 22.4R2. Esta función no está habilitada por defecto."}], "references": [{"url": "https://supportportal.juniper.net/JSA100058", "source": "<EMAIL>", "tags": []}, {"url": "https://www.juniper.net/documentation/us/en/software/junos/network-mgmt/topics/topic-map/cfm-configuring.html", "source": "<EMAIL>", "tags": []}]}