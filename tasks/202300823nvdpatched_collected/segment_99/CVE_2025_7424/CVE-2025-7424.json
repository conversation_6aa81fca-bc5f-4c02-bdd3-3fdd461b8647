{"cve_id": "CVE-2025-7424", "published_date": "2025-07-10T14:15:27.573", "last_modified_date": "2025-07-15T13:24:41.097", "descriptions": [{"lang": "en", "value": "A flaw was found in the libxslt library. The same memory field, psvi, is used for both stylesheet and input data, which can lead to type confusion during XML transformations. This vulnerability allows an attacker to crash the application or corrupt memory. In some cases, it may lead to denial of service or unexpected behavior."}, {"lang": "es", "value": "Se encontró una falla en la librería libxslt. El mismo campo de memoria, psvi, se utiliza tanto para la hoja de estilo como para los datos de entrada, lo que puede provocar confusión de tipos durante las transformaciones XML. Esta vulnerabilidad permite a un atacante bloquear la aplicación o corromper la memoria. En algunos casos, puede provocar una denegación de servicio o un comportamiento inesperado."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2025-7424", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2379228", "source": "<EMAIL>", "tags": []}]}