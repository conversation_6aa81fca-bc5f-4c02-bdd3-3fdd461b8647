{"cve_id": "CVE-2025-53656", "published_date": "2025-07-09T16:15:25.037", "last_modified_date": "2025-07-18T17:33:42.757", "descriptions": [{"lang": "en", "value": "Jenkins ReadyAPI Functional Testing Plugin 1.11 and earlier stores SLM License Access Keys, client secrets, and passwords unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins ReadyAPI Functional Testing Plugin 1.11  y versiones anteriores almacenan claves de acceso de licencia SLM, secretos de cliente y contraseñas sin cifrar en archivos job config.xml en el controlador Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador Jenkins pueden verlos."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3556", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}