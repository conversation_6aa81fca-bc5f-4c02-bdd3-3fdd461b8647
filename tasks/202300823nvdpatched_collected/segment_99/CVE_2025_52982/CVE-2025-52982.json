{"cve_id": "CVE-2025-52982", "published_date": "2025-07-11T16:15:25.367", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Improper Resource Shutdown or Release vulnerability in the SIP ALG of Juniper Networks Junos OS on MX Series with MS-MPC allows an unauthenticated, network-based attacker to cause a Denial-of-Service (DoS).\n\nWhen an MX Series device with an MS-MPC is configured with two or more service sets which are both processing SIP calls, a specific sequence of call events will lead to a crash and restart of the MS-MPC.\nThis issue affects Junos OS:\n\n\n\n  *  all versions before 21.2R3-S9,\n  *  21.4 versions from 21.4R1,\n  *  22.2 versions before 22.2R3-S6,\n  *  22.4 versions before 22.4R3-S6.\n\n\n\n\nAs the MS-MPC is EoL after Junos OS 22.4, later versions are not affected.\n\nThis issue does not affect MX-SPC3 or SRX Series devices."}, {"lang": "es", "value": "Una vulnerabilidad de apagado o liberación incorrecta de recursos en la ALG SIP de Juniper Networks Junos OS en la serie MX con MS-MPC permite que un atacante no autenticado basado en la red provoque una denegación de servicio (DoS). Cuando un dispositivo de la serie MX con un MS-MPC se configura con dos o más conjuntos de servicios que procesan llamadas SIP, una secuencia específica de eventos de llamada provocará un bloqueo y reinicio del MS-MPC. Este problema afecta a Junos OS: * todas las versiones anteriores a 21.2R3-S9, * versiones 21.4 a partir de 21.4R1, * versiones 22.2 anteriores a 22.2R3-S6, * versiones 22.4 anteriores a 22.4R3-S6. Dado que el MS-MPC ha alcanzado el fin de su vida útil después de Junos OS 22.4, las versiones posteriores no se ven afectadas. Este problema no afecta a los dispositivos de las series MX-SPC3 ni SRX."}], "references": [{"url": "https://supportportal.juniper.net/JSA100088", "source": "<EMAIL>", "tags": []}]}