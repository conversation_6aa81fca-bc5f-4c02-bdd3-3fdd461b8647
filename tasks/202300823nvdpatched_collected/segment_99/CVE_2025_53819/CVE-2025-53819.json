{"cve_id": "CVE-2025-53819", "published_date": "2025-07-14T21:15:28.120", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "Nix is a package manager for Linux and other Unix systems. Builds with Nix 2.30.0 on macOS were executed with elevated privileges (root), instead of the build users. The fix was applied to Nix 2.30.1. No known workarounds are available."}, {"lang": "es", "value": "Nix es un gestor de paquetes para Linux y otros sistemas Unix. Las compilaciones con Nix 2.30.0 en macOS se ejecutaban con privilegios elevados (root), en lugar de los usuarios de la compilación. La corrección se aplicó a Nix 2.30.1. No se conocen soluciones alternativas."}], "references": [{"url": "https://github.com/NixOS/nix/commit/e2ef2cfcbc83ea01308ee64c38a58707ab23dec3", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/NixOS/nix/pull/13281", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/NixOS/nix/pull/13455", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/NixOS/nix/security/advisories/GHSA-qc7j-jgf3-qmhg", "source": "<EMAIL>", "tags": []}]}