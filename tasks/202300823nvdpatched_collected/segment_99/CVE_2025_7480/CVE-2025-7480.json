{"cve_id": "CVE-2025-7480", "published_date": "2025-07-12T16:15:23.250", "last_modified_date": "2025-07-15T18:07:56.533", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Vehicle Parking Management System 1.13 and classified as critical. Affected by this issue is some unknown functionality of the file /users/signup.php. The manipulation of the argument email leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /users/signup.php. La manipulación del argumento \"email\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/110", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316130", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316130", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610568", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}