{"cve_id": "CVE-2025-7452", "published_date": "2025-07-11T18:15:35.267", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A vulnerability was found in kone-net go-chat up to f9e58d0afa9bbdb31faf25e7739da330692c4c63. It has been declared as critical. This vulnerability affects the function GetFile of the file go-chat/api/v1/file_controller.go of the component Endpoint. The manipulation of the argument fileName leads to path traversal. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. This product is using a rolling release to provide continious delivery. Therefore, no version details for affected nor updated releases are available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en kone-net go-chat hasta f9e58d0afa9bbdb31faf25e7739da330692c4c63. Se ha declarado crítica. Esta vulnerabilidad afecta a la función GetFile del archivo go-chat/api/v1/file_controller.go del componente Endpoint. La manipulación del argumento fileName provoca un path traversal. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza una versión continua para garantizar una distribución continua. Por lo tanto, no se dispone de detalles de las versiones afectadas ni de las versiones actualizadas."}], "references": [{"url": "http://github.com/kone-net/go-chat/issues/14", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kone-net/go-chat/issues/14#issue-3195205637", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316096", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316096", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.607818", "source": "<EMAIL>", "tags": []}]}