{"cve_id": "CVE-2025-7218", "published_date": "2025-07-09T06:15:25.417", "last_modified_date": "2025-07-11T16:27:18.217", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Campcodes Payroll Management System 1.0 and classified as critical. This issue affects some unknown processing of the file /ajax.php?action=delete_position. The manipulation of the argument ID leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Campcodes Payroll Management System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /ajax.php?action=delete_position. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/falling-snow1/vuldb/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.315167", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315167", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608252", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}