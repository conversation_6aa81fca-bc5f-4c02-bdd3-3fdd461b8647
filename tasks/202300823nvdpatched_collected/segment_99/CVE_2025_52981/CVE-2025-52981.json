{"cve_id": "CVE-2025-52981", "published_date": "2025-07-11T16:15:25.213", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Improper Check for Unusual or Exceptional Conditions vulnerability in the flow processing daemon (flowd) of Juniper Networks Junos OS on \n\n\n\n\n\n\n\n\nSRX1600, SRX2300, SRX 4000 Series, and SRX5000 Series with SPC3\n\n\n\nallows an unauthenticated, network-based attacker to cause a Denial-of-Service (DoS).\n\nIf a sequence of specific PIM packets is received, this will cause a flowd crash and restart.\n\n\nThis issue affects Junos OS:\n\n\n\n  *  all versions before 21.2R3-S9,\n  *  21.4 versions before 21.4R3-S11,\n  *  22.2 versions before 22.2R3-S7,\n  *  22.4 versions before 22.4R3-S6,\n  *  23.2 versions before 23.2R2-S4,\n  *  23.4 versions before 23.4R2-S4,\n  *  24.2 versions before 24.2R2.\n\n\n\n\n\n\n\nThis is a similar, but different vulnerability than the issue reported as\n\nCVE-2024-47503, published in JSA88133."}, {"lang": "es", "value": "Una vulnerabilidad de Comprobación Incorrecta de Condiciones Inusuales o Excepcionales en el daemon de procesamiento de flujo (flowd) del sistema operativo Junos de Juniper Networks en SRX1600, SRX2300, SRX 4000 y SRX5000 con SPC3 permite que un atacante no autenticado basado en la red provoque una denegación de servicio (DoS). Si se recibe una secuencia de paquetes PIM específicos, flowd se bloqueará y reiniciará. Este problema afecta a Junos OS: * todas las versiones anteriores a 21.2R3-S9, * versiones 21.4 anteriores a 21.4R3-S11, * versiones 22.2 anteriores a 22.2R3-S7, * versiones 22.4 anteriores a 22.4R3-S6, * versiones 23.2 anteriores a 23.2R2-S4, * versiones 23.4 anteriores a 23.4R2-S4, * versiones 24.2 anteriores a 24.2R2. Se trata de una vulnerabilidad similar, pero diferente, a la reportada como CVE-2024-47503, publicada en JSA88133."}], "references": [{"url": "https://supportportal.juniper.net/JSA100087", "source": "<EMAIL>", "tags": []}]}