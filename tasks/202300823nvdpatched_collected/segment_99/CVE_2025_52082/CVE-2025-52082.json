{"cve_id": "CVE-2025-52082", "published_date": "2025-07-15T16:15:37.487", "last_modified_date": "2025-07-16T14:28:44.173", "descriptions": [{"lang": "en", "value": "In Netgear XR300 V1.0.3.38_10.3.30, a stack-based buffer overflow exists in the HTTPD service through the usb_device.cgi endpoint. The vulnerability occurs when processing POST requests containing the read_access parameter."}, {"lang": "es", "value": "En Netgear XR300 V1.0.3.38_10.3.30, se produce un desbordamiento de búfer basado en pila en el servicio HTTPD a través del endpoint usb_device.cgi. La vulnerabilidad se produce al procesar solicitudes POST que contienen el parámetro read_access."}], "references": [{"url": "https://github.com/lafdrew/IOT/blob/main/Netgear%20XR300/read_access%20of%20usb_device.cgi/buffer%20overflow%20in%20read_access%20of%20usb_device.cgi.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}