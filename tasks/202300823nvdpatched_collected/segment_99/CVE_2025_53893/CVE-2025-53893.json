{"cve_id": "CVE-2025-53893", "published_date": "2025-07-15T18:15:24.287", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename, and edit files. In version 2.38.0, a Denial of Service (DoS) vulnerability exists in the file processing logic when reading a file on endpoint  `Filebrowser-Server-IP:PORT/files/{file-name}` . While the server correctly handles and stores uploaded files, it attempts to load the entire content into memory during read operations without size checks or resource limits. This allows an authenticated user to upload a large file and trigger uncontrolled memory consumption on read, potentially crashing the server and making it unresponsive. As of time of publication, no known patches are available."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. En la versión 2.38.0, existe una vulnerabilidad de denegación de servicio (DoS) en la lógica de procesamiento de archivos al leer un archivo en el endpoint `Filebrowser-Server-IP:PORT/files/{file-name}`. Si bien el servidor gestiona y almacena correctamente los archivos cargados, intenta cargar todo el contenido en memoria durante las operaciones de lectura sin verificar el tamaño ni los límites de recursos. Esto permite que un usuario autenticado cargue un archivo grande y provoque un consumo de memoria descontrolado durante la lectura, lo que podría provocar un bloqueo del servidor y dejarlo inoperante. Al momento de la publicación, no se conocen parches disponibles. "}], "references": [{"url": "https://github.com/filebrowser/filebrowser/issues/5294", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-7xqm-7738-642x", "source": "<EMAIL>", "tags": []}]}