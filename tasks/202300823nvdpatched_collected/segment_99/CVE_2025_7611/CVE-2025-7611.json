{"cve_id": "CVE-2025-7611", "published_date": "2025-07-14T15:15:24.767", "last_modified_date": "2025-07-15T18:08:24.700", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Wedding Reservation 1.0. It has been classified as critical. This affects an unknown part of the file /global.php. The manipulation of the argument lu leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Wedding Reservation 1.0. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /global.php. La manipulación del argumento lu provoca una inyección SQL. Es posible iniciar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/zzb1388/cve/issues/16", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316311", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316311", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615362", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}