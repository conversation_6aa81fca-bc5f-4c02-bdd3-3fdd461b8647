{"cve_id": "CVE-2025-7533", "published_date": "2025-07-13T17:15:23.003", "last_modified_date": "2025-07-16T14:55:30.283", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Job Diary 1.0 and classified as critical. This issue affects some unknown processing of the file /view-details.php. The manipulation of the argument job_id leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Job Diary 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /view-details.php. La manipulación del argumento job_id provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/manbo001/CVE/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316229", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316229", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613045", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}