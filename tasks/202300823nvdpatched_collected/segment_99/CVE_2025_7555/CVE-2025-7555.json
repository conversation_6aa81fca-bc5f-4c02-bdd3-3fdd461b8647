{"cve_id": "CVE-2025-7555", "published_date": "2025-07-14T01:15:22.240", "last_modified_date": "2025-07-16T14:36:33.090", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in code-projects Voting System 1.0. This issue affects some unknown processing of the file /admin/voters_add.php. The manipulation of the argument firstname/lastname leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en code-projects Voting System 1.0. Este problema afecta a un procesamiento desconocido del archivo /admin/voters_add.php. La manipulación del argumento firstname/lastname provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/i-Corner/cve/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316253", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316253", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615021", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}