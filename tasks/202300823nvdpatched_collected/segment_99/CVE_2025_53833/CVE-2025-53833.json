{"cve_id": "CVE-2025-53833", "published_date": "2025-07-14T23:15:24.710", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "LaRecipe is an application that allows users to create documentation with Markdown inside a Laravel app. Versions prior to 2.8.1 are vulnerable to Server-Side Template Injection (SSTI), which could potentially lead to Remote Code Execution (RCE) in vulnerable configurations. Attackers could execute arbitrary commands on the server, access sensitive environment variables, and/or escalate access depending on server configuration. Users are strongly advised to upgrade to version v2.8.1 or later to receive a patch."}, {"lang": "es", "value": "LaRecipe es una aplicación que permite a los usuarios crear documentación con Markdown dentro de una aplicación Laravel. Las versiones anteriores a la 2.8.1 son vulnerables a la inyección de plantillas del lado del servidor (SSTI), lo que podría provocar la ejecución remota de código (RCE) en configuraciones vulnerables. Los atacantes podrían ejecutar comandos arbitrarios en el servidor, acceder a variables de entorno sensibles o escalar el acceso según la configuración del servidor. Se recomienda encarecidamente a los usuarios actualizar a la versión 2.8.1 o posterior para recibir un parche."}], "references": [{"url": "https://github.com/saleem-hadad/larecipe/commit/c1d0d56889655ce5f2645db5acf0e78d5fc3b36b", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/saleem-hadad/larecipe/pull/390", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/saleem-hadad/larecipe/security/advisories/GHSA-jv7x-xhv2-p5v2", "source": "<EMAIL>", "tags": []}]}