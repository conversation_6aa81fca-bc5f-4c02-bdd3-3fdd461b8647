{"cve_id": "CVE-2025-53014", "published_date": "2025-07-14T18:15:23.620", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "ImageMagick is free and open-source software used for editing and manipulating digital images. Versions prior to 7.1.2-0 and 6.9.13-26 have a heap buffer overflow in the `InterpretImageFilename` function. The issue stems from an off-by-one error that causes out-of-bounds memory access when processing format strings containing consecutive percent signs (`%%`). Versions 7.1.2-0 and 6.9.13-26 fix the issue."}, {"lang": "es", "value": "ImageMagick es un software gratuito y de código abierto que se utiliza para editar y manipular imágenes digitales. Las versiones anteriores a la 7.1.2-0 y la 6.9.13-26 presentan un desbordamiento del búfer de pila en la función `InterpretImageFilename`. El problema se debe a un error de error de uno en uno que provoca un acceso a memoria fuera de los límites al procesar cadenas de formato que contienen signos de porcentaje consecutivos (`%%`). Las versiones 7.1.2-0 y 6.9.13-26 solucionan el problema."}], "references": [{"url": "https://github.com/ImageMagick/ImageMagick/security/advisories/GHSA-hm4x-r5hc-794f", "source": "<EMAIL>", "tags": []}]}