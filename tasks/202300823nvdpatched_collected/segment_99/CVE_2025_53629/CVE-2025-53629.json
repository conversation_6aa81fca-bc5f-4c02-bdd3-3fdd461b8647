{"cve_id": "CVE-2025-53629", "published_date": "2025-07-10T20:15:27.370", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "cpp-httplib is a C++11 single-file header-only cross platform HTTP/HTTPS library. Prior to 0.23.0, incoming requests using Transfer-Encoding: chunked in the header can allocate memory arbitrarily in the server, potentially leading to its exhaustion. This vulnerability is fixed in 0.23.0. NOTE: This vulnerability is related to CVE-2025-53628."}, {"lang": "es", "value": "cpp-httplib es una librería HTTP/HTTPS multiplataforma de un solo archivo de encabezado para C++11. Antes de la versión 0.23.0, las solicitudes entrantes que usaban Transfer-Encoding: fragmentadas en el encabezado podían asignar memoria arbitrariamente en el servidor, lo que podría provocar su agotamiento. Esta vulnerabilidad se corrigió en la versión 0.23.0. NOTA: Esta vulnerabilidad está relacionada con CVE-2025-53628."}], "references": [{"url": "https://github.com/yhirose/cpp-httplib/commit/17ba303889b8d4d719be3879a70639ab653efb99", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/yhirose/cpp-httplib/security/advisories/GHSA-j6p8-779x-p5pw", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/yhirose/cpp-httplib/security/advisories/GHSA-qjmq-h3cc-qv6w", "source": "<EMAIL>", "tags": []}]}