{"cve_id": "CVE-2025-7607", "published_date": "2025-07-14T14:15:28.567", "last_modified_date": "2025-07-15T18:17:30.270", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in code-projects Simple Shopping Cart 1.0. This issue affects some unknown processing of the file /Customers/save_order.php. The manipulation of the argument order_price leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en code-projects Simple Shopping Cart 1.0. Este problema afecta a un procesamiento desconocido del archivo /Customers/save_order.php. La manipulación del argumento order_price provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/zzb1388/cve/issues/20", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316307", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316307", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615358", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}