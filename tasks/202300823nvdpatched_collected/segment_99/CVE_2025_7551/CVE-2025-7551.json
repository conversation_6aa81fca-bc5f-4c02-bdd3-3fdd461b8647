{"cve_id": "CVE-2025-7551", "published_date": "2025-07-14T00:15:23.963", "last_modified_date": "2025-07-15T18:32:10.900", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Tenda FH1201 ********(408). It has been declared as critical. Affected by this vulnerability is the function fromPptpUserAdd of the file /goform/PPTPDClient. The manipulation of the argument modino/username leads to stack-based buffer overflow. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Tenda FH1201 ********(408). Se ha declarado crítica. Esta vulnerabilidad afecta a la función fromPptpUserAdd del archivo /goform/PPTPDClient. La manipulación del argumento modino/username provoca un desbordamiento del búfer basado en la pila. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/panda666-888/vuls/blob/main/tenda/fh1201/fromPptpUserAdd_modino.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/panda666-888/vuls/blob/main/tenda/fh1201/fromPptpUserAdd_modino.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316249", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316249", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.614975", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.614976", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}