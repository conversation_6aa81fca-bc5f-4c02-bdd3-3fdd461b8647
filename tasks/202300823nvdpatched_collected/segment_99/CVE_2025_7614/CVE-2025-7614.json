{"cve_id": "CVE-2025-7614", "published_date": "2025-07-14T15:15:25.393", "last_modified_date": "2025-07-16T14:31:32.970", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in TOTOLINK T6 4.1.5cu.748. Affected is the function delDevice of the file /cgi-bin/cstecgi.cgi of the component HTTP POST Request Handler. The manipulation of the argument ipAddr leads to command injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en TOTOLINK T6 4.1.5cu.748. La función delDevice del archivo /cgi-bin/cstecgi.cgi del componente HTTP POST Request Handler se ve afectada. La manipulación del argumento ipAddr provoca la inyección de comandos. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/5.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/5.md#poc", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.316314", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316314", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615368", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}