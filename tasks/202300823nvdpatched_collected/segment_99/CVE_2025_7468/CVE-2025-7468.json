{"cve_id": "CVE-2025-7468", "published_date": "2025-07-12T09:15:25.950", "last_modified_date": "2025-07-15T15:42:26.307", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Tenda FH1201 ******** and classified as critical. This vulnerability affects the function fromSafeUrlFilter of the file /goform/fromSafeUrlFilter of the component HTTP POST Request Handler. The manipulation of the argument page leads to buffer overflow. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en Tenda FH1201 ********, clasificada como crítica. Esta vulnerabilidad afecta a la función fromSafeUrlFilter del archivo /goform/fromSafeUrlFilter del componente HTTP POST Request Handler. La manipulación de la página de argumentos provoca un desbordamiento del búfer. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://candle-throne-f75.notion.site/Tenda-FH1201-fromSafeUrlFilter-229df0aa118580ceb3e4f54d22814c40", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316120", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316120", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610394", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}