{"cve_id": "CVE-2025-53662", "published_date": "2025-07-09T16:15:25.640", "last_modified_date": "2025-07-18T17:31:15.557", "descriptions": [{"lang": "en", "value": "Jenkins IFTTT Build Notifier Plugin 1.2 and earlier stores IFTTT Maker Channel Keys unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins IFTTT Build Notifier Plugin 1.2 y versiones anteriores almacenan claves de canal IFTTT Maker sin cifrar en archivos job config.xml en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador de Jenkins pueden verlas."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3541", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}