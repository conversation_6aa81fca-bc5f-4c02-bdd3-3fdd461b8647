{"cve_id": "CVE-2025-53548", "published_date": "2025-07-09T18:15:24.157", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "Clerk helps developers build user management. Applications that use the verifyWebhook() helper to verify incoming Clerk webhooks are susceptible to accepting improperly signed webhook events. The issue was resolved in @clerk/backend 2.4.0."}, {"lang": "es", "value": "Clerk ayuda a los desarrolladores a crear la gestión de usuarios. Las aplicaciones que usan el asistente verifyWebhook() para verificar los webhooks entrantes de Clerk son susceptibles de aceptar eventos de webhooks firmados incorrectamente. El problema se resolvió en @clerk/backend 2.4.0."}], "references": [{"url": "https://github.com/clerk/javascript/security/advisories/GHSA-9mp4-77wg-rwx9", "source": "<EMAIL>", "tags": []}]}