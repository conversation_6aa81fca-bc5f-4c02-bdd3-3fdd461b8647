{"cve_id": "CVE-2025-53673", "published_date": "2025-07-09T16:15:26.830", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "Jenkins Sensedia Api Platform tools Plugin 1.0 stores the Sensedia API Manager integration token unencrypted in its global configuration file on the Jenkins controller, where it can be viewed by users with access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins Sensedia Api Platform tools Plugin 1.0 almacena el token de integración de Sensedia API Manager sin cifrar en su archivo de configuración global en el controlador de Jenkins, donde lo pueden ver los usuarios con acceso al sistema de archivos del controlador de Jenkins."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3551", "source": "jenkins<PERSON>-<EMAIL>", "tags": []}]}