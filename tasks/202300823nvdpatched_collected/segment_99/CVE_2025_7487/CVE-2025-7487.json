{"cve_id": "CVE-2025-7487", "published_date": "2025-07-12T19:15:26.227", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in JoeyBling SpringBoot_MyBatisPlus up to a6a825513bd688f717dbae3a196bc9c9622fea26. This affects the function SysFileController of the file /file/upload. The manipulation of the argument portraitFile leads to unrestricted upload. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en JoeyBling SpringBoot_MyBatisPlus hasta a6a825513bd688f717dbae3a196bc9c9622fea26. Esta vulnerabilidad afecta a la función SysFileController del archivo /file/upload. La manipulación del argumento portraitFile permite una carga sin restricciones. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza el enfoque de lanzamiento continuo para garantizar una distribución continua. Por lo tanto, no se dispone de información sobre las versiones afectadas ni sobre las actualizadas."}], "references": [{"url": "https://github.com/JoeyBling/SpringBoot_MyBatisPlus/issues/19", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316137", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316137", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609342", "source": "<EMAIL>", "tags": []}]}