{"cve_id": "CVE-2025-53542", "published_date": "2025-07-10T19:15:26.497", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Headlamp is an extensible Kubernetes web UI. A command injection vulnerability was discovered in the codeSign.js script used in the macOS packaging workflow of the Kubernetes Headlamp project. This issue arises due to the improper use of Node.js's execSync() function with unsanitized input derived from environment variables, which can be influenced by an attacker. The variables ${teamID}, ${entitlementsPath}, and ${config.app} are dynamically derived from the environment or application config and passed directly to the shell command without proper escaping or argument separation. This exposes the system to command injection if any of the values contain malicious input. This vulnerability is fixed in 0.31.1."}, {"lang": "es", "value": "Headlamp es una interfaz web extensible de Kubernetes. Se descubrió una vulnerabilidad de inyección de comandos en el script codeSign.js utilizado en el flujo de trabajo de empaquetado de macOS del proyecto Headlamp de Kubernetes. Este problema surge debido al uso indebido de la función execSync() de Node.js con entradas no depuradas derivadas de variables de entorno, que pueden ser influenciadas por un atacante. Las variables ${teamID}, ${entitlementsPath} y ${config.app} se derivan dinámicamente de la configuración del entorno o la aplicación y se pasan directamente al comando de shell sin el escape ni la separación de argumentos adecuados. Esto expone el sistema a la inyección de comandos si alguno de los valores contiene información maliciosa. Esta vulnerabilidad se corrigió en la versión 0.31.1."}], "references": [{"url": "https://advisory.zerodaysec.org/advisory/kubernetes-headlamp-code-signing", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kubernetes-sigs/headlamp/commit/5bc0a9dd87acdf1e04be14619acde687eefa35fb", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kubernetes-sigs/headlamp/pull/3377", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/kubernetes-sigs/headlamp/security/advisories/GHSA-34rf-485x-g5h7", "source": "<EMAIL>", "tags": []}]}