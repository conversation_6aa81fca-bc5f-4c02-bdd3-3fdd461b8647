{"cve_id": "CVE-2025-53890", "published_date": "2025-07-15T00:15:24.150", "last_modified_date": "2025-07-15T20:15:51.080", "descriptions": [{"lang": "en", "value": "pyload is an open-source Download Manager written in pure Python. An unsafe JavaScript evaluation vulnerability in pyLoad’s CAPTCHA processing code allows unauthenticated remote attackers to execute arbitrary code in the client browser and potentially the backend server. Exploitation requires no user interaction or authentication and can result in session hijacking, credential theft, and full system remote code execution. Commit 909e5c97885237530d1264cfceb5555870eb9546, the patch for the issue, is included in version 0.5.0b3.dev89."}, {"lang": "es", "value": "Pyload es un gestor de descargas de código abierto escrito en Python puro. Una vulnerabilidad de evaluación de JavaScript insegura en el código de procesamiento de CAPTCHA de PyLoad permite a atacantes remotos no autenticados ejecutar código arbitrario en el navegador del cliente y, potencialmente, en el servidor backend. Su explotación no requiere interacción ni autenticación del usuario y puede provocar el secuestro de sesión, el robo de credenciales y la ejecución remota de código en todo el sistema. El commit 909e5c97885237530d1264cfceb5555870eb9546, el parche para este problema, está incluido en la versión 0.5.0b3.dev89."}], "references": [{"url": "https://github.com/pyload/pyload/commit/909e5c97885237530d1264cfceb5555870eb9546", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pyload/pyload/pull/4586", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/pyload/pyload/security/advisories/GHSA-8w3f-4r8f-pf53", "source": "<EMAIL>", "tags": []}]}