{"cve_id": "CVE-2025-53633", "published_date": "2025-07-10T20:15:27.823", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Chall-Manager is a platform-agnostic system able to start Challenges on Demand of a player. When decoding a scenario (i.e. a zip archive), the size of the decoded content is not checked, potentially leading to zip bombs decompression. Exploitation does not require authentication nor authorization, so anyone can exploit it. It should nonetheless not be exploitable as it is highly recommended to bury Chall-Manager deep within the infrastructure due to its large capabilities, so no users could reach the system. Patch has been implemented by commit 14042aa and shipped in v0.1.4."}, {"lang": "es", "value": "Chall-Manager es un sistema independiente de la plataforma, capaz de iniciar Desafíos a petición del jugador. Al decodificar un escenario (por ejemplo, un archivo zip), no se verifica el tamaño del contenido decodificado, lo que podría provocar la descompresión de bombas zip. Su explotación no requiere autenticación ni autorización, por lo que cualquiera puede hacerlo. Sin embargo, no debería ser explotable, ya que se recomienda encarecidamente integrar Chall-Manager en la infraestructura debido a sus amplias capacidades, de modo que ningún usuario pueda acceder al sistema. El parche se implementó mediante el commit 14042aa y se publicó en la versión v0.1.4."}], "references": [{"url": "https://github.com/ctfer-io/chall-manager/commit/14042aa66a577caee777e10fe09adcf2587d20dd", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ctfer-io/chall-manager/releases/tag/v0.1.4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ctfer-io/chall-manager/security/advisories/GHSA-r7fm-3pqm-ww5w", "source": "<EMAIL>", "tags": []}]}