{"cve_id": "CVE-2025-7543", "published_date": "2025-07-13T22:15:23.513", "last_modified_date": "2025-07-16T14:37:07.753", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul User Registration & Login and User Management System 3.3. It has been classified as critical. This affects an unknown part of the file /admin/manage-users.php. The manipulation of the argument ID leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul User Registration &amp; Login and User Management System 3.3. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/manage-users.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/4m3rr0r/PoCVulDb/blob/main/CVE-2025-7543.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316239", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316239", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613920", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}