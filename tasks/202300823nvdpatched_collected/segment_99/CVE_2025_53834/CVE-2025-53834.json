{"cve_id": "CVE-2025-53834", "published_date": "2025-07-14T23:15:24.870", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "Caido is a web security auditing toolkit. A reflected cross-site scripting (XSS) vulnerability was discovered in Caido’s toast UI component in versions prior to 0.49.0. Toast messages may reflect unsanitized user input in certain tools such as Match&Replace and Scope. This could allow an attacker to craft input that results in arbitrary script execution. Version 0.49.0 fixes the issue."}, {"lang": "es", "value": "Caido es un kit de herramientas de auditoría de seguridad web. Se descubrió una vulnerabilidad de cross-site scripting (XSS) reflejado en el componente de interfaz de usuario de Caido en versiones anteriores a la 0.49.0. Los mensajes de notificación pueden reflejar entradas de usuario no depuradas en ciertas herramientas como Match&amp;Replace y Scope. Esto podría permitir que un atacante cree entradas que resulten en la ejecución de scripts arbitrarios. La versión 0.49.0 corrige el problema."}], "references": [{"url": "https://github.com/caido/caido/releases/tag/v0.49.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/caido/caido/security/advisories/GHSA-h8jr-c6qq-h7m7", "source": "<EMAIL>", "tags": []}]}