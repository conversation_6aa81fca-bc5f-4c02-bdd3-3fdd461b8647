{"cve_id": "CVE-2025-7059", "published_date": "2025-07-09T04:16:10.170", "last_modified_date": "2025-07-10T13:18:53.830", "descriptions": [{"lang": "en", "value": "The Simple Featured Image plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘slideshow’ parameter in all versions up to, and including, 1.3.1 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Simple Featured Image para WordPress es vulnerable a cross-site scripting almacenado a través del parámetro 'slideshow' en todas las versiones hasta la 1.3.1 inclusive, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/simple-featured-image/trunk/templates/slider.tpl.php#L24", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/simple-featured-image/#developers", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/4d4ecc01-7969-4ff6-8210-530835a43dbc?source=cve", "source": "<EMAIL>", "tags": []}]}