{"cve_id": "CVE-2025-52946", "published_date": "2025-07-11T15:15:24.823", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A Use After Free vulnerability in the routing protocol daemon (rpd) of Juniper Networks Junos OS and Juniper Networks Junos OS Evolved allows an attacker sending a BGP update with a specifically malformed AS PATH to cause rpd to crash, resulting in a Denial of Service (DoS). Continuous receipt of the malformed AS PATH attribute will cause a sustained DoS condition.\n\nOn all Junos OS and Junos OS Evolved platforms, the rpd process will crash and restart when a specifically malformed AS PATH is received within a BGP update and traceoptions are enabled.\n\nThis issue only affects systems with BGP traceoptions enabled and requires a BGP session to be already established.  Systems without BGP traceoptions enabled are not impacted by this issue.\n\n\n\nThis issue affects:\n\n Junos OS:\n\n\n\n  *  All versions before 21.2R3-S9, \n  *  all versions of 21.4,\n  *  from 22.2 before 22.2R3-S6, \n  *  from 22.4 before 22.4R3-S5, \n  *  from 23.2 before 23.2R2-S3, \n  *  from 23.4 before 23.4R2-S4, \n  *  from 24.2 before 24.2R2; \n\n\n\n\nJunos OS Evolved: \n\n\n\n  *  All versions before 22.4R3-S5-EVO, \n  *  from 23.2-EVO before 23.2R2-S3-<PERSON>VO, \n  *  from 23.4-EVO before 23.4R2-S4-<PERSON>VO, \n  *  from 24.2-EVO before 24.2R2-EVO.\n\n\n\n\n\n\n\nThis is a more complete fix for previously published CVE-2024-39549 (JSA83011)."}, {"lang": "es", "value": "Una vulnerabilidad de Use After Free en el daemon del protocolo de enrutamiento (rpd) de Juniper Networks Junos OS y Juniper Networks Junos OS Evolved permite que un atacante que envíe una actualización de BGP con una ruta AS PATH malformada provoque el bloqueo de rpd, lo que resulta en una denegación de servicio (DoS). La recepción continua del atributo AS PATH malformado provocará una denegación de servicio (DoS) sostenida. En todas las plataformas Junos OS y Junos OS Evolved, el proceso rpd se bloquea y se reinicia cuando se recibe una ruta AS PATH malformada en una actualización de BGP y las opciones de seguimiento están habilitadas. Este problema solo afecta a sistemas con opciones de seguimiento de BGP habilitadas y requiere que una sesión de BGP ya esté establecida. Los sistemas sin opciones de seguimiento de BGP habilitadas no se ven afectados. Este problema afecta a: Junos OS: * Todas las versiones anteriores a 21.2R3-S9, * todas las versiones de 21.4, * desde 22.2 hasta 22.2R3-S6, * desde 22.4 hasta 22.4R3-S5, * desde 23.2 hasta 23.2R2-S3, * desde 23.4 hasta 23.4R2-S4, * desde 24.2 hasta 24.2R2; Junos OS Evolved: * Todas las versiones anteriores a 22.4R3-S5-EVO, * desde 23.2-EVO hasta 23.2R2-S3-EVO, * desde 23.4-EVO hasta 23.4R2-S4-EVO, * desde 24.2-EVO hasta 24.2R2-EVO. Esta es una solución más completa para CVE-2024-39549 (JSA83011) publicada anteriormente."}], "references": [{"url": "https://supportportal.juniper.net/JSA100050", "source": "<EMAIL>", "tags": []}]}