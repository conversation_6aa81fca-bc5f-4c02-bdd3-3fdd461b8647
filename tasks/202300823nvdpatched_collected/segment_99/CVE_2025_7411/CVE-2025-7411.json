{"cve_id": "CVE-2025-7411", "published_date": "2025-07-10T19:15:27.533", "last_modified_date": "2025-07-16T15:02:47.523", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects LifeStyle Store 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /success.php. The manipulation of the argument ID leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects LifeStyle Store 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /success.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/rom4j/cve/issues/18", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315871", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315871", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608531", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}