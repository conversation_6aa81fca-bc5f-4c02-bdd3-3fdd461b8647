{"cve_id": "CVE-2025-7517", "published_date": "2025-07-13T05:15:26.473", "last_modified_date": "2025-07-15T18:34:10.630", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in code-projects Online Appointment Booking System 1.0. This issue affects some unknown processing of the file /getDay.php. The manipulation of the argument cidval leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en code-projects Online Appointment Booking System 1.0. Este problema afecta a un procesamiento desconocido del archivo /getDay.php. La manipulación del argumento cidval provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/cccc88/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316199", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316199", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.612927", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}