{"cve_id": "CVE-2025-52994", "published_date": "2025-07-11T15:15:27.167", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "gif_outputAsJpeg in phpThumb through 1.7.23 allows phpthumb.gif.php OS Command Injection via a crafted parameter value. This is fixed in 1.7.23-202506081709."}, {"lang": "es", "value": "gif_outputAsJpeg en phpThumb hasta la versión 1.7.23 permite la inyección de comandos del sistema operativo phpthumb.gif.php mediante un valor de parámetro manipulado. Esto se solucionó en la versión 1.7.23-202506081709."}], "references": [{"url": "https://github.com/JamesHeinrich/phpThumb/commit/cdcbc206ae601b15fd17e7aadf59df51149a0e82", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/JamesHeinrich/phpThumb/releases", "source": "<EMAIL>", "tags": []}, {"url": "https://safety-online.pl/cve-2025-52994/", "source": "<EMAIL>", "tags": []}]}