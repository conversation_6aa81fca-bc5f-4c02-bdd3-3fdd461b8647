{"cve_id": "CVE-2025-6514", "published_date": "2025-07-09T13:15:24.213", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "mcp-remote is exposed to OS command injection when connecting to untrusted MCP servers due to crafted input from the authorization_endpoint response URL"}, {"lang": "es", "value": "mcp-remote está expuesto a la inyección de comandos del sistema operativo cuando se conecta a servidores MCP no confiables debido a una entrada manipulada desde la URL de respuesta de autorización_endpoint"}], "references": [{"url": "https://github.com/geelen/mcp-remote/commit/607b226a356cb61a239ffaba2fb3db1c9dea4bac", "source": "<EMAIL>", "tags": []}, {"url": "https://jfrog.com/blog/2025-6514-critical-mcp-remote-rce-vulnerability", "source": "<EMAIL>", "tags": []}, {"url": "https://research.jfrog.com/vulnerabilities/mcp-remote-command-injection-rce-jfsa-2025-001290844/", "source": "<EMAIL>", "tags": []}]}