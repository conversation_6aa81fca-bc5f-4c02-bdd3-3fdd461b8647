{"cve_id": "CVE-2025-53669", "published_date": "2025-07-09T16:15:26.383", "last_modified_date": "2025-07-18T18:47:13.863", "descriptions": [{"lang": "en", "value": "Jenkins <PERSON>ddy Plugin 1.2.8 and earlier does not mask Vaddy API Auth Keys displayed on the job configuration form, increasing the potential for attackers to observe and capture them."}, {"lang": "es", "value": "Jenkins VAddy Plugin 1.2.8 y versiones anteriores no enmascaran las claves de autenticación de API de Vaddy que se muestran en el formulario de configuración del trabajo, lo que aumenta la posibilidad de que los atacantes las observen y capturen."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3527", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}