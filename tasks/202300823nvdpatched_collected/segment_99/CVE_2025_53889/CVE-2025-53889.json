{"cve_id": "CVE-2025-53889", "published_date": "2025-07-15T00:15:23.997", "last_modified_date": "2025-07-16T14:20:25.787", "descriptions": [{"lang": "en", "value": "Directus is a real-time API and App dashboard for managing SQL database content. Starting in version 9.12.0 and prior to version 11.9.0, Directus Flows with a manual trigger are not validating whether the user triggering the Flow has permissions to the items provided as payload to the Flow. Depending on what the Flow is set up to do this can lead to the Flow executing potential tasks on the attacker's behalf without authenticating. Bad actors could execute the manual trigger Flows without authentication, or access rights to the said collection(s) or item(s). Users with manual trigger Flows configured are impacted as these endpoints do not currently validate if the user has read access to `directus_flows` or to the relevant collection/items. The manual trigger Flows should have tighter security requirements as compared to webhook Flows where users are expected to perform do their own checks. Version 11.9.0 fixes the issue. As a workaround, implement permission checks for read access to Flows and read access to relevant collection/items."}, {"lang": "es", "value": "Directus es una API en tiempo real y un panel de control de aplicaciones para gestionar el contenido de bases de datos SQL. A partir de la versión 9.12.0 y anteriores a la 11.9.0, los flujos de Directus con un disparador manual no validan si el usuario que los activa tiene permisos sobre los elementos proporcionados como payload. Dependiendo de la configuración del flujo, esto puede provocar que ejecute tareas en nombre del atacante sin autenticarse. Los atacantes podrían ejecutar los flujos de activación manual sin autenticación ni derechos de acceso a dichas colecciones o elementos. Los usuarios con flujos de activación manual configurados se ven afectados, ya que estos endpoints no validan actualmente si el usuario tiene acceso de lectura a `directus_flows` o a la colección o los elementos relevantes. Los flujos de activación manual deberían tener requisitos de seguridad más estrictos que los flujos de webhook, donde se espera que los usuarios realicen sus propias comprobaciones. La versión 11.9.0 soluciona el problema. Como solución alternativa, implemente comprobaciones de permisos para el acceso de lectura a los flujos y el acceso de lectura a la colección o los elementos relevantes."}], "references": [{"url": "https://github.com/directus/directus/commit/22be460c76957708d67fdd52846a9ad1cbb083fb", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/releases/tag/v11.9.0", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/directus/directus/security/advisories/GHSA-7cvf-pxgp-42fc", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}