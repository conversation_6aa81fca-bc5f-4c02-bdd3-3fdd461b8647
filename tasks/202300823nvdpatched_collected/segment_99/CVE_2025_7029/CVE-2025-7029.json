{"cve_id": "CVE-2025-7029", "published_date": "2025-07-11T16:15:27.237", "last_modified_date": "2025-07-15T15:15:26.493", "descriptions": [{"lang": "en", "value": "A vulnerability in the Software SMI handler (SwSmiInputValue 0xB2) allows a local attacker to control the RBX register, which is used to derive pointers (OcHeader, OcData) passed into power and thermal configuration logic. These buffers are not validated before performing multiple structured memory writes based on OcSetup NVRAM values, enabling arbitrary SMRAM corruption and potential SMM privilege escalation."}, {"lang": "es", "value": "Una vulnerabilidad en el controlador Software SMI (SwSmiInputValue 0xB2) permite a un atacante local controlar el registro RBX, que se utiliza para derivar punteros (OcHeader, OcData) que se pasan a la lógica de configuración de energía y temperatura. Estos búferes no se validan antes de realizar múltiples escrituras en memoria estructurada basadas en los valores NVRAM de OcSetup, lo que permite la corrupción arbitraria de SMRAM y la posible escalada de privilegios de SMM."}], "references": [{"url": "https://kb.cert.org/vuls/id/746790", "source": "<EMAIL>", "tags": []}, {"url": "https://www.binarly.io/advisories/brly-dva-2025-011", "source": "<EMAIL>", "tags": []}, {"url": "https://www.gigabyte.com/Support/Security", "source": "<EMAIL>", "tags": []}]}