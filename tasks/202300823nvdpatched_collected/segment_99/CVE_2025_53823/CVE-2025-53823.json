{"cve_id": "CVE-2025-53823", "published_date": "2025-07-14T23:15:24.293", "last_modified_date": "2025-07-15T20:15:50.333", "descriptions": [{"lang": "en", "value": "WeGIA is an open source web manager with a focus on the Portuguese language and charitable institutions. Versions prior to 3.4.5 have a SQL Injection vulnerability in the endpoint `/WeGIA/html/socio/sistema/processa_deletar_socio.php`, in the `id_socio` parameter. This vulnerability allows the execution of arbitrary SQL commands, which can compromise the confidentiality, integrity, and availability of stored data. Version 3.4.5 fixes the issue."}, {"lang": "es", "value": "WeGIA es un gestor web de código abierto centrado en el idioma portugués y las instituciones benéficas. Las versiones anteriores a la 3.4.5 presentan una vulnerabilidad de inyección SQL en el endpoint `/WeGIA/html/socio/sistema/processa_deletar_socio.php`, en el parámetro `id_socio`. Esta vulnerabilidad permite la ejecución de comandos SQL arbitrarios, lo que puede comprometer la confidencialidad, integridad y disponibilidad de los datos almacenados. La versión 3.4.5 corrige el problema."}], "references": [{"url": "https://github.com/LabRedesCefetRJ/WeGIA/security/advisories/GHSA-p8xr-qg3c-6ww2", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}