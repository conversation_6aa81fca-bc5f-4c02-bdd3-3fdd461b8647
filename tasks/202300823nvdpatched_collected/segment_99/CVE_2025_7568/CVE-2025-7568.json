{"cve_id": "CVE-2025-7568", "published_date": "2025-07-14T04:15:42.300", "last_modified_date": "2025-07-15T18:31:09.543", "descriptions": [{"lang": "en", "value": "A vulnerability was found in qianfox FoxCMS up to 1.2.5. It has been classified as critical. Affected is the function batchCope of the file app/admin/controller/Video.php. The manipulation of the argument ids leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en qianfox FoxCMS hasta la versión 1.2.5. Se ha clasificado como crítica. La función batchCope del archivo app/admin/controller/Video.php está afectada. La manipulación de los identificadores de los argumentos provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/beauty12345678/CVE/blob/main/foxcms_sql.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316266", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316266", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.603778", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}