{"cve_id": "CVE-2025-50069", "published_date": "2025-07-15T20:15:42.533", "last_modified_date": "2025-07-16T16:15:27.273", "descriptions": [{"lang": "en", "value": "Vulnerability in the Java VM component of Oracle Database Server.  Supported versions that are affected are 19.3-19.27 and  21.3-21.18. Easily exploitable vulnerability allows low privileged attacker having Create Session, Create Procedure privilege with network access via Oracle Net to compromise Java VM.  While the vulnerability is in Java VM, attacks may significantly impact additional products (scope change).  Successful attacks of this vulnerability can result in  unauthorized access to critical data or complete access to all Java VM accessible data. CVSS 3.1 Base Score 7.7 (Confidentiality impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en el componente Java VM de Oracle Database Server. Las versiones compatibles afectadas son 19.3-19.27 y 21.3-21.18. Esta vulnerabilidad, fácilmente explotable, permite a un atacante con privilegios bajos, con privilegios para crear sesión y procedimiento y acceso a la red a través de Oracle Net, comprometer Java VM. Si bien la vulnerabilidad afecta a Java VM, los ataques pueden afectar significativamente a otros productos (cambio de alcance). Los ataques exitosos a esta vulnerabilidad pueden resultar en acceso no autorizado a datos críticos o acceso completo a todos los datos accesibles de Java VM. Puntuación base de CVSS 3.1: 7.7 (Afecta a la confidencialidad). Vector CVSS: (CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:C/C:H/I:N/A:N)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": []}]}