{"cve_id": "CVE-2025-7525", "published_date": "2025-07-13T10:15:21.713", "last_modified_date": "2025-07-15T20:15:52.380", "descriptions": [{"lang": "en", "value": "A vulnerability was found in TOTOLINK T6 4.1.5cu.748_B20211015. It has been declared as critical. This vulnerability affects the function setTracerouteCfg of the file /cgi-bin/cstecgi.cgi of the component HTTP POST Request Handler. The manipulation of the argument command leads to command injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en TOTOLINK T6 4.1.5cu.748_B20211015. Se ha declarado crítica. Esta vulnerabilidad afecta a la función setTracerouteCfg del archivo /cgi-bin/cstecgi.cgi del componente HTTP POST Request Handler. La manipulación del argumento command provoca la inyección de comandos. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/3.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/3.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316222", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316222", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.612936", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://youtu.be/GawLaYfTwYs", "source": "<EMAIL>", "tags": ["Exploit"]}]}