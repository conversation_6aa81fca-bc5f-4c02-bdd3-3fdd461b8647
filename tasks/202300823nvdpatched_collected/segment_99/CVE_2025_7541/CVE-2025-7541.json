{"cve_id": "CVE-2025-7541", "published_date": "2025-07-13T21:15:25.077", "last_modified_date": "2025-07-16T14:37:54.273", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in code-projects Online Appointment Booking System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /get_town.php. The manipulation of the argument countryid leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en code-projects Online Appointment Booking System 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /get_town.php. La manipulación del argumento countryid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/bit001020/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316237", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316237", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613691", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}