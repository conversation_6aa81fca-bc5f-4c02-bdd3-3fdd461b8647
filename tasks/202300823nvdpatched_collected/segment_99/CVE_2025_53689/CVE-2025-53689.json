{"cve_id": "CVE-2025-53689", "published_date": "2025-07-14T10:15:28.587", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "Blind XXE Vulnerabilities in jackrabbit-spi-commons and jackrabbit-core in Apache Jackrabbit < 2.23.2 due to usage of an unsecured document build to load privileges.\n\nUsers are recommended to upgrade to versions 2.20.17 (Java 8), 2.22.1 (Java 11) or 2.23.2 (Java 11, beta versions), which fix this issue. Earlier versions (up to 2.20.16) are not supported anymore, thus users should update to the respective supported version."}, {"lang": "es", "value": "Vulnerabilidades Blind XXE en jackrabbit-spi-commons y jackrabbit-core en Apache Jackrabbit anterior a la versión 2.23.2 debido al uso de una compilación de documento no segura para cargar privilegios. Se recomienda a los usuarios actualizar a las versiones 2.20.17 (Java 8), 2.22.1 (Java 11) o 2.23.2 (Java 11, versiones beta), que solucionan este problema. Las versiones anteriores (hasta la 2.20.16) ya no son compatibles, por lo que los usuarios deben actualizar a la versión compatible correspondiente."}], "references": [{"url": "https://lists.apache.org/thread/5pf9n76ny13pzzk765og2h3gxdxw7p24", "source": "<EMAIL>", "tags": []}]}