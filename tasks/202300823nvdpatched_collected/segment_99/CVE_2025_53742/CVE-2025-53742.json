{"cve_id": "CVE-2025-53742", "published_date": "2025-07-09T16:15:27.440", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "<PERSON> Applitools Eyes Plugin 1.16.5 and earlier stores Applitools API keys unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "<PERSON> Applitools Eyes Plugin 1.16.5 y versiones anteriores almacenan las claves de API de Applitools sin cifrar en archivos job config.xml en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador de Jenkins pueden verlas."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3510", "source": "jenkins<PERSON>-<EMAIL>", "tags": []}]}