{"cve_id": "CVE-2025-7582", "published_date": "2025-07-14T07:15:24.980", "last_modified_date": "2025-07-16T14:34:28.317", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Online Fire Reporting System 1.2. This affects an unknown part of the file /admin/assigned-requests.php. The manipulation of the argument teamid leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Online Fire Reporting System 1.2. Esta afecta a una parte desconocida del archivo /admin/assigned-requests.php. La manipulación del argumento teamid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/130", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316281", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316281", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615037", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}