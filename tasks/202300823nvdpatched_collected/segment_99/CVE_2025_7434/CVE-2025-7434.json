{"cve_id": "CVE-2025-7434", "published_date": "2025-07-11T02:15:23.640", "last_modified_date": "2025-07-16T16:43:09.483", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Tenda FH451 up to ******* and classified as critical. Affected by this issue is the function fromAddressNat of the file /goform/addressNat of the component POST Request Handler. The manipulation of the argument page leads to stack-based buffer overflow. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se detectó una vulnerabilidad en Tenda FH451 hasta la versión *******, clasificada como crítica. Este problema afecta la función fromAddressNat del archivo /goform/addressNat del componente POST Request Handler. La manipulación de la página de argumentos provoca un desbordamiento del búfer en la pila. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/zezhifu1/cve_report/blob/main/FH451/fromAddressNat.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/zezhifu1/cve_report/blob/main/FH451/fromAddressNat.md#payload", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316004", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316004", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.609058", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}