{"cve_id": "CVE-2025-7626", "published_date": "2025-07-14T17:15:39.243", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in YiJiuSmile kkFileViewOfficeEdit up to 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd and classified as critical. Affected by this vulnerability is the function onlinePreview of the file /onlinePreview. The manipulation of the argument url leads to path traversal. The attack can be launched remotely. The exploit has been disclosed to the public and may be used. This product does not use versioning. This is why information about affected and unaffected releases are unavailable."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en YiJiuSmile kkFileViewOfficeEdit hasta 5fbc57c48e8fe6c1b91e0e7995e2d59615f37abd, clasificada como crítica. Esta vulnerabilidad afecta a la función onlinePreview del archivo /onlinePreview. La manipulación del argumento url provoca un path traversal. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto no utiliza control de versiones. Por ello, no hay información disponible sobre las versiones afectadas y no afectadas."}], "references": [{"url": "https://github.com/YiJiuSmile/kkFileViewOfficeEdit/issues/13", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316327", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316327", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609078", "source": "<EMAIL>", "tags": []}]}