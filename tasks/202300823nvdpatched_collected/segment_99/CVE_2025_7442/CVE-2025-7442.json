{"cve_id": "CVE-2025-7442", "published_date": "2025-07-11T08:15:24.590", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The WPGYM - Wordpress Gym Management System plugin for WordPress is vulnerable to SQL Injection via several parameters in the MJ_gmgt_delete_class_limit_for_member, MJ_gmgt_get_yearly_income_expense, MJ_gmgt_get_monthly_income_expense, MJ_gmgt_add_class_limit, MJ_gmgt_view_meeting_detail, and MJ_gmgt_create_meeting functions in all versions up to 67.8.0 due to insufficient escaping on the user supplied parameter and lack of sufficient preparation on the existing SQL query.  This makes it possible for unauthenticated attackers to append additional SQL queries into already existing queries that can be used to extract sensitive information from the database."}, {"lang": "es", "value": "El complemento WPGYM - Wordpress Gym Management System de WordPress es vulnerable a la inyección SQL a través de varios parámetros en las funciones MJ_gmgt_delete_class_limit_for_member, MJ_gmgt_get_yearly_income_expense, MJ_gmgt_get_monthly_income_expense, MJ_gmgt_add_class_limit, MJ_gmgt_view_meeting_detail y MJ_gmgt_create_meeting en todas las versiones hasta la 67.8.0, debido a un escape insuficiente en el parámetro proporcionado por el usuario y a la falta de preparación en la consulta SQL existente. Esto permite a atacantes no autenticados añadir consultas SQL adicionales a las consultas existentes, las cuales pueden utilizarse para extraer información confidencial de la base de datos."}], "references": [{"url": "https://codecanyon.net/item/-wpgym-wordpress-gym-management-system/13352964?s_rank=2", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e52289fe-9a38-4ebf-b24a-034768fa56b7?source=cve", "source": "<EMAIL>", "tags": []}]}