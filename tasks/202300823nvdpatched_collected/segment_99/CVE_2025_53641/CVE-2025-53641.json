{"cve_id": "CVE-2025-53641", "published_date": "2025-07-11T18:15:34.963", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Postiz is an AI social media scheduling tool. From 1.45.1 to 1.62.3, the Postiz frontend application allows an attacker to inject arbitrary HTTP headers into the middleware pipeline. This flaw enables a server-side request forgery (SSRF) condition, which can be exploited to initiate unauthorized outbound requests from the server hosting the Postiz application. This vulnerability is fixed in 1.62.3."}, {"lang": "es", "value": "Postiz es una herramienta de programación de redes sociales con IA. Desde la versión 1.45.1 hasta la 1.62.3, la aplicación frontend de Postiz permite a un atacante inyectar encabezados HTTP arbitrarios en la canalización del middleware. Esta falla habilita una condición de Server-Side Request Forgery (SSRF), que puede explotarse para iniciar solicitudes salientes no autorizadas desde el servidor que aloja la aplicación Postiz. Esta vulnerabilidad se corrigió en la versión 1.62.3."}], "references": [{"url": "https://github.com/gitroomhq/postiz-app/commit/65eca0e2f22155b43c78724ca43617ee52e42753", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/gitroomhq/postiz-app/security/advisories/GHSA-48c8-25jq-m55f", "source": "<EMAIL>", "tags": []}]}