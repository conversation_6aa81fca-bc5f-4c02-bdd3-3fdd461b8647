{"cve_id": "CVE-2025-7410", "published_date": "2025-07-10T17:15:48.980", "last_modified_date": "2025-07-16T15:02:54.863", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects LifeStyle Store 1.0. It has been classified as critical. Affected is an unknown function of the file /cart_remove.php. The manipulation of the argument ID leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects LifeStyle Store 1.0. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /cart_remove.php. La manipulación del ID del argumento provoca una inyección SQL. Es posible ejecutar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/rom4j/cve/issues/19", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315870", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315870", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608524", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}