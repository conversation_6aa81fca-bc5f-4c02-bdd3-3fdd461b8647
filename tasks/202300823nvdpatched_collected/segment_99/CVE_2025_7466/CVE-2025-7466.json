{"cve_id": "CVE-2025-7466", "published_date": "2025-07-12T08:15:24.440", "last_modified_date": "2025-07-15T18:08:53.070", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in 1000projects ABC Courier Management 1.0. Affected by this issue is some unknown functionality of the file /add_dealerrequest.php. The manipulation of the argument Name leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en 1000projects ABC Courier Management 1.0. Este problema afecta a una funcionalidad desconocida del archivo /add_dealerrequest.php. La manipulación del argumento \"Name\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/kensei0x/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316118", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316118", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610390", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}