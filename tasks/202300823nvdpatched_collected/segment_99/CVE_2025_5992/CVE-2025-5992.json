{"cve_id": "CVE-2025-5992", "published_date": "2025-07-11T07:15:25.207", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "When passing values outside of the expected range to QColorTransferGenericFunction it can cause a denial of service, for example, this can happen when passing a specifically crafted ICC profile to QColorSpace::fromICCProfile.This issue affects Qt from 6.6.0 through 6.8.3, from 6.9.0 through 6.9.1. This is fixed in 6.8.4 and 6.9.2."}, {"lang": "es", "value": "Al pasar valores fuera del rango esperado a QColorTransferGenericFunction, se puede producir una denegación de servicio; por ejemplo, esto puede ocurrir al pasar un perfil ICC específicamente manipulado a QColorSpace::fromICCProfile. Este problema afecta a Qt desde la versión 6.6.0 hasta la 6.8.3 y desde la 6.9.0 hasta la 6.9.1. Se ha corregido en las versiones 6.8.4 y 6.9.2."}], "references": [{"url": "https://codereview.qt-project.org/c/qt/qtbase/+/647919", "source": "a59d8014-47c4-4630-ab43-e1b13cbe58e3", "tags": []}]}