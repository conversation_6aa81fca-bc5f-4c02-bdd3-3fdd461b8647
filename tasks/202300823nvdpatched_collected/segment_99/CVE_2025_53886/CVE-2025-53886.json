{"cve_id": "CVE-2025-53886", "published_date": "2025-07-15T00:15:23.690", "last_modified_date": "2025-07-16T14:19:03.560", "descriptions": [{"lang": "en", "value": "Directus is a real-time API and App dashboard for managing SQL database content. Starting in version 9.0.0 and prior to version 11.9.0, when using Directus Flows with the WebHook trigger all incoming request details are logged including security sensitive data like access and refresh tokens in cookies. Malicious admins with access to the logs can hijack the user sessions within the token expiration time of them triggering the Flow. Version 11.9.0 fixes the issue."}, {"lang": "es", "value": "Directus es una API en tiempo real y un panel de control de aplicaciones para gestionar el contenido de bases de datos SQL. A partir de la versión 9.0.0 y anteriores a la 11.9.0, al usar flujos de Directus con el disparador de webhook, se registran todos los detalles de las solicitudes entrantes, incluyendo datos confidenciales como los tokens de acceso y actualización en las cookies. Administradores malintencionados con acceso a los registros pueden secuestrar las sesiones de usuario antes de que caduque el token al activar el flujo. La versión 11.9.0 soluciona este problema."}], "references": [{"url": "https://github.com/directus/directus/commit/22be460c76957708d67fdd52846a9ad1cbb083fb", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/pull/25354", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/releases/tag/v11.9.0", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/directus/directus/security/advisories/GHSA-f24x-rm6g-3w5v", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}