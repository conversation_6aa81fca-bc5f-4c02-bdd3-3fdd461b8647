{"cve_id": "CVE-2025-7520", "published_date": "2025-07-13T05:15:27.527", "last_modified_date": "2025-07-15T20:15:51.707", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Vehicle Parking Management System 1.13. This issue affects some unknown processing of the file /admin/manage-category.php. The manipulation of the argument del leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul Vehicle Parking Management System 1.13. Este problema afecta a un procesamiento desconocido del archivo /admin/manage-category.php. La manipulación del argumento \"del\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/121", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316217", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316217", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610578", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}