{"cve_id": "CVE-2025-6200", "published_date": "2025-07-11T06:15:25.017", "last_modified_date": "2025-07-17T01:04:44.910", "descriptions": [{"lang": "en", "value": "The GeoDirectory  WordPress plugin before 2.8.120 does not validate and escape some of its shortcode attributes before outputting them back in a page/post where the shortcode is embed, which could allow users with the contributor role and above to perform Stored Cross-Site Scripting attacks."}, {"lang": "es", "value": "El complemento GeoDirectory para WordPress anterior a la versión 2.8.120 no valida ni escapa algunos de los atributos de su código corto antes de mostrarlos nuevamente en una página o publicación donde está incrustado el código corto, lo que podría permitir a los usuarios con rol de colaborador o superior realizar ataques de cross-site scripting almacenado."}], "references": [{"url": "https://wpscan.com/vulnerability/27c35255-4963-4d93-85e7-9e7688e5eb2e/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}