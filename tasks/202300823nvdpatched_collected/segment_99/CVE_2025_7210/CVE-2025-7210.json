{"cve_id": "CVE-2025-7210", "published_date": "2025-07-09T02:15:22.460", "last_modified_date": "2025-07-11T17:19:54.390", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects/Fabian R<PERSON> Library Management System 2.0 and classified as critical. Affected by this issue is some unknown functionality of the file admin/profile_update.php. The manipulation of the argument photo leads to unrestricted upload. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects/Fabian Ros Library Management System 2.0, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo admin/profile_update.php. La manipulación del argumento \"photo\" permite la subida sin restricciones. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/horytick/CVE/blob/main/Library%20Management%20System%20In%20PHP%20Arbitrary%20file%20upload.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315158", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315158", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.607801", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}