{"cve_id": "CVE-2025-7599", "published_date": "2025-07-14T12:15:23.697", "last_modified_date": "2025-07-16T14:33:40.477", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Dairy Farm Shop Management System 1.3. Affected by this issue is some unknown functionality of the file /invoice.php. The manipulation of the argument del leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul Dairy Farm Shop Management System 1.3. Este problema afecta a una funcionalidad desconocida del archivo /invoice.php. La manipulación del argumento del provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/139", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316298", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316298", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615233", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}