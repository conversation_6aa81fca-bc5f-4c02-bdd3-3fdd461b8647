{"cve_id": "CVE-2025-7491", "published_date": "2025-07-12T21:15:21.640", "last_modified_date": "2025-07-15T20:15:51.443", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Vehicle Parking Management System 1.13. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /admin/manage-outgoingvehicle.php. The manipulation of the argument del leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/manage-outgoingvehicle.php. La manipulación del argumento \"del\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/119", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316141", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316141", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610576", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}