{"cve_id": "CVE-2025-6234", "published_date": "2025-07-10T06:15:21.897", "last_modified_date": "2025-07-11T18:29:08.090", "descriptions": [{"lang": "en", "value": "The Hostel WordPress plugin before ******* does not sanitise and escape a parameter before outputting it back in the page, leading to a Reflected Cross-Site Scripting which could be used against high privilege users such as admin."}, {"lang": "es", "value": "El complemento Hostel de WordPress anterior a la versión ******* no depura ni escapa un parámetro antes de mostrarlo nuevamente en la página, lo que genera un Cross-Site Scripting reflejado que podría usarse contra usuarios con privilegios elevados, como el administrador."}], "references": [{"url": "https://wpscan.com/vulnerability/7447c4e1-81b9-4415-b425-27491ff692b2/", "source": "<EMAIL>", "tags": ["Third Party Advisory", "Exploit"]}]}