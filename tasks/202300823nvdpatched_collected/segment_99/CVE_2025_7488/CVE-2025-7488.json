{"cve_id": "CVE-2025-7488", "published_date": "2025-07-12T20:15:23.640", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in JoeyBling SpringBoot_MyBatisPlus up to a6a825513bd688f717dbae3a196bc9c9622fea26 and classified as critical. This vulnerability affects the function Download of the file /file/download. The manipulation of the argument Name leads to path traversal. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used. Continious delivery with rolling releases is used by this product. Therefore, no version details of affected nor updated releases are available."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en JoeyBling SpringBoot_MyBatisPlus hasta a6a825513bd688f717dbae3a196bc9c9622fea26, clasificada como crítica. Esta vulnerabilidad afecta la función de descarga del archivo /file/download. La manipulación del argumento \"Name\" provoca un path traversal. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza un sistema de entrega continua con versiones continuas. Por lo tanto, no se dispone de detalles de las versiones afectadas ni de las versiones actualizadas."}], "references": [{"url": "https://github.com/JoeyBling/SpringBoot_MyBatisPlus/issues/18", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316138", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316138", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609343", "source": "<EMAIL>", "tags": []}]}