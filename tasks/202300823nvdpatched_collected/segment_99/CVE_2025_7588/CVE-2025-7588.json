{"cve_id": "CVE-2025-7588", "published_date": "2025-07-14T09:15:24.020", "last_modified_date": "2025-07-15T18:29:55.417", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Dairy Farm Shop Management System 1.3. This affects an unknown part of the file edit-product.php. The manipulation of the argument productname leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Dairy Farm Shop Management System 1.3. Esta afecta a una parte desconocida del archivo edit-product.php. La manipulación del argumento \"productname\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/134", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316287", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316287", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615228", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}