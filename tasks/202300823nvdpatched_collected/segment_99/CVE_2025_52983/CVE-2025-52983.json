{"cve_id": "CVE-2025-52983", "published_date": "2025-07-11T16:15:25.523", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A UI Discrepancy for Security Feature\n\nvulnerability in the UI of Juniper Networks Junos OS on VM Host systems allows a network-based, unauthenticated attacker to access the device.\n\n\n\nOn VM Host Routing Engines (RE), even if the configured public key for root has been removed, remote users which are in possession of the corresponding private key can still log in as root.\nThis issue affects Junos OS:\n\n\n\n  *  all versions before 22.2R3-S7,\n  *  22.4 versions before 22.4R3-S5,\n  *  23.2 versions before 23.2R2-S3,\n  *  23.4 versions before 23.4R2-S3,\n  *  24.2 versions before 24.2R1-S2, 24.2R2."}, {"lang": "es", "value": "Una vulnerabilidad de discrepancia en la interfaz de usuario (IU) para la función de seguridad en la interfaz de usuario de Juniper Networks Junos OS en sistemas host de máquina virtual permite que un atacante no autenticado acceda al dispositivo. En los motores de enrutamiento (RE) de host de máquina virtual, incluso si se ha eliminado la clave pública configurada para root, los usuarios remotos que poseen la clave privada correspondiente aún pueden iniciar sesión como root. Este problema afecta a Junos OS: * todas las versiones anteriores a 22.2R3-S7, * versiones 22.4 anteriores a 22.4R3-S5, * versiones 23.2 anteriores a 23.2R2-S3, * versiones 23.4 anteriores a 23.4R2-S3, * versiones 24.2 anteriores a 24.2R1-S2 y 24.2R2."}], "references": [{"url": "https://supportportal.juniper.net/JSA100089", "source": "<EMAIL>", "tags": []}, {"url": "https://www.juniper.net/documentation/us/en/software/junos/junos-install-upgrade/topics/topic-map/vm-host-overview.html#id-routing-engines-with-vm-host-support", "source": "<EMAIL>", "tags": []}]}