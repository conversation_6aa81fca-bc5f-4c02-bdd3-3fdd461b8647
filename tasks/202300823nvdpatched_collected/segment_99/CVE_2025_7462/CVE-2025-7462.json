{"cve_id": "CVE-2025-7462", "published_date": "2025-07-12T06:15:21.200", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Artifex GhostPDL up to 3989415a5b8e99b9d1b87cc9902bde9b7cdea145. It has been classified as problematic. This affects the function pdf_ferror of the file devices/vector/gdevpdf.c of the component New Output File Open Error Handler. The manipulation leads to null pointer dereference. It is possible to initiate the attack remotely. The identifier of the patch is 619a106ba4c4abed95110f84d5efcd7aee38c7cb. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Artifex GhostPDL hasta 3989415a5b8e99b9d1b87cc9902bde9b7cdea145. Se ha clasificado como problemática. Afecta a la función pdf_ferror del archivo devices/vector/gdevpdf.c del componente \"New Output File Open Error Handler\". La manipulación provoca la desreferencia de puntero nulo. El ataque puede ejecutarse en remoto. El identificador del parche es 619a106ba4c4abed95110f84d5efcd7aee38c7cb. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://artifex.com/", "source": "<EMAIL>", "tags": []}, {"url": "https://cgit.ghostscript.com/cgi-bin/cgit.cgi/ghostpdl.git/commit/?id=619a106ba4c4", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316113", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316113", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.610173", "source": "<EMAIL>", "tags": []}]}