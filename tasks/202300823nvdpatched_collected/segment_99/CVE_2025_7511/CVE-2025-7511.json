{"cve_id": "CVE-2025-7511", "published_date": "2025-07-13T02:15:20.770", "last_modified_date": "2025-07-15T17:44:14.950", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Chat System 1.0 and classified as critical. This issue affects some unknown processing of the file /user/update_account.php. The manipulation of the argument musername leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Chat System 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /user/update_account.php. La manipulación del argumento musername provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/LamentXU123/cve/blob/main/sql_update_account.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316193", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316193", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.611668", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}