{"cve_id": "CVE-2025-7608", "published_date": "2025-07-14T14:15:28.753", "last_modified_date": "2025-07-15T20:16:00.183", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in code-projects Simple Shopping Cart 1.0. Affected is an unknown function of the file /userlogin.php. The manipulation of the argument user_email leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en code-projects Simple Shopping Cart 1.0. Se ve afectada una función desconocida del archivo /userlogin.php. La manipulación del argumento user_email provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/zzb1388/cve/issues/19", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316308", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316308", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615359", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}