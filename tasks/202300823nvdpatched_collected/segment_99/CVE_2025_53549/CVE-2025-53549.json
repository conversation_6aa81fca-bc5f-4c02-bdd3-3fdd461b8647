{"cve_id": "CVE-2025-53549", "published_date": "2025-07-10T19:15:26.683", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The Matrix Rust SDK is a collection of libraries that make it easier to build Matrix clients in Rust. An SQL injection vulnerability in the EventCache::find_event_with_relations method of matrix-sdk 0.11 and 0.12 allows malicious room members to execute arbitrary SQL commands in Matrix clients that directly pass relation types provided by those room members into this method, when used with the default sqlite-based store backend. Exploitation is unlikely, as no known clients currently use the API in this manner. This vulnerability is fixed in 0.13."}, {"lang": "es", "value": "SDK Matrix de Rust es una colección de librerías que facilita la creación de clientes Matrix en Rust. Una vulnerabilidad de inyección SQL en el método EventCache::find_event_with_relations de matrix-sdk 0.11 y 0.12 permite a miembros maliciosos de la sala ejecutar comandos SQL arbitrarios en clientes Matrix que transfieren directamente los tipos de relación proporcionados por dichos miembros a este método, al usarse con el backend de almacenamiento predeterminado basado en SQLite. Es improbable que se explote, ya que actualmente ningún cliente conocido utiliza la API de esta manera. Esta vulnerabilidad se corrigió en la versión 0.13."}], "references": [{"url": "https://github.com/matrix-org/matrix-rust-sdk/pull/4849", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/matrix-org/matrix-rust-sdk/security/advisories/GHSA-275g-g844-73jh", "source": "<EMAIL>", "tags": []}]}