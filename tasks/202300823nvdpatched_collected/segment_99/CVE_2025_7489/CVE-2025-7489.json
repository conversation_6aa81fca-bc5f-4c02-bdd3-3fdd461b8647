{"cve_id": "CVE-2025-7489", "published_date": "2025-07-12T20:15:23.837", "last_modified_date": "2025-07-15T18:06:26.350", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Vehicle Parking Management System 1.13 and classified as critical. This issue affects some unknown processing of the file /admin/search-vehicle.php. The manipulation of the argument searchdata leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/search-vehicle.php. La manipulación del argumento \"searchdata\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/116", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316139", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316139", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610573", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}