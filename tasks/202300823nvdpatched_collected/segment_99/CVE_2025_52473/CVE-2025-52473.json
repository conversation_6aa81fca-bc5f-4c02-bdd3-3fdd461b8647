{"cve_id": "CVE-2025-52473", "published_date": "2025-07-10T19:15:25.387", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "liboqs is a C-language cryptographic library that provides implementations of post-quantum cryptography algorithms. Multiple secret-dependent branches have been identified in the reference implementation of the HQC key encapsulation mechanism when it is compiled with Clang for optimization levels above -O0 (-O1, -O2, etc). A proof-of-concept local attack exploits this secret-dependent information to recover the entire secret key. This vulnerability is fixed in 0.14.0."}, {"lang": "es", "value": "liboqs es una librería criptográfica en lenguaje C que proporciona implementaciones de algoritmos de criptografía poscuántica. Se han identificado múltiples ramas dependientes de secretos en la implementación de referencia del mecanismo de encapsulación de claves HQC al compilarse con Clang para niveles de optimización superiores a -O0 (-O1, -O2, etc.). Un ataque local de prueba de concepto explota esta información dependiente de secretos para recuperar la clave secreta completa. Esta vulnerabilidad se corrigió en la versión 0.14.0."}], "references": [{"url": "https://github.com/open-quantum-safe/liboqs/commit/4215362acbf69b88fe1777c4c052f154e29f9897", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/open-quantum-safe/liboqs/security/advisories/GHSA-qq3m-rq9v-jfgm", "source": "<EMAIL>", "tags": []}]}