{"cve_id": "CVE-2025-7365", "published_date": "2025-07-10T15:15:30.427", "last_modified_date": "2025-07-15T13:24:41.097", "descriptions": [{"lang": "en", "value": "A flaw was found in Keycloak. When an authenticated attacker attempts to merge accounts with another existing account during an identity provider (IdP) login, the attacker will subsequently be prompted to \"review profile\" information. This vulnerability allows the attacker to modify their email address to match that of a victim's account, triggering a verification email sent to the victim's email address. The attacker's email address is not present in the verification email content, making it a potential phishing opportunity. If the victim clicks the verification link, the attacker can gain access to the victim's account."}, {"lang": "es", "value": "Se encontró una falla en Keycloak. Cuando un atacante autenticado intenta fusionar cuentas con otra existente durante el inicio de sesión de un proveedor de identidad (IdP), se le solicita que revise la información del perfil. Esta vulnerabilidad permite al atacante modificar su dirección de correo electrónico para que coincida con la de la cuenta de la víctima, lo que activa el envío de un correo electrónico de verificación a la dirección de correo electrónico de la víctima. La dirección de correo electrónico del atacante no aparece en el contenido del correo de verificación, lo que lo convierte en una posible oportunidad de phishing. Si la víctima hace clic en el enlace de verificación, el atacante puede acceder a su cuenta."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2025-7365", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2378852", "source": "<EMAIL>", "tags": []}]}