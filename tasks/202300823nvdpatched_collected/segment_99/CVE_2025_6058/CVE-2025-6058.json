{"cve_id": "CVE-2025-6058", "published_date": "2025-07-12T05:15:22.387", "last_modified_date": "2025-07-16T14:57:37.827", "descriptions": [{"lang": "en", "value": "The WPBookit plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the image_upload_handle() function hooked via the 'add_booking_type' route in all versions up to, and including, 1.0.4. This makes it possible for unauthenticated attackers to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento WPBookit para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de validación del tipo de archivo en la función image_upload_handle(), interceptada mediante la ruta 'add_booking_type' en todas las versiones hasta la 1.0.4 incluida. Esto permite que atacantes no autenticados carguen archivos arbitrarios en el servidor del sitio afectado, lo que podría posibilitar la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wpbookit/trunk/core/admin/classes/controllers/class.wpb-booking-type-controller.php#L455", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3314288%40wpbookit&new=3314288%40wpbookit&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/1d779ad1-fdbe-444c-85c5-99146a1a03d8?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}