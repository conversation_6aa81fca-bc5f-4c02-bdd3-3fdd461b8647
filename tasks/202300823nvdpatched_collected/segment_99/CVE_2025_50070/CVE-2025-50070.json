{"cve_id": "CVE-2025-50070", "published_date": "2025-07-15T20:15:42.673", "last_modified_date": "2025-07-25T20:27:23.567", "descriptions": [{"lang": "en", "value": "Vulnerability in the JDBC component of Oracle Database Server.  Supported versions that are affected are 23.4-23.8. Di<PERSON>icult to exploit vulnerability allows low privileged attacker having Authenticated OS User privilege with logon to the infrastructure where JDBC executes to compromise JDBC.  Successful attacks require human interaction from a person other than the attacker and while the vulnerability is in JDBC, attacks may significantly impact additional products (scope change). Successful attacks of this vulnerability can result in  unauthorized access to critical data or complete access to all JDBC accessible data. CVSS 3.1 Base Score 5.3 (Confidentiality impacts).  CVSS Vector: (CVSS:3.1/AV:L/AC:H/PR:L/UI:R/S:C/C:H/I:N/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en el componente JDBC de Oracle Database Server. Las versiones compatibles afectadas son las 23.4-23.8. Esta vulnerabilidad, difícil de explotar, permite que un atacante con privilegios bajos, con privilegios de usuario autenticado del sistema operativo e inicio de sesión en la infraestructura donde se ejecuta JDBC, comprometa JDBC. Los ataques exitosos requieren la interacción humana de una persona distinta al atacante y, si bien la vulnerabilidad se encuentra en JDBC, pueden afectar significativamente a otros productos (cambio de alcance). Los ataques exitosos de esta vulnerabilidad pueden resultar en el acceso no autorizado a datos críticos o en el acceso completo a todos los datos accesibles de JDBC. Puntuación base de CVSS 3.1: 5.3 (Afecta a la confidencialidad). Vector CVSS: (CVSS:3.1/AV:L/AC:H/PR:L/UI:R/S:C/C:H/I:N/A:N). "}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}