{"cve_id": "CVE-2025-7561", "published_date": "2025-07-14T02:15:22.503", "last_modified_date": "2025-07-15T20:15:59.537", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Fire Reporting System 1.2. It has been rated as critical. This issue affects some unknown processing of the file /admin/team-ontheway-requests.php. The manipulation of the argument teamid leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Fire Reporting System 1.2. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/team-ontheway-requests.php. La manipulación del argumento teamid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/127", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316259", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316259", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615034", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}