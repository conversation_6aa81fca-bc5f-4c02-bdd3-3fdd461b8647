{"cve_id": "CVE-2025-7412", "published_date": "2025-07-10T20:15:28.543", "last_modified_date": "2025-07-16T15:02:38.730", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Library System 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /user/student/profile.php. The manipulation of the argument image leads to unrestricted upload. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Library System 1.0. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /user/student/profile.php. La manipulación del argumento \"image\" permite la carga sin restricciones. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/rom4j/cve/issues/17", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315872", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315872", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608532", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}