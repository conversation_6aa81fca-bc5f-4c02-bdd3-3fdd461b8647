{"cve_id": "CVE-2025-7545", "published_date": "2025-07-13T22:15:23.873", "last_modified_date": "2025-07-15T20:15:53.893", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in GNU Binutils 2.45. Affected by this vulnerability is the function copy_section of the file binutils/objcopy.c. The manipulation leads to heap-based buffer overflow. Attacking locally is a requirement. The exploit has been disclosed to the public and may be used. The patch is named 08c3cbe5926e4d355b5cb70bbec2b1eeb40c2944. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en GNU Binutils 2.45. Esta vulnerabilidad afecta a la función copy_section del archivo binutils/objcopy.c. Esta manipulación provoca un desbordamiento del búfer en el montón. Es necesario atacar localmente. Se ha hecho público el exploit y puede que sea utilizado. El parche se llama 08c3cbe5926e4d355b5cb70bbec2b1eeb40c2944. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://sourceware.org/bugzilla/attachment.cgi?id=16117", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=33049", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=33049#c1", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/git/gitweb.cgi?p=binutils-gdb.git;h=08c3cbe5926e4d355b5cb70bbec2b1eeb40c2944", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316243", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316243", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.614355", "source": "<EMAIL>", "tags": []}, {"url": "https://www.gnu.org/", "source": "<EMAIL>", "tags": []}]}