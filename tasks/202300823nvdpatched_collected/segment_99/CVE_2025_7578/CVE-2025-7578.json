{"cve_id": "CVE-2025-7578", "published_date": "2025-07-14T06:15:29.137", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Teledyne FLIR FB-Series O and FLIR FH-Series ID ********. It has been declared as critical. This vulnerability affects the function sendCommand of the file runcmd.sh. The manipulation of the argument cmd leads to command injection. The attack can be initiated remotely. The complexity of an attack is rather high. The exploitation appears to be difficult. The researcher highlights, that \"[a]lthough this functionality is currently disabled due to server CGI configuration errors, it is essentially a 'time bomb' waiting to be activated\". The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Teledyne FLIR FB-Series O y FLIR FH-Series con ID ********. Se ha declarado crítica. Esta vulnerabilidad afecta a la función sendCommand del archivo runcmd.sh. La manipulación del argumento cmd provoca la inyección de comandos. El ataque puede ejecutarse en remoto. Es un ataque de complejidad bastante alta. Su explotación parece difícil. El investigador destaca que «aunque esta funcionalidad está actualmente deshabilitada debido a errores de configuración CGI del servidor, es básicamente una bomba de tiempo a punto de ser activada». Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/waiwai24/0101/blob/main/CVEs/FLIR/Command_Injection_Vulnerability_in_Developer_Backdoor_Page.md", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316276", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316276", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.609551", "source": "<EMAIL>", "tags": []}]}