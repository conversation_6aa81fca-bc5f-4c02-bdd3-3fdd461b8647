{"cve_id": "CVE-2025-7456", "published_date": "2025-07-11T20:15:25.083", "last_modified_date": "2025-07-16T14:59:05.797", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in Campcodes Online Movie Theater Seat Reservation System 1.0. Affected by this issue is some unknown functionality of the file /reserve.php. The manipulation of the argument ID leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en Campcodes Online Movie Theater Seat Reservation System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /reserve.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/N1n3b9S/cve/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316100", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316100", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.609489", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}