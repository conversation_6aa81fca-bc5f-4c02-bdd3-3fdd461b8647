{"cve_id": "CVE-2025-53624", "published_date": "2025-07-09T21:15:27.953", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "The Docusaurus gists plugin adds a page to your Docusaurus instance, displaying all public gists of a GitHub user. docusaurus-plugin-content-gists versions prior to 4.0.0 are vulnerable to exposing GitHub Personal Access Tokens in production build artifacts when passed through plugin configuration options. The token, intended for build-time API access only, is inadvertently included in client-side JavaScript bundles, making it accessible to anyone who can view the website's source code. This vulnerability is fixed in 4.0.0."}, {"lang": "es", "value": "El complemento Docusaurus gists añade una página a tu instancia de Docusaurus que muestra todos los gists públicos de un usuario de GitHub. Las versiones de docusaurus-plugin-content-gists anteriores a la 4.0.0 son vulnerables a la exposición de tokens de acceso personal de GitHub en artefactos de compilación de producción al pasarlos a través de las opciones de configuración del complemento. El token, destinado únicamente al acceso a la API durante la compilación, se incluye accidentalmente en paquetes de JavaScript del lado del cliente, lo que lo hace accesible a cualquiera que pueda ver el código fuente del sitio web. Esta vulnerabilidad se corrigió en la versión 4.0.0."}], "references": [{"url": "https://github.com/webbertakken/docusaurus-plugin-content-gists/commit/8d4230b82412edb215ddfa9e609d178510a5fe31", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/webbertakken/docusaurus-plugin-content-gists/security/advisories/GHSA-qf34-qpr4-5pph", "source": "<EMAIL>", "tags": []}]}