{"cve_id": "CVE-2025-7415", "published_date": "2025-07-10T21:15:30.443", "last_modified_date": "2025-07-16T15:00:33.120", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in Tenda O3V2 ********(3880). This issue affects the function fromTraceroutGet of the file /goform/getTraceroute of the component httpd. The manipulation of the argument dest leads to command injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en Tenda O3V2 ********(3880). Este problema afecta a la función fromTraceroutGet del archivo /goform/getTraceroute del componente httpd. La manipulación del argumento dest provoca la inyección de comandos. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/wudipjq/my_vuln/blob/main/Tenda3/vuln_48/48.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/wudipjq/my_vuln/blob/main/Tenda3/vuln_48/48.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315875", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315875", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608856", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}