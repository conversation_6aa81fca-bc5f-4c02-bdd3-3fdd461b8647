{"cve_id": "CVE-2025-7524", "published_date": "2025-07-13T09:15:24.247", "last_modified_date": "2025-07-15T20:15:52.227", "descriptions": [{"lang": "en", "value": "A vulnerability was found in TOTOLINK T6 4.1.5cu.748_B20211015. It has been classified as critical. This affects the function setDiagnosisCfg of the file /cgi-bin/cstecgi.cgi of the component HTTP POST Request Handler. The manipulation of the argument ip leads to command injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en TOTOLINK T6 4.1.5cu.748_B20211015. Se ha clasificado como crítica. Afecta a la función setDiagnosisCfg del archivo /cgi-bin/cstecgi.cgi del componente HTTP POST Request Handler. La manipulación del argumento ip provoca la inyección de comandos. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/2.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/2.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316221", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316221", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.612935", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.youtube.com/watch?v=T62BuSoHmoM", "source": "<EMAIL>", "tags": ["Exploit"]}]}