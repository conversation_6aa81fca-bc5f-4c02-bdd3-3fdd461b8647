{"cve_id": "CVE-2025-7547", "published_date": "2025-07-13T23:15:23.093", "last_modified_date": "2025-07-16T14:36:41.720", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in Campcodes Online Movie Theater Seat Reservation System 1.0. This affects the function save_movie of the file /admin/admin_class.php. The manipulation of the argument cover leads to unrestricted upload. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad, clasificada como crítica, en Campcodes Online Movie Theater Seat Reservation System 1.0. Esta afecta a la función \"save_movie\" del archivo /admin/admin_class.php. La manipulación del argumento \"cover\" permite la carga sin restricciones. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/N1n3b9S/cve/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316245", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316245", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.614466", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}