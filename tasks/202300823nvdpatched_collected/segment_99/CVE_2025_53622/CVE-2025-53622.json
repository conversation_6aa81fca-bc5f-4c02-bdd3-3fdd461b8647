{"cve_id": "CVE-2025-53622", "published_date": "2025-07-15T15:15:25.680", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "DSpace open source software is a repository application which provides durable access to digital resources. Prior to versions 7.6.4, 8.2, and 9.1, a path traversal vulnerability is possible during the import of an archive (in Simple Archive Format), either from command-line (`./dspace import` command) or from the \"Batch Import (Zip)\" user interface feature. An attacker may craft a malicious Simple Archive Format (SAF) package where the `contents` file references any system files (using relative traversal sequences) which are readable by the Tomcat user.  If such a package is imported, this will result in sensitive content disclose, including retrieving arbitrary files or configurations from the server where DSpace is running. The Simple Archive Format (SAF) importer / Batch Import (Zip) is only usable by site administrators (from user interface / REST API) or system administrators (from command-line). Therefore, to exploit this vulnerability, the malicious payload would have to be provided by an attacker and trusted by an administrator (who would trigger the import). The fix is included in DSpace 7.6.4, 8.2 and 9.1. For those who cannot upgrade immediately, it is possible to manually patch the DSpace backend. (No changes are necessary to the frontend.)  A pull request exists which can be used to patch systems running DSpace 7.6.x, 8.x or 9.0. Although it is not possible to fully protect the system via workarounds, one may can apply a best practice. Administrators must carefully inspect any SAF archives (they did not construct themselves) before importing, paying close attention to the `contents` file to validate it does not reference files outside of the SAF archives."}, {"lang": "es", "value": "El software de código abierto DSpace es una aplicación de repositorio que proporciona acceso duradero a recursos digitales. En versiones anteriores a la 7.6.4, 8.2 y 9.1, existía una vulnerabilidad de path traversal durante la importación de un archivo (en formato de archivo simple), ya sea desde la línea de comandos (comando `./dspace import`) o desde la función de interfaz de usuario \"Importación por lotes (Zip)\". Un atacante podría crear un paquete malicioso en formato de archivo simple (SAF) donde el archivo `contents` haga referencia a cualquier archivo del sistema (mediante secuencias de recorrido relativas) legible para el usuario de Tomcat. Si se importa dicho paquete, se divulgará contenido sensible, incluyendo la recuperación de archivos o configuraciones arbitrarias del servidor donde se ejecuta DSpace. El importador de formato de archivo simple (SAF)/Importación por lotes (Zip) solo puede ser utilizado por administradores del sitio (desde la interfaz de usuario/API REST) o administradores del sistema (desde la línea de comandos). Por lo tanto, para explotar esta vulnerabilidad, el payload malicioso tendría que ser proporcionado por un atacante y contar con la confianza de un administrador (quien activaría la importación). La solución está incluida en DSpace 7.6.4, 8.2 y 9.1. Quienes no puedan actualizar inmediatamente, pueden aplicar un parche manualmente al backend de DSpace (no es necesario modificar el frontend). Existe una solicitud de extracción que puede utilizarse para aplicar parches a sistemas que ejecutan DSpace 7.6.x, 8.x o 9.0. Aunque no es posible proteger completamente el sistema mediante soluciones alternativas, se puede aplicar una práctica recomendada. Los administradores deben inspeccionar cuidadosamente cualquier archivo SAF (que no hayan creado ellos mismos) antes de importar, prestando especial atención al archivo `contents` para verificar que no haga referencia a archivos externos a los archivos SAF."}], "references": [{"url": "https://github.com/DSpace/DSpace/pull/11036", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11036.patch", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11037", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11037.patch", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11038", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11038.patch", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/security/advisories/GHSA-vhvx-8xgc-99wf", "source": "<EMAIL>", "tags": []}]}