{"cve_id": "CVE-2025-51651", "published_date": "2025-07-14T17:15:33.413", "last_modified_date": "2025-07-17T13:27:40.607", "descriptions": [{"lang": "en", "value": "An authenticated arbitrary file download vulnerability in the component /admin/Backups.php of Mccms v2.7.0 allows attackers to download arbitrary files via a crafted GET request."}, {"lang": "es", "value": "Una vulnerabilidad de descarga arbitraria de archivos autenticados en el componente /admin/Backups.php de Mccms v2.7.0 permite a los atacantes descargar archivos arbitrarios a través de una solicitud GET manipulada."}], "references": [{"url": "https://github.com/Y4y17/CVE/blob/main/Arbitrary%20file%20download%20vulnerability.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}