{"cve_id": "CVE-2025-6491", "published_date": "2025-07-13T22:15:23.353", "last_modified_date": "2025-07-22T16:51:04.830", "descriptions": [{"lang": "en", "value": "In PHP versions:8.1.* before 8.1.33, 8.2.* before 8.2.29, 8.3.* before 8.3.23, 8.4.* before 8.4.10 when parsing XML data in SOAP extensions, overly large (>2Gb) XML namespace prefix may lead to null pointer dereference. This may lead to crashes and affect the availability of the target server."}, {"lang": "es", "value": "En las versiones de PHP 8.1.* anteriores a la 8.1.33, 8.2.* anteriores a la 8.2.29, 8.3.* anteriores a la 8.3.23 y 8.4.* anteriores a la 8.4.10, al analizar datos XML en extensiones SOAP, un prefijo de espacio de nombres XML demasiado grande (&gt;2 GB) puede provocar la desreferencia de puntero nulo. Esto puede provocar fallos y afectar la disponibilidad del servidor de destino."}], "references": [{"url": "https://github.com/php/php-src/security/advisories/GHSA-453j-q27h-5p8x", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}