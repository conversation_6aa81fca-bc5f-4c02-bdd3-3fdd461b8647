{"cve_id": "CVE-2025-53864", "published_date": "2025-07-11T03:16:03.563", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Connect2id Nimbus JOSE + JWT before 10.0.2 allows a remote attacker to cause a denial of service via a deeply nested JSON object supplied in a JWT claim set, because of uncontrolled recursion. NOTE: this is independent of the Gson 2.11.0 issue because the Connect2id product could have checked the JSON object nesting depth, regardless of what limits (if any) were imposed by Gson."}, {"lang": "es", "value": "Connect2id Nimbus JOSE + JWT anterior a la versión 10.0.2 permite a un atacante remoto provocar una denegación de servicio mediante un objeto JSON profundamente anidado, suministrado en un conjunto de notificaciones JWT, debido a una recursión incontrolada. NOTA: Esto es independiente del problema de Gson 2.11.0, ya que Connect2id podría haber comprobado la profundidad de anidación de objetos JSON, independientemente de los límites (si los hubiera) impuestos por Gson."}], "references": [{"url": "https://bitbucket.org/connect2id/nimbus-jose-jwt/issues/583/stackoverflowerror-due-to-deeply-nested", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/google/gson/commit/1039427ff0100293dd3cf967a53a55282c0fef6b", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/google/gson/compare/gson-parent-2.11.0...gson-parent-2.12.0", "source": "<EMAIL>", "tags": []}]}