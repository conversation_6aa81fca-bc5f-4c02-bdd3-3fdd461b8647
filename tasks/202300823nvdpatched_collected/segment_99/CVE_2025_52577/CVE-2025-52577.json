{"cve_id": "CVE-2025-52577", "published_date": "2025-07-11T00:15:26.430", "last_modified_date": "2025-07-23T19:20:13.513", "descriptions": [{"lang": "en", "value": "A vulnerability exists in Advantech iView that could allow SQL injection\n and remote code execution through NetworkServlet.archiveTrapRange(). \nThis issue requires an authenticated attacker with at least user-level \nprivileges. Certain input parameters are not properly sanitized, \nallowing an attacker to perform SQL injection and potentially execute \ncode in the context of the 'nt authority\\local service' account."}, {"lang": "es", "value": "Existe una vulnerabilidad en Advantech iView que podría permitir la inyección SQL y la ejecución remota de código mediante NetworkServlet.archiveTrapRange(). Este problema requiere un atacante autenticado con al menos privilegios de usuario. Ciertos parámetros de entrada no se desinfectan correctamente, lo que permite a un atacante realizar una inyección SQL y potencialmente ejecutar código en el contexto de la cuenta 'nt authority\\local service'."}], "references": [{"url": "https://www.advantech.com/en/support/details/firmware-?id=1-HIPU-183", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.cisa.gov/news-events/ics-advisories/icsa-25-191-08", "source": "<EMAIL>", "tags": ["Third Party Advisory", "US Government Resource"]}]}