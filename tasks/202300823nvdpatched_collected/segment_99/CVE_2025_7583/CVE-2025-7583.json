{"cve_id": "CVE-2025-7583", "published_date": "2025-07-14T08:15:23.897", "last_modified_date": "2025-07-16T14:34:16.413", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Online Fire Reporting System 1.2 and classified as critical. This vulnerability affects unknown code of the file /admin/all-requests.php. The manipulation of the argument teamid leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Online Fire Reporting System 1.2, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /admin/all-requests.php. La manipulación del argumento teamid provoca una inyección SQL. El ataque puede iniciarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/131", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316282", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316282", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615038", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}