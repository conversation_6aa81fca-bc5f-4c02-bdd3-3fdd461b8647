{"cve_id": "CVE-2025-53652", "published_date": "2025-07-09T16:15:24.627", "last_modified_date": "2025-07-18T18:03:14.117", "descriptions": [{"lang": "en", "value": "Jenkins Git Parameter Plugin 439.vb_0e46ca_14534 and earlier does not validate that the Git parameter value submitted to the build matches one of the offered choices, allowing attackers with Item/Build permission to inject arbitrary values into Git parameters."}, {"lang": "es", "value": "Jenkins Git Parameter Plugin 439.vb_0e46ca_14534 y anteriores no valida que el valor del parámetro Git enviado a la compilación coincida con una de las opciones ofrecidas, lo que permite a los atacantes con permiso de Elemento/Compilación inyectar valores arbitrarios en los parámetros Git."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3419", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}