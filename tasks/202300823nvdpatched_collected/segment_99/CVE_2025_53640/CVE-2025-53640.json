{"cve_id": "CVE-2025-53640", "published_date": "2025-07-14T21:15:27.663", "last_modified_date": "2025-07-22T16:15:33.047", "descriptions": [{"lang": "en", "value": "Indico is an event management system that uses Flask-Multipass, a multi-backend authentication system for Flask. Starting in version 2.2 and prior to version 3.3.7, an endpoint used to display details of users listed in certain fields (such as ACLs) could be misused to dump basic user details (such as name, affiliation and email) in bulk. Version 3.3.7 fixes the issue. Owners of instances that allow everyone to create a user account, who wish to truly restrict access to these user details, should consider restricting user search to managers. As a workaround, it is possible to restrict access to the affected endpoints (e.g. in the webserver config), but doing so would break certain form fields which could no longer show the details of the users listed in those fields, so upgrading instead is highly recommended."}, {"lang": "es", "value": "Indico es un sistema de gestión de eventos que utiliza Flask-Multipass, un sistema de autenticación multibackend para Flask. A partir de la versión 2.2 y anteriores a la 3.3.7, un endpoint utilizado para mostrar la información de los usuarios en ciertos campos (como las ACL) podía utilizarse indebidamente para volcar información básica del usuario (como nombre, afiliación y correo electrónico) de forma masiva. La versión 3.3.7 soluciona este problema. Los propietarios de instancias que permiten a cualquier persona crear una cuenta de usuario y que deseen restringir el acceso a esta información de usuario deberían considerar restringir la búsqueda de usuarios a los administradores. Como solución alternativa, es posible restringir el acceso a los endpoints afectados (por ejemplo, en la configuración del servidor web); sin embargo, esto interrumpiría el funcionamiento de ciertos campos de formulario, que ya no mostrarían la información de los usuarios incluidos en ellos, por lo que se recomienda encarecidamente actualizar la versión."}], "references": [{"url": "https://docs.getindico.io/en/stable/config/settings/#ALLOW_PUBLIC_USER_SEARCH", "source": "<EMAIL>", "tags": []}, {"url": "https://docs.getindico.io/en/stable/installation/upgrade", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/indico/indico/releases/tag/v3.3.7", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/indico/indico/security/advisories/GHSA-q28v-664f-q6wj", "source": "<EMAIL>", "tags": []}, {"url": "https://www.vicarius.io/vsociety/posts/cve202553640-detect-indico-vulnerability", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}, {"url": "https://www.vicarius.io/vsociety/posts/cve202553640-mitigate-indico-vulnerability", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}