{"cve_id": "CVE-2025-52951", "published_date": "2025-07-11T15:15:25.740", "last_modified_date": "2025-07-15T15:15:25.340", "descriptions": [{"lang": "en", "value": "A Protection Mechanism Failure vulnerability in kernel filter processing of Juniper Networks Junos OS allows an attacker sending IPv6 traffic destined to the device to effectively bypass any firewall filtering configured on the interface.\n\nDue to an issue with Junos OS kernel filter processing, the 'payload-protocol' match is not being supported, causing any term containing it to accept all packets without taking any other action. In essence, these firewall filter terms were being processed as an 'accept' for all traffic on the interface destined for the control plane, even when used in combination with other match criteria.\n\nThis issue only affects firewall filters protecting the device's control plane. Transit firewall filtering is unaffected by this vulnerability.\n\nThis issue affects Junos OS: \n\n\n\n  *  all versions before 21.2R3-S9, \n  *  from 21.4 before 21.4R3-S11, \n  *  from 22.2 before 22.2R3-S7, \n  *  from 22.4 before 22.4R3-S7, \n  *  from 23.2 before 23.2R2-S4, \n  *  from 23.4 before 23.4R2-S5, \n  *  from 24.2 before 24.2R2-S1, \n  *  from 24.4 before 24.4R1-S2, 24.4R2.\n\n\n\nThis is a more complete fix for previously published CVE-2024-21607 (JSA75748)."}, {"lang": "es", "value": "Una vulnerabilidad de fallo del mecanismo de protección en el procesamiento del filtro del kernel de Junos OS de Juniper Networks permite a un atacante que envía tráfico IPv6 a una interfaz eludir eficazmente cualquier filtrado del firewall configurado en ella. Debido a un problema con el procesamiento del filtro del kernel de Junos OS, no se admite la coincidencia \"payload-protocol\", lo que provoca que cualquier término que la contenga acepte todos los paquetes sin realizar ninguna otra acción. En esencia, estos términos del filtro del firewall se procesaban como una \"aceptación\" para todo el tráfico de la interfaz. Este problema afecta a Junos OS: * todas las versiones anteriores a 21.2R3-S9, * desde la versión 21.4 hasta la 21.4R3-S11, * desde la versión 22.2 hasta la 22.2R3-S7, * desde la versión 22.4 hasta la 22.4R3-S7, * desde la versión 23.2 hasta la 23.2R2-S4, * desde la versión 23.4 hasta la 23.4R2-S5, * desde la versión 24.2 hasta la 24.2R2-S1, * desde la versión 24.4 hasta la 24.4R1-S2, 24.4R2. Esta es una corrección más completa para la CVE-2024-21607 (JSA75748) publicada anteriormente."}], "references": [{"url": "https://supportportal.juniper.net/JSA100055", "source": "<EMAIL>", "tags": []}]}