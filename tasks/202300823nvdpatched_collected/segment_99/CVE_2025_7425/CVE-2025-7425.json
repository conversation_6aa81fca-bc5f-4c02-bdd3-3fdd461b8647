{"cve_id": "CVE-2025-7425", "published_date": "2025-07-10T14:15:27.877", "last_modified_date": "2025-07-15T13:24:41.097", "descriptions": [{"lang": "en", "value": "A flaw was found in libxslt where the attribute type, atype, flags are modified in a way that corrupts internal memory management. When XSLT functions, such as the key() process, result in tree fragments, this corruption prevents the proper cleanup of ID attributes. As a result, the system may access freed memory, causing crashes or enabling attackers to trigger heap corruption."}, {"lang": "es", "value": "Se encontró una falla en libxslt donde los atributos type, atype y flags se modifican de forma que corrompe la gestión de memoria interna. Cuando las funciones XSLT, como el proceso key(), generan fragmentos de árbol, esta corrupción impide la limpieza correcta de los atributos ID. Como resultado, el sistema puede acceder a la memoria liberada, provocando fallos o permitiendo a los atacantes provocar la corrupción del montón."}], "references": [{"url": "https://access.redhat.com/security/cve/CVE-2025-7425", "source": "<EMAIL>", "tags": []}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2379274", "source": "<EMAIL>", "tags": []}, {"url": "https://gitlab.gnome.org/GNOME/libxslt/-/issues/140", "source": "<EMAIL>", "tags": []}]}