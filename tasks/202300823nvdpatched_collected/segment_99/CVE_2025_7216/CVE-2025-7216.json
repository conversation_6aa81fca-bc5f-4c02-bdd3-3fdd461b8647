{"cve_id": "CVE-2025-7216", "published_date": "2025-07-09T05:15:39.380", "last_modified_date": "2025-07-10T13:18:53.830", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in lty628 Aidigu up to 1.8.2. This affects the function checkUserCookie of the file /application/common.php of the component PHP Object Handler. The manipulation of the argument rememberMe leads to deserialization. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en lty628 Aidigu (hasta la versión 1.8.2). Esta afecta a la función checkUserCookie del archivo /application/common.php del componente PHP Object Handler. La manipulación del argumento rememberMe provoca la deserialización. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://note-hxlab.wetolink.com/share/Bon2P1OSuNDc", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.315165", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.315165", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.608209", "source": "<EMAIL>", "tags": []}]}