{"cve_id": "CVE-2025-7542", "published_date": "2025-07-13T21:15:25.270", "last_modified_date": "2025-07-16T14:37:28.693", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul User Registration & Login and User Management System 3.3 and classified as critical. Affected by this issue is some unknown functionality of the file /admin/user-profile.php. The manipulation of the argument uid leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul User Registration &amp; Login and User Management System 3.3, clasificada como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/user-profile.php. La manipulación del argumento uid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/4m3rr0r/PoCVulDb/blob/main/CVE-2025-7542.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316238", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316238", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613919", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}