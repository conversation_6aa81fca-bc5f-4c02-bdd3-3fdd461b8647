{"cve_id": "CVE-2025-53645", "published_date": "2025-07-09T17:15:31.297", "last_modified_date": "2025-07-22T16:15:33.187", "descriptions": [{"lang": "en", "value": "Zimbra Collaboration (ZCS) before 9.0.0 Patch 46, 10.0.x before 10.0.15, and 10.1.x before 10.1.9 is vulnerable to a denial of service condition due to improper handling of excessive, comma-separated path segments in the Admin Console. An unauthenticated remote attacker can send specially crafted GET requests that trigger redundant processing and inflated responses. This leads to uncontrolled resource consumption, resulting in denial of service."}, {"lang": "es", "value": "Zimbra Collaboration Suite (ZCS) anterior a la versión 9.0.0 Parche 46, 10.0.x anterior a la 10.0.15 y 10.1.x anterior a la 10.1.9 es vulnerable a una condición de denegación de servicio debido al manejo inadecuado de segmentos de ruta excesivos separados por comas, tanto en la interfaz de correo web como en la Consola de administración. Un atacante remoto no autenticado puede enviar solicitudes GET especialmente manipuladas que desencadenan un procesamiento redundante y respuestas infladas. Esto provoca un consumo descontrolado de recursos, lo que resulta en una denegación de servicio."}], "references": [{"url": "https://wiki.zimbra.com/wiki/Security_Center", "source": "<EMAIL>", "tags": []}, {"url": "https://wiki.zimbra.com/wiki/Zimbra_Releases/10.0.15#Security_Fixes", "source": "<EMAIL>", "tags": []}, {"url": "https://wiki.zimbra.com/wiki/Zimbra_Releases/10.1.9#Security_Fixes", "source": "<EMAIL>", "tags": []}, {"url": "https://wiki.zimbra.com/wiki/Zimbra_Releases/9.0.0/P46#Security_Fixes", "source": "<EMAIL>", "tags": []}, {"url": "https://wiki.zimbra.com/wiki/Zimbra_Responsible_Disclosure_Policy", "source": "<EMAIL>", "tags": []}, {"url": "https://wiki.zimbra.com/wiki/Zimbra_Security_Advisories", "source": "<EMAIL>", "tags": []}]}