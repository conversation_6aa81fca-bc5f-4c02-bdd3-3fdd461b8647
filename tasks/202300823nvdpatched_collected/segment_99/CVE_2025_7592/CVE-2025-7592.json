{"cve_id": "CVE-2025-7592", "published_date": "2025-07-14T10:15:28.747", "last_modified_date": "2025-07-16T16:59:53.350", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Dairy Farm Shop Management System 1.3 and classified as critical. Affected by this vulnerability is an unknown functionality of the file invoices.php. The manipulation of the argument del leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Dairy Farm Shop Management System 1.3, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo receipts.php. La manipulación del argumento \"del\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/138", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316291", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316291", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615232", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}