{"cve_id": "CVE-2025-53371", "published_date": "2025-07-10T18:15:24.280", "last_modified_date": "2025-07-15T13:24:41.097", "descriptions": [{"lang": "en", "value": "DiscordNotifications is an extension for MediaWiki that sends notifications of actions in your Wiki to a Discord channel. DiscordNotifications allows sending requests via curl and file_get_contents to arbitrary URLs set via $wgDiscordIncomingWebhookUrl and $wgDiscordAdditionalIncomingWebhookUrls. This allows for DOS by causing the server to read large files. SSRF is also possible if there are internal unprotected APIs that can be accessed using HTTP POST requests, which could also possibly lead to RCE. This vulnerability is fixed in commit 1f20d850cbcce5b15951c7c6127b87b927a5415e."}, {"lang": "es", "value": "DiscordNotifications es una extensión para MediaWiki que envía notificaciones de acciones en tu wiki a un canal de Discord. DiscordNotifications permite enviar solicitudes mediante curl y file_get_contents a URLs arbitrarias configuradas mediante $wgDiscordIncomingWebhookUrl y $wgDiscordAdditionalIncomingWebhookUrls. Esto permite ataques de denegación de servicio (DOS) al obligar al servidor a leer archivos grandes. SSRF también es posible si existen API internas sin protección a las que se puede acceder mediante solicitudes HTTP POST, lo que podría provocar un RCE. Esta vulnerabilidad está corregida en el commit 1f20d850cbcce5b15951c7c6127b87b927a5415e."}], "references": [{"url": "https://github.com/miraheze/DiscordNotifications/commit/1f20d850cbcce5b15951c7c6127b87b927a5415e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/miraheze/DiscordNotifications/security/advisories/GHSA-gvfx-p3h5-qf65", "source": "<EMAIL>", "tags": []}]}