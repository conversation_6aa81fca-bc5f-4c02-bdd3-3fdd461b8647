{"cve_id": "CVE-2025-7556", "published_date": "2025-07-14T01:15:22.420", "last_modified_date": "2025-07-16T14:36:23.887", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in code-projects Voting System 1.0. Affected is an unknown function of the file /admin/voters_edit.php. The manipulation of the argument ID leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en code-projects Voting System 1.0. Se ve afectada una función desconocida del archivo /admin/voters_edit.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/i-Corner/cve/issues/4", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316254", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316254", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615022", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}