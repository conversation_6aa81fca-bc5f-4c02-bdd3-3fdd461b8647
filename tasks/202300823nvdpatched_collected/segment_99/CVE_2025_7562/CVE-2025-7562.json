{"cve_id": "CVE-2025-7562", "published_date": "2025-07-14T03:15:24.150", "last_modified_date": "2025-07-15T18:31:21.547", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in PHPGurukul Online Fire Reporting System 1.2. Affected is an unknown function of the file /admin/new-requests.php. The manipulation of the argument teamid leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en PHPGurukul Online Fire Reporting System 1.2. Se ve afectada una función desconocida del archivo /admin/new-requests.php. La manipulación del argumento teamid provoca una inyección SQL. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/128", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316260", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316260", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615035", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}