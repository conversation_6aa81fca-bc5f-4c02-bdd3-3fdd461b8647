{"cve_id": "CVE-2025-7515", "published_date": "2025-07-13T04:15:49.913", "last_modified_date": "2025-07-15T17:40:16.637", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in code-projects Online Appointment Booking System 1.0. This affects an unknown part of the file /ulocateus.php. The manipulation of the argument doctorname leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en code-projects Online Appointment Booking System 1.0. Esta afecta a una parte desconocida del archivo /ulocateus.php. La manipulación del argumento doctorname provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/cccc88/cve/issues/3", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316197", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316197", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.612925", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}