{"cve_id": "CVE-2025-6549", "published_date": "2025-07-11T16:15:26.587", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Incorrect Authorization vulnerability in the web server of Juniper Networks Junos OS on SRX Series allows an unauthenticated, network-based attacker to reach the \n\nJuniper Web Device Manager\n\n (J-Web).\n\nWhen Juniper Secure connect (JSC) is enabled on specific interfaces, or multiple interfaces are configured for J-Web, the J-Web UI is reachable over more than the intended interfaces.\nThis issue affects Junos OS:\n\n\n\n  *  all versions before 21.4R3-S9,\n  *  22.2 versions before 22.2R3-S5,\n  *  22.4 versions before 22.4R3-S5,\n  *  23.2 versions before 23.2R2-S3,\n  *  23.4 versions before 23.4R2-S5,\n  *  24.2 versions before 24.2R2."}, {"lang": "es", "value": "Una vulnerabilidad de autorización incorrecta en el servidor web de Juniper Networks Junos OS en la serie SRX permite que un atacante no autenticado acceda a Juniper Web Device Manager (J-Web). Cuando Juniper Secure Connect (JSC) está habilitado en interfaces específicas, o se configuran varias interfaces para J-Web, se puede acceder a la interfaz de usuario de J-Web desde más de las interfaces previstas. Este problema afecta a Junos OS: * todas las versiones anteriores a 21.4R3-S9, * versiones 22.2 anteriores a 22.2R3-S5, * versiones 22.4 anteriores a 22.4R3-S5, * versiones 23.2 anteriores a 23.2R2-S3, * versiones 23.4 anteriores a 23.4R2-S5, * versiones 24.2 anteriores a 24.2R2."}], "references": [{"url": "https://supportportal.juniper.net/JSA100098", "source": "<EMAIL>", "tags": []}]}