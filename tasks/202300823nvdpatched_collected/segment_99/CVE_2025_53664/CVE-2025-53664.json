{"cve_id": "CVE-2025-53664", "published_date": "2025-07-09T16:15:25.837", "last_modified_date": "2025-07-18T18:41:56.733", "descriptions": [{"lang": "en", "value": "Jenkins Apica Loadtest Plugin 1.10 and earlier stores Apica Loadtest LTP authentication tokens unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins Apica Loadtest Plugin 1.10 y versiones anteriores almacenan tokens de autenticación LTP de Apica Loadtest sin cifrar en archivos job config.xml en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador de Jenkins pueden verlos."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3540", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}