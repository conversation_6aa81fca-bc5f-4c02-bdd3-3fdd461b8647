{"cve_id": "CVE-2025-5037", "published_date": "2025-07-10T12:15:23.173", "last_modified_date": "2025-07-22T16:42:24.810", "descriptions": [{"lang": "en", "value": "A maliciously crafted RFA, RTE, or RVT file, when parsed through Autodesk Revit, can force a Memory Corruption vulnerability. A malicious actor can leverage this vulnerability to execute arbitrary code in the context of the current process."}, {"lang": "es", "value": "Un archivo RFA manipulado con fines maliciosos, al analizarse mediante Autodesk Revit, puede generar una vulnerabilidad de corrupción de memoria. Un agente malicioso puede aprovechar esta vulnerabilidad para ejecutar código arbitrario en el contexto del proceso actual."}], "references": [{"url": "https://www.autodesk.com/trust/security-advisories/adsk-sa-2025-0012", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}