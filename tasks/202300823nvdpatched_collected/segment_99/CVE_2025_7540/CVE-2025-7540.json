{"cve_id": "CVE-2025-7540", "published_date": "2025-07-13T20:15:26.270", "last_modified_date": "2025-07-16T14:53:47.927", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in code-projects Online Appointment Booking System 1.0. Affected is an unknown function of the file /getclinic.php. The manipulation of the argument townid leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used. Other parameters might be affected as well."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en code-projects Online Appointment Booking System 1.0. Se ve afectada una función desconocida del archivo /getclinic.php. La manipulación del argumento townid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Otros parámetros también podrían verse afectados."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/bit001020/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316236", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316236", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613690", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}