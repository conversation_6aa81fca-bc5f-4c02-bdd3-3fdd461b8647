{"cve_id": "CVE-2025-7584", "published_date": "2025-07-14T08:15:24.327", "last_modified_date": "2025-07-16T14:34:01.303", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Fire Reporting System 1.2 and classified as critical. This issue affects some unknown processing of the file /admin/add-team.php. The manipulation of the argument teammember leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Fire Reporting System 1.2, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /admin/add-team.php. La manipulación del argumento \"teammember\" provoca una inyección SQL. El ataque puede iniciarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/132", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316283", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316283", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615039", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}