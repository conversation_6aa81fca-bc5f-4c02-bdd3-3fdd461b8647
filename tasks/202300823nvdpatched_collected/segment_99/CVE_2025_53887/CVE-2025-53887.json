{"cve_id": "CVE-2025-53887", "published_date": "2025-07-15T00:15:23.847", "last_modified_date": "2025-07-16T14:19:39.037", "descriptions": [{"lang": "en", "value": "Directus is a real-time API and App dashboard for managing SQL database content. Starting in version 9.0.0 and prior to version 11.9.0, the exact Directus version number is incorrectly being used as OpenAPI Spec version this means that it is being exposed by the `/server/specs/oas` endpoint without authentication. With the exact version information a malicious attacker can look for known vulnerabilities in Directus core or any of its shipped dependencies in that specific running version. Version 11.9.0 fixes the issue."}, {"lang": "es", "value": "Directus es una API en tiempo real y un panel de control de aplicaciones para gestionar el contenido de bases de datos SQL. A partir de la versión 9.0.0 y anteriores a la 11.9.0, el número de versión exacto de Directus se utiliza incorrectamente como versión de OpenAPI Spec, lo que significa que el endpoint `/server/specs/oas` lo expone sin autenticación. Con la información exacta de la versión, un atacante malicioso puede buscar vulnerabilidades conocidas en el núcleo de Directus o en cualquiera de sus dependencias incluidas en esa versión específica. La versión 11.9.0 soluciona este problema."}], "references": [{"url": "https://github.com/directus/directus/commit/e74f3e4e92edc33b5f83eefb001a3d2a85af17a3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/pull/25353", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/directus/directus/releases/tag/v11.9.0", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/directus/directus/security/advisories/GHSA-rmjh-cf9q-pv7q", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}