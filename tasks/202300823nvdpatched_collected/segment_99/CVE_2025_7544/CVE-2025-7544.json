{"cve_id": "CVE-2025-7544", "published_date": "2025-07-13T22:15:23.693", "last_modified_date": "2025-07-16T14:36:53.967", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Tenda AC1206 ***********. It has been rated as critical. This issue affects the function formSetMacFilterCfg of the file /goform/setMacFilterCfg. The manipulation of the argument deviceList leads to stack-based buffer overflow. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Tenda AC1206 ***********. Se ha clasificado como crítica. Este problema afecta a la función formSetMacFilterCfg del archivo /goform/setMacFilterCfg. La manipulación del argumento deviceList provoca un desbordamiento del búfer en la pila. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/panda666-888/vuls/blob/main/tenda/ac1206/formSetMacFilterCfg.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/panda666-888/vuls/blob/main/tenda/ac1206/formSetMacFilterCfg.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316241", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316241", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.614089", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}