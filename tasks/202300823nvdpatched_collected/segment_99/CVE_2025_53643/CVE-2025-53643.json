{"cve_id": "CVE-2025-53643", "published_date": "2025-07-14T21:15:27.827", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "AIOHTTP is an asynchronous HTTP client/server framework for asyncio and Python. Prior to version 3.12.14, the Python parser is vulnerable to a request smuggling vulnerability due to not parsing trailer sections of an HTTP request. If a pure Python version of aiohttp is installed (i.e. without the usual C extensions) or AIOHTTP_NO_EXTENSIONS is enabled, then an attacker may be able to execute a request smuggling attack to bypass certain firewalls or proxy protections. Version 3.12.14 contains a patch for this issue."}, {"lang": "es", "value": "AIOHTTP es un framework cliente/servidor HTTP asíncrono para asyncio y Python. En versiones anteriores a la 3.12.14, el analizador de Python era vulnerable a una vulnerabilidad de contrabando de solicitudes debido a que no analizaba las secciones finales de una solicitud HTTP. Si se instala una versión de aiohttp pura en Python (es decir, sin las extensiones habituales de C) o se habilita AIOHTTP_NO_EXTENSIONS, un atacante podría ejecutar un ataque de contrabando de solicitudes para eludir ciertas protecciones de firewall o proxy. La versión 3.12.14 incluye un parche para este problema."}], "references": [{"url": "https://github.com/aio-libs/aiohttp/commit/e8d774f635dc6d1cd3174d0e38891da5de0e2b6a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/aio-libs/aiohttp/security/advisories/GHSA-9548-qrrj-x5pj", "source": "<EMAIL>", "tags": []}]}