{"cve_id": "CVE-2025-7565", "published_date": "2025-07-14T04:15:34.377", "last_modified_date": "2025-07-17T17:48:57.350", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in LB-LINK BL-AC3600 up to 1.0.22. This affects the function geteasycfg of the file /cgi-bin/lighttpd.cgi of the component Web Management Interface. The manipulation of the argument Password leads to information disclosure. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used. The vendor was contacted early about this disclosure but did not respond in any way."}, {"lang": "es", "value": "Se detectó una vulnerabilidad crítica en LB-LINK BL-AC3600 hasta la versión 1.0.22. Esta afecta a la función geteasycfg del archivo /cgi-bin/lighttpd.cgi del componente Interfaz de Administración Web. La manipulación del argumento Password provoca la divulgación de información. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. Se contactó al proveedor con antelación para informarle sobre esta divulgación, pero no respondió."}], "references": [{"url": "https://github.com/waiwai24/0101/blob/main/CVEs/Blink/Plaintext_Password_Leakage_in_the_Web_Management_Interface_of_BL-AC3600_Routers.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/waiwai24/0101/blob/main/CVEs/Blink/Plaintext_Password_Leakage_in_the_Web_Management_Interface_of_BL-AC3600_Routers.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316263", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316263", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.605632", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}