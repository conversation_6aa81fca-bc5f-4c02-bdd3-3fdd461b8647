{"cve_id": "CVE-2025-7492", "published_date": "2025-07-12T22:15:21.397", "last_modified_date": "2025-07-15T20:15:51.577", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Vehicle Parking Management System 1.13. It has been rated as critical. Affected by this issue is some unknown functionality of the file /admin/manage-incomingvehicle.php. The manipulation of the argument del leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /admin/manage-incomingvehicle.php. La manipulación del argumento \"del\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/120", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316142", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316142", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610577", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}