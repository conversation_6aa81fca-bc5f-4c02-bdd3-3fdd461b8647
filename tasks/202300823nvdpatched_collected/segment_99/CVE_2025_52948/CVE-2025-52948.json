{"cve_id": "CVE-2025-52948", "published_date": "2025-07-11T15:15:25.180", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Improper Handling of Exceptional Conditions vulnerability in Berkeley Packet Filter (BPF) processing of Juniper Networks Junos OS allows an attacker, in rare cases, sending specific, unknown traffic patterns to cause the FPC and system to crash and restart.\n\nBPF provides a raw interface to data link layers in a protocol independent fashion. Internally within the Junos kernel, due to a rare timing issue (race condition), when a BPF instance is cloned, the newly created interface causes an internal structure leakage, leading to a system crash. The precise content and timing of the traffic patterns is indeterminate, but has been seen in a lab environment multiple times.\n\nThis issue is more likely to occur when packet capturing is enabled.  See required configuration below.\n\nThis issue affects Junos OS: \n\n\n\n  *  all versions before 21.2R3-S9, \n  *  from 21.4 before 21.4R3-S10, \n  *  from 22.2 before 22.2R3-S6, \n  *  from 22.4 before 22.4R3-S7, \n  *  from 23.2 before 23.2R2-S3, \n  *  from 23.4 before 23.4R2-S3, \n  *  from 24.2 before 24.2R1-S1, 24.2R2."}, {"lang": "es", "value": "Una vulnerabilidad de Manejo Inadecuado de Condiciones Excepcionales en el procesamiento del Filtro de Paquetes Berkeley (BPF) del sistema operativo Junos de Juniper Networks permite que un atacante, en casos excepcionales, envíe patrones de tráfico específicos y desconocidos para provocar el bloqueo y reinicio del FPC y del sistema. El BPF proporciona una interfaz sin procesar a las capas de enlace de datos de forma independiente del protocolo. Internamente, dentro del kernel de Junos, debido a un problema de sincronización poco frecuente (condición de ejecución), al clonar una instancia de BPF, la interfaz recién creada provoca una fuga de estructura interna, lo que provoca un bloqueo del sistema. El contenido y la sincronización precisos de los patrones de tráfico son indeterminados, pero se han observado en un entorno de laboratorio en múltiples ocasiones. Este problema es más probable cuando la captura de paquetes está habilitada. Consulte la configuración requerida a continuación. Este problema afecta a Junos OS: * todas las versiones anteriores a 21.2R3-S9, * desde 21.4 hasta 21.4R3-S10, * desde 22.2 hasta 22.2R3-S6, * desde 22.4 hasta 22.4R3-S7, * desde 23.2 hasta 23.2R2-S3, * desde 23.4 hasta 23.4R2-S3, * desde 24.2 hasta 24.2R1-S1, 24.2R2."}], "references": [{"url": "https://supportportal.juniper.net/JSA100052", "source": "<EMAIL>", "tags": []}, {"url": "https://www.juniper.net/documentation/us/en/software/junos/network-mgmt/topics/topic-map/analyze-network-traffic-by-using-packet-capture.html", "source": "<EMAIL>", "tags": []}]}