{"cve_id": "CVE-2025-7217", "published_date": "2025-07-09T05:15:39.620", "last_modified_date": "2025-07-11T16:27:11.010", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Campcodes Payroll Management System 1.0 and classified as critical. This vulnerability affects unknown code of the file /ajax.php?action=save_position. The manipulation of the argument ID leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en Campcodes Payroll Management System 1.0, clasificada como crítica. Esta vulnerabilidad afecta al código desconocido del archivo /ajax.php?action=save_position. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/falling-snow1/vuldb/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.315166", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315166", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608251", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}