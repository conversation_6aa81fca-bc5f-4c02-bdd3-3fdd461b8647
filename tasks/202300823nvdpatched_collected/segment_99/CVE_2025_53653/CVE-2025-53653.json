{"cve_id": "CVE-2025-53653", "published_date": "2025-07-09T16:15:24.733", "last_modified_date": "2025-07-18T17:45:40.817", "descriptions": [{"lang": "en", "value": "Jenkins Aqua Security Scanner Plugin 3.2.8 and earlier stores Scanner Tokens for Aqua API unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins Aqua Security Scanner Plugin 3.2.8 y versiones anteriores almacenan tokens de escáner para Aqua API sin cifrar en archivos job config.xml en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador de Jenkins pueden verlos."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3542", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}