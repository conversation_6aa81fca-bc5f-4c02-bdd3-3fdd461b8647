{"cve_id": "CVE-2025-53642", "published_date": "2025-07-11T18:15:35.123", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "haxcms-nodejs and haxcms-php are backends for HAXcms. The logout function within the application does not terminate a user's session or clear their cookies. Additionally, the application issues a refresh token when logging out. This vulnerability is fixed in 11.0.6."}, {"lang": "es", "value": "haxcms-nodejs y haxcms-php son backends para HAXcms. La función de cierre de sesión de la aplicación no cierra la sesión del usuario ni borra sus cookies. Además, la aplicación emite un token de actualización al cerrar sesión. Esta vulnerabilidad se corrigió en la versión 11.0.6."}], "references": [{"url": "https://github.com/haxtheweb/issues/security/advisories/GHSA-g4f5-5w5j-p5jg", "source": "<EMAIL>", "tags": []}]}