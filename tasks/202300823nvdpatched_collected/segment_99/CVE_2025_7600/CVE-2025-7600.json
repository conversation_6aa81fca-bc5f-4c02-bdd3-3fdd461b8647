{"cve_id": "CVE-2025-7600", "published_date": "2025-07-14T12:15:24.653", "last_modified_date": "2025-07-16T14:33:26.127", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Online Library Management System 3.0. This affects an unknown part of the file /admin/student-history.php. The manipulation of the argument stdid leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Online Library Management System 3.0. Esta afecta a una parte desconocida del archivo /admin/student-history.php. La manipulación del argumento stdid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/141", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316299", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316299", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615295", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}