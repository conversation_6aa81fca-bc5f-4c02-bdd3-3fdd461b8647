{"cve_id": "CVE-2025-53546", "published_date": "2025-07-09T15:15:24.787", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "Folo organizes feeds content into one timeline. Using pull_request_target on .github/workflows/auto-fix-lint-format-commit.yml can be exploited by attackers, since untrusted code can be executed having full access to secrets (from the base repo). By exploiting the vulnerability is possible to exfiltrate GITHUB_TOKEN which has high privileges. GITHUB_TOKEN can be used to completely overtake the repo since the token has content write privileges. This vulnerability is fixed in commit 585c6a591440cd39f92374230ac5d65d7dd23d6a."}, {"lang": "es", "value": "Folo organiza el contenido de los feeds en una sola línea de tiempo. El uso de pull_request_target en .github/workflows/auto-fix-lint-format-commit.yml puede ser explotado por atacantes, ya que se puede ejecutar código no confiable con acceso completo a los secretos (del repositorio base). Al explotar esta vulnerabilidad, es posible exfiltrar GITHUB_TOKEN, que tiene altos privilegios. GITHUB_TOKEN puede usarse para controlar completamente el repositorio, ya que el token tiene privilegios de escritura de contenido. Esta vulnerabilidad está corregida en el commit 585c6a591440cd39f92374230ac5d65d7dd23d6a."}], "references": [{"url": "https://github.com/RSSNext/Folo/commit/585c6a591440cd39f92374230ac5d65d7dd23d6a", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/RSSNext/Folo/security/advisories/GHSA-h87r-5w74-qfm4", "source": "<EMAIL>", "tags": []}]}