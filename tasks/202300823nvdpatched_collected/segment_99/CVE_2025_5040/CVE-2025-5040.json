{"cve_id": "CVE-2025-5040", "published_date": "2025-07-10T12:15:23.893", "last_modified_date": "2025-07-22T16:50:39.567", "descriptions": [{"lang": "en", "value": "A maliciously crafted RTE file, when parsed through Autodesk Revit, can force a Heap-Based Overflow vulnerability. A malicious actor can leverage this vulnerability to cause a crash, read sensitive data, or execute arbitrary code in the context of the current process."}, {"lang": "es", "value": "Un archivo RTE manipulado con fines maliciosos, al analizarse mediante Autodesk Revit, puede generar una vulnerabilidad de desbordamiento basado en montón. Un agente malicioso puede aprovechar esta vulnerabilidad para provocar un bloqueo, leer datos confidenciales o ejecutar código arbitrario en el contexto del proceso actual."}], "references": [{"url": "https://www.autodesk.com/trust/security-advisories/adsk-sa-2025-0012", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}