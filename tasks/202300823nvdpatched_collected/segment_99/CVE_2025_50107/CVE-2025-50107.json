{"cve_id": "CVE-2025-50107", "published_date": "2025-07-15T20:15:47.140", "last_modified_date": "2025-07-24T20:28:46.290", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle Universal Work Queue product of Oracle E-Business Suite (component: Request handling).  Supported versions that are affected are 12.2.5-12.2.14. Easily exploitable vulnerability allows unauthenticated attacker with network access via HTTP to compromise Oracle Universal Work Queue.  Successful attacks require human interaction from a person other than the attacker and while the vulnerability is in Oracle Universal Work Queue, attacks may significantly impact additional products (scope change). Successful attacks of this vulnerability can result in  unauthorized update, insert or delete access to some of Oracle Universal Work Queue accessible data as well as  unauthorized read access to a subset of Oracle Universal Work Queue accessible data. CVSS 3.1 Base Score 6.1 (Confidentiality and Integrity impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en Oracle Universal Work Queue de Oracle E-Business Suite (componente: Gestión de solicitudes). Las versiones compatibles afectadas son la 12.2.5-12.2.14. Esta vulnerabilidad, fácilmente explotable, permite que un atacante no autenticado con acceso a la red a través de HTTP comprometa Oracle Universal Work Queue. Los ataques exitosos requieren la interacción humana de una persona distinta al atacante y, si bien la vulnerabilidad afecta a Oracle Universal Work Queue, pueden afectar significativamente a otros productos (cambio de alcance). Los ataques exitosos de esta vulnerabilidad pueden resultar en actualizaciones, inserciones o eliminaciones no autorizadas de algunos datos accesibles de Oracle Universal Work Queue, así como en accesos de lectura no autorizados a un subconjunto de dichos datos. Puntuación base de CVSS 3.1: 6.1 (Afecta a la confidencialidad y la integridad). Vector CVSS: (CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}