{"cve_id": "CVE-2025-53623", "published_date": "2025-07-14T20:15:29.323", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "The Job Iteration API is an an extension for ActiveJob that make jobs interruptible and resumable Versions prior to 1.11.0 have an arbitrary code execution vulnerability in the `CsvEnumerator` class. This vulnerability can be exploited by an attacker to execute arbitrary commands on the system where the application is running, potentially leading to unauthorized access, data leakage, or complete system compromise. The issue is fixed in versions `1.11.0` and above. Users can mitigate the risk by avoiding the use of untrusted input in the `CsvEnumerator` class and ensuring that any file paths are properly sanitized and validated before being passed to the class methods. Users should avoid using the `count_of_rows_in_file` method with untrusted CSV filenames."}, {"lang": "es", "value": "La API de iteración de trabajos es una extensión de ActiveJob que permite interrumpir y reanudar los trabajos. Las versiones anteriores a la 1.11.0 presentan una vulnerabilidad de ejecución de código arbitrario en la clase `CsvEnumerator`. Un atacante puede aprovechar esta vulnerabilidad para ejecutar comandos arbitrarios en el sistema donde se ejecuta la aplicación, lo que podría provocar acceso no autorizado, fuga de datos o la vulneración total del sistema. El problema se ha solucionado en las versiones `1.11.0` y posteriores. Los usuarios pueden mitigar el riesgo evitando el uso de entradas no confiables en la clase `CsvEnumerator` y asegurándose de que las rutas de archivo se depuren y validen correctamente antes de pasarlas a los métodos de la clase. Se recomienda a los usuarios evitar el uso del método `count_of_rows_in_file` con nombres de archivo CSV no confiables."}], "references": [{"url": "https://github.com/Shopify/job-iteration/commit/1a7adfdd041105a5e45e774cadc6b973a292ba55", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Shopify/job-iteration/pull/595", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Shopify/job-iteration/releases/tag/v1.11.0", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Shopify/job-iteration/security/advisories/GHSA-6qjf-g333-pv38", "source": "<EMAIL>", "tags": []}]}