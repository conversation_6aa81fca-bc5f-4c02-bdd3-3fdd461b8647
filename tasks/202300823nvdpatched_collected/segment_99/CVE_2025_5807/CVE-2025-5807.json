{"cve_id": "CVE-2025-5807", "published_date": "2025-07-10T02:15:26.230", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "The Gwolle Guestbook plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘gwolle_gb_content’ parameter in all versions up to, and including, 4.9.2 due to insufficient input sanitization and output escaping. This makes it possible for unauthenticated attackers to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Gwolle Guestbook para WordPress es vulnerable a Cross-Site Scripting almacenado a través del parámetro 'gwolle_gb_content' en todas las versiones hasta la 4.9.2 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes no autenticados inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset/3316455/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/956f86c5-05af-41c3-a779-5b25f62122dd?source=cve", "source": "<EMAIL>", "tags": []}]}