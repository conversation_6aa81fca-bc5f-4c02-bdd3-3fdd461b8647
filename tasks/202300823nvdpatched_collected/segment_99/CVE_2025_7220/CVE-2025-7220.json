{"cve_id": "CVE-2025-7220", "published_date": "2025-07-09T07:15:24.263", "last_modified_date": "2025-07-11T16:27:52.557", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Campcodes Payroll Management System 1.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /ajax.php?action=save_deductions. The manipulation of the argument ID leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Campcodes Payroll Management System 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /ajax.php?action=save_deductions. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/skyrainoh/CVE/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.315169", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315169", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608264", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}