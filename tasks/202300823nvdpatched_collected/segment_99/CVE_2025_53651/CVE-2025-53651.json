{"cve_id": "CVE-2025-53651", "published_date": "2025-07-09T16:15:24.513", "last_modified_date": "2025-07-18T18:02:43.727", "descriptions": [{"lang": "en", "value": "Jenkins HTML Publisher Plugin 425 and earlier displays log messages that include the absolute paths of files archived during the Publish HTML reports post-build step, exposing information about the Jenkins controller file system in the build log."}, {"lang": "es", "value": "El complemento Jenkins HTML Publisher 425 y versiones anteriores muestran mensajes de registro que incluyen las rutas absolutas de los archivos archivados durante el paso posterior a la compilación de Publicar informes HTML, lo que expone información sobre el sistema de archivos del controlador Jenkins en el registro de compilación."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3547", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}