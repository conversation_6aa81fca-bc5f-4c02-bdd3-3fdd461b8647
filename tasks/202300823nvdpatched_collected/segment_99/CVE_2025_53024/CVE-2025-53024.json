{"cve_id": "CVE-2025-53024", "published_date": "2025-07-15T20:15:48.393", "last_modified_date": "2025-07-16T19:49:32.097", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle VM VirtualBox product of Oracle Virtualization (component: Core).   The supported version that is affected is 7.1.10. Easily exploitable vulnerability allows high privileged attacker with logon to the infrastructure where Oracle VM VirtualBox executes to compromise Oracle VM VirtualBox.  While the vulnerability is in Oracle VM VirtualBox, attacks may significantly impact additional products (scope change).  Successful attacks of this vulnerability can result in takeover of Oracle VM VirtualBox. CVSS 3.1 Base Score 8.2 (Confidentiality, Integrity and Availability impacts).  CVSS Vector: (CVSS:3.1/AV:L/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H)."}, {"lang": "es", "value": "Vulnerabilidad en el producto Oracle VM VirtualBox de Oracle Virtualization (componente: Core). La versión compatible afectada es la 7.1.10. Esta vulnerabilidad, fácilmente explotable, permite a un atacante con privilegios elevados, con acceso a la infraestructura donde se ejecuta Oracle VM VirtualBox, comprometer Oracle VM VirtualBox. Si bien la vulnerabilidad se encuentra en Oracle VM VirtualBox, los ataques pueden afectar significativamente a otros productos (cambio de alcance). Los ataques exitosos a esta vulnerabilidad pueden resultar en la toma de control de Oracle VM VirtualBox. Puntuación base de CVSS 3.1: 8.2 (impactos en confidencialidad, integridad y disponibilidad). Vector CVSS: (CVSS:3.1/AV:L/AC:L/PR:H/UI:N/S:C/C:H/I:H/A:H)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}