{"cve_id": "CVE-2025-7590", "published_date": "2025-07-14T09:15:24.393", "last_modified_date": "2025-07-15T18:29:14.350", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in PHPGurukul Dairy Farm Shop Management System 1.3. This issue affects some unknown processing of the file edit-category.php. The manipulation of the argument categorycode leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en PHPGurukul Dairy Farm Shop Management System 1.3. Este problema afecta a un procesamiento desconocido del archivo edit-category.php. La manipulación del argumento categorycode provoca una inyección SQL. El ataque puede iniciarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/136", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316289", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316289", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615230", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}