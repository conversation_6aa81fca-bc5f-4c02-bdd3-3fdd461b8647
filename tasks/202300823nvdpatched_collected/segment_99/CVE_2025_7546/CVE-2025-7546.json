{"cve_id": "CVE-2025-7546", "published_date": "2025-07-13T22:15:24.070", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, has been found in GNU Binutils 2.45. Affected by this issue is the function bfd_elf_set_group_contents of the file bfd/elf.c. The manipulation leads to out-of-bounds write. It is possible to launch the attack on the local host. The exploit has been disclosed to the public and may be used. The name of the patch is 41461010eb7c79fee7a9d5f6209accdaac66cc6b. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como problemática en GNU Binutils 2.45. Este problema afecta a la función bfd_elf_set_group_contents del archivo bfd/elf.c. Esta manipulación provoca escritura fuera de los límites. Es posible lanzar el ataque en el host local. Se ha hecho público el exploit y puede que sea utilizado. El parche se llama 41461010eb7c79fee7a9d5f6209accdaac66cc6b. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://sourceware.org/bugzilla/attachment.cgi?id=16118", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=33050", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/bugzilla/show_bug.cgi?id=33050#c2", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceware.org/git/gitweb.cgi?p=binutils-gdb.git;h=41461010eb7c79fee7a9d5f6209accdaac66cc6b", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316244", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316244", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.614375", "source": "<EMAIL>", "tags": []}, {"url": "https://www.gnu.org/", "source": "<EMAIL>", "tags": []}]}