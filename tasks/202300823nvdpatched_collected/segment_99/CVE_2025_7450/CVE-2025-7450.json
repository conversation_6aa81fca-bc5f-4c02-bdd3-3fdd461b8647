{"cve_id": "CVE-2025-7450", "published_date": "2025-07-11T17:15:42.210", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A vulnerability was found in letseeqiji gorobbs up to 1.0.8. It has been classified as critical. This affects the function ResetUserAvatar of the file controller/api/v1/user.go of the component API. The manipulation of the argument filename leads to path traversal. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en letseeqiji gorobbs hasta la versión 1.0.8. Se ha clasificado como crítica. Afecta a la función ResetUserAvatar del archivo controller/api/v1/user.go de la API del componente. La manipulación del argumento filename provoca un path traversal. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/letseeqiji/gorobbs/issues/18", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316095", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316095", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.607799", "source": "<EMAIL>", "tags": []}]}