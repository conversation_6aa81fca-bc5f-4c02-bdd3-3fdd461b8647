{"cve_id": "CVE-2025-53820", "published_date": "2025-07-14T21:15:28.277", "last_modified_date": "2025-07-15T20:15:49.997", "descriptions": [{"lang": "en", "value": "WeGIA is an open source web manager with a focus on the Portuguese language and charitable institutions. A Reflected Cross-Site Scripting (XSS) vulnerability was identified in the `index.php` endpoint of the WeGIA application prior to version 3.4.5. This vulnerability allows attackers to inject malicious scripts in the `erro` parameter. Version 3.4.5 contains a patch for the issue."}, {"lang": "es", "value": "WeGIA es un gestor web de código abierto centrado en el idioma portugués y las instituciones benéficas. Se identificó una vulnerabilidad de Cross-Site Scripting (XSS) Reflejado en el endpoint `index.php` de la aplicación WeGIA antes de la versión 3.4.5. Esta vulnerabilidad permite a los atacantes inyectar scripts maliciosos en el parámetro `erro`. La versión 3.4.5 incluye un parche para solucionar el problema."}], "references": [{"url": "https://github.com/LabRedesCefetRJ/WeGIA/security/advisories/GHSA-9rrm-92jv-xwcv", "source": "<EMAIL>", "tags": ["Exploit", "Vendor Advisory"]}]}