{"cve_id": "CVE-2025-7451", "published_date": "2025-07-14T03:15:23.947", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "The iSherlock developed by Hgiga has an OS Command Injection vulnerability, allowing unauthenticated remote attackers to inject arbitrary OS commands and execute them on the server. This vulnerability has already been exploited. Please update immediately."}, {"lang": "es", "value": "El iSherlock desarrollado por Hgiga presenta una vulnerabilidad de inyección de comandos del sistema operativo, que permite a atacantes remotos no autenticados inyectar comandos arbitrarios del sistema operativo y ejecutarlos en el servidor. Esta vulnerabilidad ya ha sido explotada. Por favor, actualice la versión de inmediato."}], "references": [{"url": "https://www.twcert.org.tw/en/cp-139-10238-f2bba-2.html", "source": "<EMAIL>", "tags": []}, {"url": "https://www.twcert.org.tw/tw/cp-132-10237-9e0f7-1.html", "source": "<EMAIL>", "tags": []}]}