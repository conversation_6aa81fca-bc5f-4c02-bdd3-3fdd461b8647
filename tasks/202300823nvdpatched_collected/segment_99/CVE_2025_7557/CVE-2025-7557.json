{"cve_id": "CVE-2025-7557", "published_date": "2025-07-14T01:15:22.597", "last_modified_date": "2025-07-16T14:35:37.447", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in code-projects Voting System 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /admin/voters_row.php. The manipulation of the argument ID leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en code-projects Voting System 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /admin/voters_row.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/i-Corner/cve/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316255", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316255", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615023", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}