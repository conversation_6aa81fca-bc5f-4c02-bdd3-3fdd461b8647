{"cve_id": "CVE-2025-53670", "published_date": "2025-07-09T16:15:26.490", "last_modified_date": "2025-07-18T18:48:33.803", "descriptions": [{"lang": "en", "value": "Jenkins Nouvola DiveCloud Plugin 1.08 and earlier stores DiveCloud API Keys and Credentials Encryption Keys unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins Nouvola DiveCloud Plugin 1.08 y las versiones anteriores almacenan las claves de cifrado de credenciales y las claves de API de DiveCloud sin cifrar en los archivos job config.xml en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elementos o acceso al sistema de archivos del controlador de Jenkins pueden verlas."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3526", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}