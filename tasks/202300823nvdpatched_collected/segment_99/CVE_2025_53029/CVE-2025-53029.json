{"cve_id": "CVE-2025-53029", "published_date": "2025-07-15T20:15:49.043", "last_modified_date": "2025-07-16T19:58:08.907", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle VM VirtualBox product of Oracle Virtualization (component: Core).   The supported version that is affected is 7.1.10. Easily exploitable vulnerability allows high privileged attacker with logon to the infrastructure where Oracle VM VirtualBox executes to compromise Oracle VM VirtualBox.  Successful attacks of this vulnerability can result in  unauthorized read access to a subset of Oracle VM VirtualBox accessible data. CVSS 3.1 Base Score 2.3 (Confidentiality impacts).  CVSS Vector: (CVSS:3.1/AV:L/AC:L/PR:H/UI:N/S:U/C:L/I:N/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en el producto Oracle VM VirtualBox de Oracle Virtualization (componente: Core). La versión compatible afectada es la 7.1.10. Esta vulnerabilidad, fácilmente explotable, permite a un atacante con privilegios elevados, con acceso a la infraestructura donde se ejecuta Oracle VM VirtualBox, comprometer Oracle VM VirtualBox. Los ataques con éxito de esta vulnerabilidad pueden resultar en acceso de lectura no autorizado a un subconjunto de datos accesibles de Oracle VM VirtualBox. Puntuación base de CVSS 3.1: 2.3 (Afecta a la confidencialidad). Vector CVSS: (CVSS:3.1/AV:L/AC:L/PR:H/UI:N/S:U/C:L/I:N/A:N)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}