{"cve_id": "CVE-2025-53903", "published_date": "2025-07-15T19:15:23.220", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "The Scratch Channel is a news website that is under development as of time of this writing. The file `/api/users.js` doesn't properly sanitize text box inputs, leading to a potential vulnerability to cross-site scripting attacks. Commit 90b39eb56b27b2bac29001abb1a3cac0964b8ddb addresses this issue."}, {"lang": "es", "value": "Scratch Channel es un sitio web de noticias en desarrollo al momento de escribir este artículo. El archivo `/api/users.js` no depura correctamente las entradas de los cuadros de texto, lo que genera una posible vulnerabilidad a ataques de cross site scripting. El commit 90b39eb56b27b2bac29001abb1a3cac0964b8ddb soluciona este problema."}], "references": [{"url": "https://github.com/The-Scratch-Channel/the-scratch-channel.github.io/commit/90b39eb56b27b2bac29001abb1a3cac0964b8ddb", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/The-Scratch-Channel/the-scratch-channel.github.io/security/advisories/GHSA-25wp-g9g6-7fr9", "source": "<EMAIL>", "tags": []}]}