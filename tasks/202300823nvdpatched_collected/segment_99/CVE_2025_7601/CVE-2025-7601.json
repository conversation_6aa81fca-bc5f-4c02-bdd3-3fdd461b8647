{"cve_id": "CVE-2025-7601", "published_date": "2025-07-14T12:15:24.850", "last_modified_date": "2025-07-16T14:33:11.107", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Online Library Management System 3.0 and classified as problematic. This vulnerability affects unknown code of the file /admin/student-history.php. The manipulation of the argument stdid leads to cross site scripting. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Online Library Management System 3.0, clasificada como problemática. Esta vulnerabilidad afecta al código desconocido del archivo /admin/student-history.php. La manipulación del argumento stdid provoca ataques de cross site scripting. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/142", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316300", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316300", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615297", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}