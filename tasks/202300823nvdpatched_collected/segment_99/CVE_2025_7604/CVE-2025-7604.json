{"cve_id": "CVE-2025-7604", "published_date": "2025-07-14T13:15:25.650", "last_modified_date": "2025-07-16T14:32:32.300", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Hospital Management System 4.0. It has been declared as critical. Affected by this vulnerability is an unknown functionality of the file /user-login.php. The manipulation of the argument Username leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Hospital Management System 4.0. Se ha declarado crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /user-login.php. La manipulación del argumento \"Username\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/143", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316303", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316303", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615321", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}