{"cve_id": "CVE-2025-50067", "published_date": "2025-07-15T20:15:41.340", "last_modified_date": "2025-07-24T21:27:21.010", "descriptions": [{"lang": "en", "value": "Vulnerability in Oracle Application Express (component: Strategic Planner Starter App).  Supported versions that are affected are 24.2.4 and  24.2.5. Easily exploitable vulnerability allows low privileged attacker with network access via HTTP to compromise Oracle Application Express.  Successful attacks require human interaction from a person other than the attacker and while the vulnerability is in Oracle Application Express, attacks may significantly impact additional products (scope change). Successful attacks of this vulnerability can result in takeover of Oracle Application Express. CVSS 3.1 Base Score 9.0 (Confidentiality, Integrity and Availability impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H)."}, {"lang": "es", "value": "Vulnerabilidad en Oracle Application Express (componente: Strategic Planner Starter App). Las versiones compatibles afectadas son la 24.2.4 y la 24.2.5. Esta vulnerabilidad, fácilmente explotable, permite a un atacante con privilegios reducidos y acceso a la red a través de HTTP comprometer Oracle Application Express. Los ataques exitosos requieren la interacción humana de una persona distinta al atacante y, si bien la vulnerabilidad afecta a Oracle Application Express, pueden afectar significativamente a otros productos (cambio de alcance). Los ataques exitosos de esta vulnerabilidad pueden resultar en la toma de control de Oracle Application Express. Puntuación base de CVSS 3.1: 9.0 (impactos en confidencialidad, integridad y disponibilidad). Vector CVSS: (CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:C/C:H/I:H/A:H)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}