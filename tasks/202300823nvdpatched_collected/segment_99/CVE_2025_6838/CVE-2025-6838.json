{"cve_id": "CVE-2025-6838", "published_date": "2025-07-11T09:15:25.200", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The Broken Link Notifier plugin for WordPress is vulnerable to CSV Injection in all versions up to, and including, 1.3.0 via broken links that are later exported. This makes it possible for authenticated attackers, with Contributor-level access and above, to embed untrusted input into exported CSV files, which can result in code execution when these files are downloaded and opened on a local system with a vulnerable configuration."}, {"lang": "es", "value": "El complemento Broken Link Notifier para WordPress es vulnerable a la inyección de CSV en todas las versiones hasta la 1.3.0 incluida, a través de enlaces rotos que se exportan posteriormente. Esto permite a atacantes autenticados, con acceso de Colaborador o superior, incrustar información no confiable en archivos CSV exportados, lo que puede provocar la ejecución de código al descargar y abrir estos archivos en un sistema local con una configuración vulnerable."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3323864%40broken-link-notifier&new=3323864%40broken-link-notifier&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/cd96beee-afcb-4439-ad9b-f24e8afeac3c?source=cve", "source": "<EMAIL>", "tags": []}]}