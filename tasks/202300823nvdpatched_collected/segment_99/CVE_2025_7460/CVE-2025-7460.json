{"cve_id": "CVE-2025-7460", "published_date": "2025-07-11T22:15:26.373", "last_modified_date": "2025-07-16T14:58:33.887", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in TOTOLINK T6 4.1.5cu.748_B20211015 and classified as critical. Affected by this vulnerability is the function setWiFiAclRules of the file /cgi-bin/cstecgi.cgi of the component HTTP POST Request Handler. The manipulation of the argument mac leads to buffer overflow. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en TOTOLINK T6 4.1.5cu.748_B20211015, clasificada como crítica. Esta vulnerabilidad afecta a la función setWiFiAclRules del archivo /cgi-bin/cstecgi.cgi del componente HTTP POST Request Handler. La manipulación del argumento \"mac\" provoca un desbordamiento del búfer. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/1.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/1.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316111", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316111", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.609819", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}