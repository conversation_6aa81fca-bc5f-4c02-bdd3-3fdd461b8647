{"cve_id": "CVE-2025-7490", "published_date": "2025-07-12T21:15:21.440", "last_modified_date": "2025-07-15T18:05:55.740", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Vehicle Parking Management System 1.13. It has been classified as critical. Affected is an unknown function of the file /admin/reg-users.php. The manipulation of the argument del leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /admin/reg-users.php. La manipulación del argumento del provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/117", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316140", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316140", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610574", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}