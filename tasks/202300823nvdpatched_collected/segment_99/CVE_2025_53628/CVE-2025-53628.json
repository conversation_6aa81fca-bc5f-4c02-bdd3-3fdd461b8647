{"cve_id": "CVE-2025-53628", "published_date": "2025-07-10T20:15:27.220", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "cpp-httplib is a C++11 single-file header-only cross platform HTTP/HTTPS library. Prior to 0.20.1, cpp-httplib does not have a limit for a unique line, permitting an attacker to explore this to allocate memory arbitrarily. This vulnerability is fixed in 0.20.1. NOTE: This vulnerability is related to CVE-2025-53629."}, {"lang": "es", "value": "cpp-httplib es una librería HTTP/HTTPS multiplataforma de C++11 con un solo archivo de encabezado. En versiones anteriores a la 0.20.1, cpp-httplib no tenía límite de línea única, lo que permitía a un atacante aprovecharlo para asignar memoria arbitrariamente. Esta vulnerabilidad se corrigió en la 0.20.1. NOTA: Esta vulnerabilidad está relacionada con CVE-2025-53629."}], "references": [{"url": "https://github.com/yhirose/cpp-httplib/commit/7b752106ac42bd5b907793950d9125a0972c8e8e", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/yhirose/cpp-httplib/security/advisories/GHSA-j6p8-779x-p5pw", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/yhirose/cpp-httplib/security/advisories/GHSA-qjmq-h3cc-qv6w", "source": "<EMAIL>", "tags": []}]}