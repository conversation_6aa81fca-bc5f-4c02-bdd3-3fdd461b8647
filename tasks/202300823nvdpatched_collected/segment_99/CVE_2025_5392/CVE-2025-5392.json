{"cve_id": "CVE-2025-5392", "published_date": "2025-07-11T07:15:25.033", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The GB Forms DB plugin for WordPress is vulnerable to Remote Code Execution in all versions up to, and including, 1.0.2 via the gbfdb_talk_to_front() function. This is due to the function accepting user input and then passing that through call_user_func(). This makes it possible for unauthenticated attackers to execute code on the server which can be leverage to inject backdoors or create new administrative user accounts to name a few things."}, {"lang": "es", "value": "El complemento GB Forms DB para WordPress es vulnerable a la ejecución remota de código en todas las versiones hasta la 1.0.2 incluida, a través de la función gbfdb_talk_to_front(). Esto se debe a que la función acepta la entrada del usuario y la pasa a través de call_user_func(). Esto permite que atacantes no autenticados ejecuten código en el servidor, lo que puede utilizarse para inyectar puertas traseras o crear nuevas cuentas de usuario administrativas, entre otras cosas."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/gb-forms-db/trunk/core/functions.php#L334", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/gb-forms-db/trunk/core/functions.php#L367", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3323703%40gb-forms-db&new=3323703%40gb-forms-db&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fe8723a7-bbb1-41a0-b222-3cf4eb44cd64?source=cve", "source": "<EMAIL>", "tags": []}]}