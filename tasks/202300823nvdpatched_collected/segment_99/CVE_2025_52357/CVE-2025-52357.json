{"cve_id": "CVE-2025-52357", "published_date": "2025-07-09T20:15:27.053", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "Cross-Site Scripting (XSS) vulnerability exists in the ping diagnostic feature of FiberHome FD602GW-DX-R410 router (firmware V2.2.14), allowing an authenticated attacker to execute arbitrary JavaScript code in the context of the router s web interface. The vulnerability is triggered via user-supplied input in the ping form field, which fails to sanitize special characters. This can be exploited to hijack sessions or escalate privileges through social engineering or browser-based attacks."}, {"lang": "es", "value": "Existe una vulnerabilidad de Cross-Site Scripting (XSS) en la función de diagnóstico de ping del router FiberHome FD602GW-DX-R410 (firmware V2.2.14), que permite a un atacante autenticado ejecutar código JavaScript arbitrario en la interfaz web del router. La vulnerabilidad se activa mediante la entrada del usuario en el campo de formulario de ping, que no corrige los caracteres especiales. Esto puede explotarse para secuestrar sesiones o escalar privilegios mediante ingeniería social o ataques basados en el navegador."}], "references": [{"url": "https://github.com/wrathfulDiety/CVE-2025-52357", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/wrathfulDiety/fd602gw-dx-r410-xss-advisory/blob/main/README.md", "source": "<EMAIL>", "tags": []}]}