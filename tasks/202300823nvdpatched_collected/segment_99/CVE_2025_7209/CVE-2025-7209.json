{"cve_id": "CVE-2025-7209", "published_date": "2025-07-09T01:15:50.773", "last_modified_date": "2025-07-10T13:18:53.830", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in 9fans plan9port up to 9da5b44 and classified as problematic. Affected by this vulnerability is the function value_decode in the library src/libsec/port/x509.c. The manipulation leads to null pointer dereference. Local access is required to approach this attack. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available. The identifier of the patch is deae8939583d83fd798fca97665e0e94656c3ee8. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en 9fans plan9port hasta la versión 9da5b44, clasificada como problemática. Esta vulnerabilidad afecta a la función value_decode de la librería src/libsec/port/x509.c. La manipulación provoca la desreferencia de puntero nulo. Se requiere acceso local para abordar este ataque. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza versiones continuas para garantizar una distribución continua. Por lo tanto, no se dispone de información sobre las versiones afectadas ni sobre las actualizadas. El identificador del parche es deae8939583d83fd798fca97665e0e94656c3ee8. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://git.9front.org/plan9front/plan9front/deae8939583d83fd798fca97665e0e94656c3ee8/commit.html", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/9fans/plan9port/issues/711", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/9fans/plan9port/issues/711#issuecomment-2819905220", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/user-attachments/files/19698361/plan9port_crash_2.txt", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.315157", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.315157", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.607685", "source": "<EMAIL>", "tags": []}]}