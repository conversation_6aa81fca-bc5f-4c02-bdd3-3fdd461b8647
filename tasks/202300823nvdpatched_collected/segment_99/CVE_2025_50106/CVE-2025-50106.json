{"cve_id": "CVE-2025-50106", "published_date": "2025-07-15T20:15:47.013", "last_modified_date": "2025-07-16T14:58:59.837", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle Java SE, Oracle GraalVM for JDK, Oracle GraalVM Enterprise Edition product of Oracle Java SE (component: 2D).  Supported versions that are affected are Oracle Java SE: 8u451, 8u451-perf, 11.0.27, 17.0.15, 21.0.7, 24.0.1; Oracle GraalVM for JDK: 17.0.15, 21.0.7 and  24.0.1; Oracle GraalVM Enterprise Edition: 21.3.14. Difficult to exploit vulnerability allows unauthenticated attacker with network access via multiple protocols to compromise Oracle Java SE, Oracle GraalVM for JDK, Oracle GraalVM Enterprise Edition.  Successful attacks of this vulnerability can result in takeover of Oracle Java SE, Oracle GraalVM for JDK, Oracle GraalVM Enterprise Edition. Note: This vulnerability can be exploited by using APIs in the specified Component, e.g., through a web service which supplies data to the APIs. This vulnerability also applies to Java deployments, typically in clients running sandboxed Java Web Start applications or sandboxed Java applets, that load and run untrusted code (e.g., code that comes from the internet) and rely on the Java sandbox for security. CVSS 3.1 Base Score 8.1 (Confidentiality, Integrity and Availability impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H)."}, {"lang": "es", "value": "Vulnerabilidad en Oracle Java SE, Oracle GraalVM para JD<PERSON> y Oracle GraalVM Enterprise Edition (componente 2D). Las versiones compatibles afectadas son Oracle Java SE: 8u451, 8u451-perf, 11.0.27, 17.0.15, 21.0.7 y 24.0.1; Oracle GraalVM para JDK: 17.0.15, 21.0.7 y 24.0.1; Oracle GraalVM Enterprise Edition: 21.3.14. Esta vulnerabilidad, difícil de explotar, permite que un atacante no autenticado con acceso a la red a través de múltiples protocolos comprometa Oracle Java SE, Oracle GraalVM para JDK y Oracle GraalVM Enterprise Edition. Los ataques con éxito a esta vulnerabilidad pueden resultar en la toma de control de Oracle Java SE, Oracle GraalVM para JDK y Oracle GraalVM Enterprise Edition. Nota: Esta vulnerabilidad puede explotarse mediante el uso de las API en el componente especificado, por ejemplo, a través de un servicio web que suministra datos a las API. Esta vulnerabilidad también afecta a implementaciones de Java, generalmente en clientes que ejecutan aplicaciones Java Web Start o applets Java en entornos aislados, que cargan y ejecutan código no confiable (por ejemplo, código proveniente de internet) y dependen del entorno aislado de Java para su seguridad. Puntuación base de CVSS 3.1: 8.1 (Afecta a la confidencialidad, integridad y disponibilidad). Vector CVSS: (CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:H/I:H/A:H)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": []}]}