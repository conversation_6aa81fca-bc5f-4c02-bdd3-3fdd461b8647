{"cve_id": "CVE-2025-7026", "published_date": "2025-07-11T16:15:26.897", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A vulnerability in the Software SMI handler (SwSmiInputValue 0xB2) allows a local attacker to control the RBX register, which is used as an unchecked pointer in the CommandRcx0 function. If the contents at RBX match certain expected values (e.g., '$DB$' or '2DB$'), the function performs arbitrary writes to System Management RAM (SMRAM), leading to potential privilege escalation to System Management Mode (SMM) and persistent firmware compromise."}, {"lang": "es", "value": "Una vulnerabilidad en el controlador Software SMI (SwSmiInputValue 0xB2) permite a un atacante local controlar el registro RBX, que se utiliza como puntero sin control en la función CommandRcx0. Si el contenido de RBX coincide con ciertos valores esperados (p. ej., '$DB$' o '2DB$'), la función realiza escrituras arbitrarias en la RAM de administración del sistema (SMRAM), lo que puede provocar una escalada de privilegios al modo de administración del sistema (SMM) y una vulnerabilidad persistente del firmware."}], "references": [{"url": "https://kb.cert.org/vuls/id/746790", "source": "<EMAIL>", "tags": []}, {"url": "https://www.binarly.io/advisories/brly-dva-2025-008", "source": "<EMAIL>", "tags": []}, {"url": "https://www.gigabyte.com/Support/Security", "source": "<EMAIL>", "tags": []}]}