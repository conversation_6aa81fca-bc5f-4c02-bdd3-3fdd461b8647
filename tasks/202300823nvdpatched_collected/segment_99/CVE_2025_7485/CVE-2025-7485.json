{"cve_id": "CVE-2025-7485", "published_date": "2025-07-12T19:15:25.993", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability classified as problematic was found in Open5GS up to 2.7.3. Affected by this vulnerability is the function ngap_recv_handler/s1ap_recv_handler/recv_handler of the component SCTP Partial Message Handler. The manipulation leads to reachable assertion. The attack needs to be approached locally. The patch is named cfa44575020f3fb045fd971358442053c8684d3d. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se detectó una vulnerabilidad clasificada como problemática en Open5GS hasta la versión 2.7.3. Esta vulnerabilidad afecta la función ngap_recv_handler/s1ap_recv_handler/recv_handler del componente SCTP Partial Message Handler. La manipulación genera una aserción accesible. El ataque debe abordarse localmente. El parche se llama cfa44575020f3fb045fd971358442053c8684d3d. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/open5gs/open5gs/commit/cfa44575020f3fb045fd971358442053c8684d3d", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/open5gs/open5gs/issues/3878#issuecomment-2853775136", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/open5gs/open5gs/issues/3878/", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316135", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316135", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.610601", "source": "<EMAIL>", "tags": []}]}