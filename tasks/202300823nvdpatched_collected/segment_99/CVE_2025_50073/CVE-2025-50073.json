{"cve_id": "CVE-2025-50073", "published_date": "2025-07-15T20:15:43.067", "last_modified_date": "2025-07-24T20:27:24.660", "descriptions": [{"lang": "en", "value": "Vulnerability in the Oracle WebLogic Server product of Oracle Fusion Middleware (component: Web Container).  Supported versions that are affected are ********.0, ********.0 and  ********.0. Easily exploitable vulnerability allows unauthenticated attacker with network access via HTTP to compromise Oracle WebLogic Server.  Successful attacks require human interaction from a person other than the attacker and while the vulnerability is in Oracle WebLogic Server, attacks may significantly impact additional products (scope change). Successful attacks of this vulnerability can result in  unauthorized update, insert or delete access to some of Oracle WebLogic Server accessible data as well as  unauthorized read access to a subset of Oracle WebLogic Server accessible data. CVSS 3.1 Base Score 6.1 (Confidentiality and Integrity impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en Oracle WebLogic Server de Oracle Fusion Middleware (componente: Web Container). Las versiones compatibles afectadas son ********.0, ********.0 y ********.0. Esta vulnerabilidad, fácilmente explotable, permite que un atacante no autenticado con acceso a la red a través de HTTP comprometa Oracle WebLogic Server. Los ataques exitosos requieren la intervención de una persona distinta al atacante y, si bien la vulnerabilidad se encuentra en Oracle WebLogic Server, pueden afectar significativamente a otros productos (cambio de alcance). Los ataques exitosos de esta vulnerabilidad pueden resultar en actualizaciones, inserciones o eliminaciones no autorizadas de algunos datos accesibles de Oracle WebLogic Server, así como en accesos de lectura no autorizados a un subconjunto de dichos datos. Puntuación base de CVSS 3.1: 6.1 (Afecta a la confidencialidad y la integridad). Vector CVSS: (CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}