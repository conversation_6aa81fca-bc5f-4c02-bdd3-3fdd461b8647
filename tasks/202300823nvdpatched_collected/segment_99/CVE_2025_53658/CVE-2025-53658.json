{"cve_id": "CVE-2025-53658", "published_date": "2025-07-09T16:15:25.237", "last_modified_date": "2025-07-18T17:37:19.640", "descriptions": [{"lang": "en", "value": "<PERSON> Applitools Eyes Plugin 1.16.5 and earlier does not escape the Applitools URL on the build page, resulting in a stored cross-site scripting (XSS) vulnerability exploitable by attackers with Item/Configure permission."}, {"lang": "es", "value": "<PERSON> Applitools Eyes Plugin 1.16.5 y versiones anteriores no escapan a la URL de Applitools en la página de compilación, lo que genera una vulnerabilidad de Cross-Site Scripting (XSS) almacenado que pueden explotar atacantes con permiso Elemento/Configurar."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3509", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}