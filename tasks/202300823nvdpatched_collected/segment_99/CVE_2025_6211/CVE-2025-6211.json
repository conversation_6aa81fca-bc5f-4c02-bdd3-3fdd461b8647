{"cve_id": "CVE-2025-6211", "published_date": "2025-07-10T13:15:23.137", "last_modified_date": "2025-07-10T16:15:24.993", "descriptions": [{"lang": "en", "value": "A vulnerability in the DocugamiReader class of the run-llama/llama_index repository, up to version 0.12.28, involves the use of MD5 hashing to generate IDs for document chunks. This approach leads to hash collisions when structurally distinct chunks contain identical text, resulting in one chunk overwriting another. This can cause loss of semantically or legally important document content, breakage of parent-child chunk hierarchies, and inaccurate or hallucinated responses in AI outputs. The issue is resolved in version 0.3.1."}, {"lang": "es", "value": "Una vulnerabilidad en la clase DocugamiReader del repositorio run-llama/llama_index, hasta la versión 0.12.28, implica el uso de hash MD5 para generar identificadores para fragmentos de documentos. Este enfoque provoca colisiones de hash cuando fragmentos estructuralmente distintos contienen texto idéntico, lo que provoca que un fragmento sobrescriba a otro. Esto puede provocar la pérdida de contenido semántico o legalmente importante del documento, la ruptura de las jerarquías de fragmentos padre-hijo y respuestas inexactas o alucinadas en las salidas de IA. El problema se ha resuelto en la versión 0.3.1."}], "references": [{"url": "https://github.com/run-llama/llama_index/commit/29b2e07e64ed7d302b1cc058185560b28eaa1352", "source": "<EMAIL>", "tags": []}, {"url": "https://huntr.com/bounties/1a48a011-a3c5-4979-9ffc-9652280bc389", "source": "<EMAIL>", "tags": []}]}