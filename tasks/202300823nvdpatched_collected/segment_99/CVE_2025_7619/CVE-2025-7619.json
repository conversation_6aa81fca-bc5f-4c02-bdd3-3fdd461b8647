{"cve_id": "CVE-2025-7619", "published_date": "2025-07-14T04:15:46.850", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "BatchSignCS, a background Windows application developed by WellChoose, has an Arbitrary File Write vulnerability. If a user visits a malicious website while the application is running, remote attackers can write arbitrary files to any path and potentially lead to arbitrary code execution."}, {"lang": "es", "value": "BatchSignCS, una aplicación de Windows en segundo plano desarrollada por WellChoose, presenta una vulnerabilidad de escritura arbitraria de archivos. Si un usuario visita un sitio web malicioso mientras la aplicación se está ejecutando, atacantes remotos pueden escribir archivos arbitrarios en cualquier ruta y potencialmente provocar la ejecución de código arbitrario."}], "references": [{"url": "https://www.twcert.org.tw/en/cp-139-10240-00f86-2.html", "source": "<EMAIL>", "tags": []}, {"url": "https://www.twcert.org.tw/tw/cp-132-10239-770ab-1.html", "source": "<EMAIL>", "tags": []}]}