{"cve_id": "CVE-2025-53895", "published_date": "2025-07-15T17:15:26.940", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "ZITADEL is an open source identity management system. Starting in version 2.53.0 and prior to versions 4.0.0-rc.2, 3.3.2, 2.71.13, and 2.70.14, vulnerability in ZITADEL's session management API allows any authenticated user to update a session if they know its ID, due to a missing permission check. This flaw enables session hijacking, allowing an attacker to impersonate another user and access sensitive resources. Versions prior to `2.53.0` are not affected, as they required the session token for updates. Versions 4.0.0-rc.2, 3.3.2, 2.71.13, and 2.70.14 fix the issue."}, {"lang": "es", "value": "ZITADEL es un sistema de gestión de identidades de código abierto. A partir de la versión 2.53.0 y anteriores a las versiones 4.0.0-rc.2, 3.3.2, 2.71.13 y 2.70.14, una vulnerabilidad en la API de gestión de sesiones de ZITADEL permite a cualquier usuario autenticado actualizar una sesión si conoce su ID, debido a la falta de verificación de permisos. Esta falla permite el secuestro de sesiones, lo que permite a un atacante suplantar la identidad de otro usuario y acceder a recursos confidenciales. Las versiones anteriores a la 2.53.0 no se ven afectadas, ya que requerían el token de sesión para las actualizaciones. Las versiones 4.0.0-rc.2, 3.3.2, 2.71.13 y 2.70.14 solucionan el problema."}], "references": [{"url": "https://github.com/zitadel/zitadel/releases/tag/v2.70.14", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/releases/tag/v2.71.13", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/releases/tag/v3.3.2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/releases/tag/v4.0.0-rc.2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/zitadel/zitadel/security/advisories/GHSA-6c5p-6www-pcmr", "source": "<EMAIL>", "tags": []}]}