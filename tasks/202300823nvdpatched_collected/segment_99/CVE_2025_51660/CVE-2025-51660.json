{"cve_id": "CVE-2025-51660", "published_date": "2025-07-14T17:15:34.833", "last_modified_date": "2025-07-15T16:33:42.263", "descriptions": [{"lang": "en", "value": "SemCms v5.0 was discovered to contain a SQL injection vulnerability via the lgid parameter at SEMCMS_Products.php."}, {"lang": "es", "value": "Se descubrió que SemCms v5.0 contenía una vulnerabilidad de inyección SQL a través del parámetro lgid en SEMCMS_Products.php."}], "references": [{"url": "http://semcms.com", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://github.com/Y4y17/CVE/blob/main/SemCms/SQL_Injection_9.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.sem-cms.com/", "source": "<EMAIL>", "tags": ["Product"]}]}