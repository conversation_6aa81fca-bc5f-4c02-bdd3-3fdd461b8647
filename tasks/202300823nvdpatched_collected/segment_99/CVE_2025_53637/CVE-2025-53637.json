{"cve_id": "CVE-2025-53637", "published_date": "2025-07-10T22:15:24.580", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Meshtastic is an open source mesh networking solution. The main_matrix.yml GitHub Action is triggered by the pull_request_target event, which has extensive permissions, and can be initiated by an attacker who forked the repository and created a pull request. In the shell code execution part, user-controlled input is interpolated unsafely into the code. If this were to be exploited, attackers could inject unauthorized code into the repository. This vulnerability is fixed in 2.6.6."}, {"lang": "es", "value": "Meshtastic es una solución de red en malla de código abierto. La acción de GitHub main_matrix.yml se activa mediante el evento pull_request_target, que cuenta con amplios permisos y puede ser iniciada por un atacante que bifurcó el repositorio y creó una solicitud de extracción. En la ejecución del código de shell, la entrada controlada por el usuario se interpola de forma insegura en el código. Si se explotara esta vulnerabilidad, los atacantes podrían inyectar código no autorizado en el repositorio. Esta vulnerabilidad se corrigió en la versión 2.6.6."}], "references": [{"url": "https://github.com/meshtastic/firmware/blob/3fd47d9713e7d1b6866c48cf218e2435741651a2/.github/workflows/main_matrix.yml#L34-L41", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/meshtastic/firmware/security/advisories/GHSA-6mwm-v2vv-pp96", "source": "<EMAIL>", "tags": []}]}