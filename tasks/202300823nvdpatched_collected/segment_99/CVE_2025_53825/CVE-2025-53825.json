{"cve_id": "CVE-2025-53825", "published_date": "2025-07-14T23:15:24.563", "last_modified_date": "2025-07-15T20:15:50.550", "descriptions": [{"lang": "en", "value": "Dokploy is a free, self-hostable Platform as a Service (PaaS). Prior to version 0.24.3, an unauthenticated preview deployment vulnerability in Dokploy allows any user to execute arbitrary code and access sensitive environment variables by simply opening a pull request on a public repository. This exposes secrets and potentially enables remote code execution, putting all public Dokploy users using these preview deployments at risk. Version 0.24.3 contains a fix for the issue."}, {"lang": "es", "value": "Dokploy es una Plataforma como Servicio (PaaS) gratuita y autoalojada. Antes de la versión 0.24.3, una vulnerabilidad en la implementación de vista previa no autenticada en Dokploy permitía a cualquier usuario ejecutar código arbitrario y acceder a variables de entorno sensibles con solo abrir una solicitud de extracción en un repositorio público. Esto expone secretos y potencialmente permite la ejecución remota de código, poniendo en riesgo a todos los usuarios públicos de Dokploy que utilizan estas implementaciones de vista previa. La versión 0.24.3 contiene una solución para este problema."}], "references": [{"url": "https://github.com/Dokploy/dokploy/commit/1977235d313824b9764f1a06785fb7f73ab7eba2", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Dokploy/dokploy/security/advisories/GHSA-h67g-mpq5-6ph5", "source": "<EMAIL>", "tags": []}]}