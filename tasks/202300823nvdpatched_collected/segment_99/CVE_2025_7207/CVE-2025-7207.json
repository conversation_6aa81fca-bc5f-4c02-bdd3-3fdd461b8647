{"cve_id": "CVE-2025-7207", "published_date": "2025-07-09T01:15:50.380", "last_modified_date": "2025-07-10T13:18:53.830", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as problematic, was found in mruby up to 3.4.0-rc2. Affected is the function scope_new of the file mrbgems/mruby-compiler/core/codegen.c of the component nregs Handler. The manipulation leads to heap-based buffer overflow. An attack has to be approached locally. The exploit has been disclosed to the public and may be used. The name of the patch is 1fdd96104180cc0fb5d3cb086b05ab6458911bb9. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como problemática en mruby hasta la versión 3.4.0-rc2. La función scope_new del archivo mrbgems/mruby-compiler/core/codegen.c del componente nregs Handler se ve afectada. La manipulación provoca un desbordamiento del búfer en el montón. Un ataque debe abordarse localmente. Se ha hecho público el exploit y puede que sea utilizado. El parche se llama 1fdd96104180cc0fb5d3cb086b05ab6458911bb9. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://github.com/mruby/mruby/commit/1fdd96104180cc0fb5d3cb086b05ab6458911bb9", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/mruby/mruby/issues/6509", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/mruby/mruby/issues/6509#event-17145516649", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/user-attachments/files/19619499/mruby_crash.txt", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.315156", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.315156", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.607683", "source": "<EMAIL>", "tags": []}]}