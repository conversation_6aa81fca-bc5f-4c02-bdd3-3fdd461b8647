{"cve_id": "CVE-2025-7459", "published_date": "2025-07-11T21:15:25.657", "last_modified_date": "2025-07-16T14:58:44.577", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in code-projects Mobile Shop 1.0. This vulnerability affects unknown code of the file /EditMobile.php. The manipulation of the argument ID leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en Code-Projects Mobile Shop 1.0. Esta vulnerabilidad afecta al código desconocido del archivo /EditMobile.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/xiajian-qx/cve-xiajian/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316108", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316108", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.609657", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}