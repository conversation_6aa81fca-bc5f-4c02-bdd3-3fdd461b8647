{"cve_id": "CVE-2025-7027", "published_date": "2025-07-11T16:15:27.053", "last_modified_date": "2025-07-15T15:15:26.343", "descriptions": [{"lang": "en", "value": "A vulnerability in the Software SMI handler (SwSmiInputValue 0xB2) allows a local attacker to control both the read and write addresses used by the CommandRcx1 function. The write target is derived from an unvalidated UEFI NVRAM variable (SetupXtuBufferAddress), while the write content is read from an attacker-controlled pointer based on the RBX register. This dual-pointer dereference enables arbitrary memory writes within System Management RAM (SMRAM), leading to potential SMM privilege escalation and firmware compromise."}, {"lang": "es", "value": "Una vulnerabilidad en el controlador Software SMI (SwSmiInputValue 0xB2) permite a un atacante local controlar las direcciones de lectura y escritura utilizadas por la función CommandRcx1. El objetivo de escritura se deriva de una variable NVRAM UEFI no validada (SetupXtuBufferAddress), mientras que el contenido de escritura se lee desde un puntero controlado por el atacante basado en el registro RBX. Esta desreferencia de puntero dual permite escrituras arbitrarias en la memoria RAM de administración del sistema (SMRAM), lo que puede provocar una escalada de privilegios de SMM y comprometer el firmware."}], "references": [{"url": "https://kb.cert.org/vuls/id/746790", "source": "<EMAIL>", "tags": []}, {"url": "https://www.binarly.io/advisories/brly-2025-009", "source": "<EMAIL>", "tags": []}, {"url": "https://www.gigabyte.com/Support/Security", "source": "<EMAIL>", "tags": []}]}