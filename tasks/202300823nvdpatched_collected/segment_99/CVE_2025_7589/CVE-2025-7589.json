{"cve_id": "CVE-2025-7589", "published_date": "2025-07-14T09:15:24.207", "last_modified_date": "2025-07-15T18:29:40.173", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in PHPGurukul Dairy Farm Shop Management System 1.3. This vulnerability affects unknown code of the file edit-company.php. The manipulation of the argument companyname leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Dairy Farm Shop Management System 1.3. Esta vulnerabilidad afecta al código desconocido del archivo edit-company.php. La manipulación del argumento companyname provoca una inyección SQL. El ataque puede iniciarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/135", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316288", "source": "<EMAIL>", "tags": ["Permissions Required", "Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316288", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615229", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}