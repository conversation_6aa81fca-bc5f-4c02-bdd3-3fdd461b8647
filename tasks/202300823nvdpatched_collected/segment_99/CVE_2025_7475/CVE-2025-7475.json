{"cve_id": "CVE-2025-7475", "published_date": "2025-07-12T13:15:20.973", "last_modified_date": "2025-07-18T19:04:46.877", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in code-projects Simple Car Rental System 1.0. This affects an unknown part of the file /pay.php. The manipulation of the argument mpesa leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad clasificada como crítica en code-projects Simple Car Rental System 1.0. Esta afecta a una parte desconocida del archivo /pay.php. La manipulación del argumento mpesa provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/y2xsec324/cve/issues/12", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316125", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316125", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610432", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}