{"cve_id": "CVE-2025-5678", "published_date": "2025-07-09T02:15:22.270", "last_modified_date": "2025-07-17T13:31:56.730", "descriptions": [{"lang": "en", "value": "The Gutenberg Blocks with AI by <PERSON><PERSON> WP – Page Builder Features plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the ‘redirectURL’ parameter in all versions up to, and including, 3.5.10 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Gutenberg Blocks con IA de Kadence WP – Page Builder Features para WordPress es vulnerable a cross-site scripting almacenado a través del parámetro 'redirectURL' en todas las versiones hasta la 3.5.10 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en las páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/kadence-blocks/tags/3.5.8/includes/assets/js/kb-countdown.min.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/browser/kadence-blocks/tags/3.5.8/includes/blocks/class-kadence-blocks-countdown-block.php#L605", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fc712f6b-f11b-4731-8f89-0044830400d6?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}