{"cve_id": "CVE-2025-7341", "published_date": "2025-07-15T05:15:29.883", "last_modified_date": "2025-07-16T14:29:53.053", "descriptions": [{"lang": "en", "value": "The HT Contact Form Widget For Elementor Page Builder & Gutenberg Blocks & Form Builder. plugin for WordPress is vulnerable to arbitrary file deletion due to insufficient file path validation in the temp_file_delete() function in all versions up to, and including, 2.2.1. This makes it possible for unauthenticated attackers to delete arbitrary files on the server, which can easily lead to remote code execution when the right file is deleted (such as wp-config.php)."}, {"lang": "es", "value": "El complemento HT Contact Form Widget para Elementor Page Builder y Gutenberg Blocks &amp; Form Builder para WordPress es vulnerable a la eliminación arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función temp_file_delete() en todas las versiones hasta la 2.2.1 incluida. Esto permite que atacantes no autenticados eliminen archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código al eliminar el archivo correcto (como wp-config.php)."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/ht-contactform/trunk/admin/Includes/Services/FileManager.php#L107", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset/3326887/ht-contactform/trunk/admin/Includes/Ajax.php?contextall=1&old=3316109&old_path=%2Fht-contactform%2Ftrunk%2Fadmin%2FIncludes%2FAjax.php", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/32da04ba-bee3-4fd3-b91b-57e588d5f4e4?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}