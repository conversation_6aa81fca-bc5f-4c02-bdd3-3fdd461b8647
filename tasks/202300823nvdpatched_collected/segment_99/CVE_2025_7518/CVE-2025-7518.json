{"cve_id": "CVE-2025-7518", "published_date": "2025-07-12T10:15:26.217", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The RSFirewall! plugin for WordPress is vulnerable to Path Traversal in all versions up to, and including, 1.1.42 via the get_local_filename() function. This makes it possible for authenticated attackers, with Administrator-level access and above, to read the contents of arbitrary files on the server, which can contain sensitive information."}, {"lang": "es", "value": "El complemento RSFirewall! para WordPress es vulnerable a la travesía de rutas en todas las versiones hasta la 1.1.42 incluida, mediante la función get_local_filename(). Esto permite a atacantes autenticados, con acceso de administrador o superior, leer el contenido de archivos arbitrarios en el servidor, que pueden contener información confidencial."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3306173%40rsfirewall&new=3306173%40rsfirewall&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fd7b0eef-3b8e-4272-bbd7-ad52088d0835?source=cve", "source": "<EMAIL>", "tags": []}]}