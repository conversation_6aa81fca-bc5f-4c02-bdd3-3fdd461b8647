{"cve_id": "CVE-2025-53906", "published_date": "2025-07-15T21:15:34.493", "last_modified_date": "2025-07-18T15:15:28.297", "descriptions": [{"lang": "en", "value": "Vim is an open source, command line text editor. Prior to version 9.1.1551, a path traversal issue in <PERSON><PERSON>’s zip.vim plugin can allow overwriting of arbitrary files when opening specially crafted zip archives. Impact is low because this exploit requires direct user interaction. However, successfully exploitation can lead to overwriting sensitive files or placing executable code in privileged locations, depending on the permissions of the process editing the archive. The victim must edit such a file using Vim which will reveal the filename and the file content, a careful user may suspect some strange things going on. Successful exploitation could results in the ability to execute arbitrary commands on the underlying operating system. Version 9.1.1551 contains a patch for the vulnerability."}, {"lang": "es", "value": "Vim es un editor de texto de línea de comandos de código abierto. Antes de la versión 9.1.1551, un problema de path traversal en el complemento zip.vim de Vim permitía sobrescribir archivos arbitrarios al abrir archivos zip especialmente manipulados. El impacto es bajo, ya que esta vulnerabilidad requiere la interacción directa del usuario. Sin embargo, una explotación exitosa puede provocar la sobrescritura de archivos confidenciales o la colocación de código ejecutable en ubicaciones privilegiadas, según los permisos del proceso que edita el archivo. La víctima debe editar dicho archivo con Vim, lo que revelará el nombre y el contenido del archivo; un usuario cuidadoso podría sospechar que están ocurriendo cosas extrañas. Una explotación exitosa podría permitir la ejecución de comandos arbitrarios en el sistema operativo subyacente. La versión 9.1.1551 contiene un parche para esta vulnerabilidad."}], "references": [{"url": "https://github.com/vim/vim/commit/586294a04179d855c3d1d4ee5ea83931963680b8", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/vim/vim/security/advisories/GHSA-r2fw-9cw4-mj86", "source": "<EMAIL>", "tags": []}]}