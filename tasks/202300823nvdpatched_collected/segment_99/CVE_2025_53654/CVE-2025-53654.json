{"cve_id": "CVE-2025-53654", "published_date": "2025-07-09T16:15:24.833", "last_modified_date": "2025-07-18T17:45:03.583", "descriptions": [{"lang": "en", "value": "Jenkins Statistics Gatherer Plugin 2.0.3 and earlier stores the AWS Secret Key unencrypted in its global configuration file on the Jenkins controller, where it can be viewed by users with access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins Statistics Gatherer Plugin 2.0.3 y versiones anteriores almacenan la clave secreta de AWS sin cifrar en su archivo de configuración global en el controlador de Jenkins, donde los usuarios con acceso al sistema de archivos del controlador de Jenkins pueden verla."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3554", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}