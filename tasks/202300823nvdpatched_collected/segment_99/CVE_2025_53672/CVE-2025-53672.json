{"cve_id": "CVE-2025-53672", "published_date": "2025-07-09T16:15:26.713", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "Jenkins Kryptowire Plugin 0.2 and earlier stores the Kryptowire API key unencrypted in its global configuration file on the Jenkins controller, where it can be viewed by users with access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins Kryptowire Plugin 0.2 y versiones anteriores almacenan la clave API de Kryptowire sin cifrar en su archivo de configuración global en el controlador Jenkins, donde los usuarios con acceso al sistema de archivos del controlador Jenkins pueden verla. "}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3525", "source": "jenkins<PERSON>-<EMAIL>", "tags": []}]}