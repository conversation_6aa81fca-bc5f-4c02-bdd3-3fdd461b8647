{"cve_id": "CVE-2025-7521", "published_date": "2025-07-13T06:15:26.580", "last_modified_date": "2025-07-15T20:15:51.840", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Vehicle Parking Management System 1.13. Affected is an unknown function of the file /admin/index.php. The manipulation of the argument Username leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Vehicle Parking Management System 1.13. La vulnerabilidad afecta a una función desconocida del archivo /admin/index.php. La manipulación del argumento \"Username\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado. "}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/122", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316218", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316218", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610579", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}