{"cve_id": "CVE-2025-6423", "published_date": "2025-07-12T08:15:23.367", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The BeeTeam368 Extensions plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the handle_submit_upload_file() function in all versions up to, and including, 2.3.5. This makes it possible for authenticated attackers with Subscriber-level access or higher to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento BeeTeam368 Extensions para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de validación del tipo de archivo en la función handle_submit_upload_file() en todas las versiones hasta la 2.3.5 incluida. Esto permite que atacantes autenticados con acceso de suscriptor o superior carguen archivos arbitrarios en el servidor del sitio afectado, lo que podría posibilitar la ejecución remota de código."}], "references": [{"url": "https://themeforest.net/item/vidmov-video-wordpress-theme/35542187#item-description__change-log", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/96170b82-6ed9-4a52-8592-944163cdd3cf?source=cve", "source": "<EMAIL>", "tags": []}]}