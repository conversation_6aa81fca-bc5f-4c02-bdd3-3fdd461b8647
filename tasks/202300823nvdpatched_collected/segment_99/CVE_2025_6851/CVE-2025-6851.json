{"cve_id": "CVE-2025-6851", "published_date": "2025-07-11T09:15:25.370", "last_modified_date": "2025-07-17T13:11:21.863", "descriptions": [{"lang": "en", "value": "The Broken Link Notifier plugin for WordPress is vulnerable to Server-Side Request Forgery in all versions up to, and including, 1.3.0 via the ajax_blinks() function which ultimately calls the check_url_status_code() function. This makes it possible for unauthenticated attackers to make web requests to arbitrary locations originating from the web application and can be used to query and modify information from internal services."}, {"lang": "es", "value": "El complemento Broken Link Notifier para WordPress es vulnerable a Server-Side Request Forgery en todas las versiones hasta la 1.3.0 incluida, a través de la función ajax_blinks(), que en última instancia invoca la función check_url_status_code(). Esto permite a atacantes no autenticados realizar solicitudes web a ubicaciones arbitrarias desde la aplicación web y puede utilizarse para consultar y modificar información de servicios internos."}], "references": [{"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3323864%40broken-link-notifier&new=3323864%40broken-link-notifier&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/0f76c9f8-c57a-4875-b581-f67c9c60021c?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}