{"cve_id": "CVE-2025-7534", "published_date": "2025-07-13T17:15:23.813", "last_modified_date": "2025-07-16T14:55:20.360", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Student Result Management System 2.0. It has been classified as critical. Affected is an unknown function of the file /notice-details.php of the component GET Parameter Handler. The manipulation of the argument nid leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Student Result Management System 2.0. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /notice-details.php del componente GET Parameter Handler. La manipulación del argumento nid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/4m3rr0r/PoCVulDb/blob/main/CVE-2025-7534.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316230", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316230", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.613168", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}