{"cve_id": "CVE-2025-7609", "published_date": "2025-07-14T14:15:28.937", "last_modified_date": "2025-07-15T18:12:55.213", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in code-projects Simple Shopping Cart 1.0 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /register.php. The manipulation of the argument ruser_email leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en code-projects Simple Shopping Cart 1.0, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /register.php. La manipulación del argumento ruser_email provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/zzb1388/cve/issues/18", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316309", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316309", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615360", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}