{"cve_id": "CVE-2025-7208", "published_date": "2025-07-09T01:15:50.573", "last_modified_date": "2025-07-10T13:18:53.830", "descriptions": [{"lang": "en", "value": "A vulnerability was found in 9fans plan9port up to 9da5b44. It has been classified as critical. This affects the function edump in the library /src/plan9port/src/libsec/port/x509.c. The manipulation leads to heap-based buffer overflow. The exploit has been disclosed to the public and may be used. This product takes the approach of rolling releases to provide continious delivery. Therefore, version details for affected and updated releases are not available. The identifier of the patch is b3e06559475b0130a7a2fb56ac4d131d13d2012f. It is recommended to apply a patch to fix this issue."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en plan9port de 9fans hasta la versión 9da5b44. Se ha clasificado como crítica. Afecta a la función edump de la biblioteca /src/plan9port/src/libsec/port/x509.c. La manipulación provoca un desbordamiento del búfer en el montón. Se ha hecho público el exploit y puede que sea utilizado. Este producto utiliza el enfoque de versiones continuas para garantizar una entrega continua. Por lo tanto, no se dispone de detalles de las versiones afectadas ni de las actualizadas. El identificador del parche es b3e06559475b0130a7a2fb56ac4d131d13d2012f. Se recomienda aplicar un parche para solucionar este problema."}], "references": [{"url": "https://drive.google.com/drive/folders/1kedwNLNDiFQB2OAp7S-ZKYoF7nxfIZGO?usp=sharing", "source": "<EMAIL>", "tags": []}, {"url": "https://git.9front.org/plan9front/plan9front/b3e06559475b0130a7a2fb56ac4d131d13d2012f/commit.html", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/9fans/plan9port/issues/710#issuecomment-2819906648", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/user-attachments/files/19698345/plan9port_crash_1.txt", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.259053", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.259053", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.304567", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?submit.607684", "source": "<EMAIL>", "tags": []}]}