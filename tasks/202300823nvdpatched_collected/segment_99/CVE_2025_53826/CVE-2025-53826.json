{"cve_id": "CVE-2025-53826", "published_date": "2025-07-15T18:15:24.127", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "File Browser provides a file managing interface within a specified directory and it can be used to upload, delete, preview, rename, and edit files. In version 2.39.0, File Browser’s authentication system issues long-lived JWT tokens that remain valid even after the user logs out. As of time of publication, no known patches exist."}, {"lang": "es", "value": "File Browser proporciona una interfaz de gestión de archivos dentro de un directorio específico y permite cargar, eliminar, previsualizar, renombrar y editar archivos. En la versión 2.39.0, el sistema de autenticación del Explorador de Archivos emite tokens JWT de larga duración que siguen siendo válidos incluso después de cerrar la sesión. Al momento de la publicación, no se conocían parches."}], "references": [{"url": "https://github.com/filebrowser/filebrowser/issues/5216", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/filebrowser/filebrowser/security/advisories/GHSA-7xwp-2cpp-p8r7", "source": "<EMAIL>", "tags": []}]}