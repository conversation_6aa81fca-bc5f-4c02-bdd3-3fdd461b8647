{"cve_id": "CVE-2025-6691", "published_date": "2025-07-09T06:15:23.567", "last_modified_date": "2025-07-11T21:22:46.333", "descriptions": [{"lang": "en", "value": "The SureForms – Drag and Drop Form Builder for WordPress plugin for WordPress is vulnerable to arbitrary file deletion due to insufficient file path validation in the delete_entry_files() function in all versions up to, and including, 1.7.3. This makes it possible for unauthenticated attackers to delete arbitrary files on the server, which can easily lead to remote code execution when the right file is deleted (such as wp-config.php)."}, {"lang": "es", "value": "El complemento SureForms – Drag and Drop Form Builder for WordPress para WordPress es vulnerable a la eliminación arbitraria de archivos debido a una validación insuficiente de la ruta de archivo en la función delete_entry_files() en todas las versiones hasta la 1.7.3 incluida. Esto permite que atacantes no autenticados eliminen archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código al eliminar el archivo correcto (como wp-config.php)."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/sureforms/trunk/admin/views/entries-list-table.php#L661", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3319753%40sureforms&new=3319753%40sureforms&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/sureforms/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/b4658546-bf57-414b-a3c9-bf7a5692c5fe?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}