{"cve_id": "CVE-2025-53663", "published_date": "2025-07-09T16:15:25.740", "last_modified_date": "2025-07-18T17:29:15.720", "descriptions": [{"lang": "en", "value": "Jenkins IBM Cloud DevOps Plugin 2.0.16 and earlier stores SonarQube authentication tokens unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins IBM Cloud DevOps Plugin 2.0.16 y versiones anteriores almacenan tokens de autenticación de SonarQube sin cifrar en archivos config.xml de trabajo en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador de Jenkins pueden verlos."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3552", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}