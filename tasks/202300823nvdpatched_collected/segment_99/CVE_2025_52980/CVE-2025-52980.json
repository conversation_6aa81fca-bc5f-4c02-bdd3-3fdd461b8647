{"cve_id": "CVE-2025-52980", "published_date": "2025-07-11T16:15:24.647", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A Use of Incorrect Byte Ordering \n\nvulnerability \n\nin the Routing Protocol Daemon (rpd) of Juniper Networks Junos OS on SRX300 Series allows an unauthenticated, network-based attacker to cause a Denial-of-Service (DoS).\n\n\n\nWhen a BGP update is received over an established BGP session which contains a specific, valid, optional, transitive path attribute, rpd will crash and restart.\n\nThis issue affects eBGP and iBGP over IPv4 and IPv6.\n\n\n\nThis issue affects:\n\nJunos OS:\n\n\n\n  *  22.1 versions from 22.1R1 before 22.2R3-S4,\n  *  22.3 versions before 22.3R3-S3,\n  *  22.4 versions before 22.4R3-S2,\n  *  23.2 versions before 23.2R2,\n  *  23.4 versions before 23.4R2."}, {"lang": "es", "value": "Una vulnerabilidad de ordenamiento incorrecto de bytes en el daemon de protocolo de enrutamiento (rpd) del sistema operativo Junos de Juniper Networks en la serie SRX300 permite que un atacante no autenticado basado en la red provoque una denegación de servicio (DoS). Cuando se recibe una actualización de BGP a través de una sesión BGP establecida que contiene un atributo de ruta transitiva específico, válido y opcional, el rpd se bloquea y se reinicia. Este problema afecta a eBGP e iBGP sobre IPv4 e IPv6. Este problema afecta a: Junos OS: * versiones 22.1 a partir de 22.1R1 anteriores a 22.2R3-S4, * versiones 22.3 anteriores a 22.3R3-S3, * versiones 22.4 anteriores a 22.4R3-S2, * versiones 23.2 anteriores a 23.2R2, * versiones 23.4 anteriores a 23.4R2."}], "references": [{"url": "https://supportportal.juniper.net/JSA100084", "source": "<EMAIL>", "tags": []}]}