{"cve_id": "CVE-2025-53632", "published_date": "2025-07-10T20:15:27.673", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Chall-Manager is a platform-agnostic system able to start Challenges on Demand of a player. When decoding a scenario (i.e. a zip archive), the path of the file to write is not checked, potentially leading to zip slips. Exploitation does not require authentication nor authorization, so anyone can exploit it. It should nonetheless not be exploitable as it is highly recommended to bury Chall-Manager deep within the infrastructure due to its large capabilities, so no users could reach the system. Patch has been implemented by commit 47d188f and shipped in v0.1.4."}, {"lang": "es", "value": "Chall-Manager es un sistema independiente de la plataforma, capaz de iniciar Desafíos a petición del jugador. Al decodificar un escenario (por ejemplo, un archivo zip), no se verifica la ruta del archivo a escribir, lo que podría provocar errores de zip. Su explotación no requiere autenticación ni autorización, por lo que cualquiera puede hacerlo. Sin embargo, no debería ser explotable, ya que se recomienda encarecidamente integrar Chall-Manager en la infraestructura debido a sus amplias capacidades, de modo que ningún usuario pueda acceder al sistema. El parche se implementó mediante el commit 47d188f y se publicó en la versión v0.1.4."}], "references": [{"url": "https://github.com/ctfer-io/chall-manager/commit/47d188fda5e3f86285e820f12ad9fb6f9930662c", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ctfer-io/chall-manager/releases/tag/v0.1.4", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/ctfer-io/chall-manager/security/advisories/GHSA-3gv2-v3jx-r9fh", "source": "<EMAIL>", "tags": []}]}