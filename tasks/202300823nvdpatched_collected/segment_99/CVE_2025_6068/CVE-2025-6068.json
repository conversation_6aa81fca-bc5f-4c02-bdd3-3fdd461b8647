{"cve_id": "CVE-2025-6068", "published_date": "2025-07-11T08:15:24.280", "last_modified_date": "2025-07-17T13:11:47.847", "descriptions": [{"lang": "en", "value": "The FooGallery – Responsive Photo Gallery, Image Viewer, Justified, Masonry & Carousel plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the `data-caption-title` & `data-caption-description` HTML attributes in all versions up to, and including, 2.4.31 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Contributor-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento FooGallery – Responsive Photo Gallery, Image Viewer, Justified, Masonry &amp; Carousel para WordPress es vulnerable a cross-site scripting almacenado a través de los atributos HTML `data-caption-title` y `data-caption-description` en todas las versiones hasta la 2.4.31 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de colaborador o superior, inyectar scripts web arbitrarios en las páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/foogallery/trunk/extensions/default-templates/shared/js/foogallery.min.js", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3322251%40foogallery&new=3322251%40foogallery&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/a6be4aaa-f8a1-42d6-95c1-062c5ca51004?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}