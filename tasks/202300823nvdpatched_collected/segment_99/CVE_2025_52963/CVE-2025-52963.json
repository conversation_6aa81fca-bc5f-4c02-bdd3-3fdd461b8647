{"cve_id": "CVE-2025-52963", "published_date": "2025-07-11T15:15:26.817", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "An Improper Access Control vulnerability in the User Interface (UI) of Juniper Networks Junos OS allows a local, low-privileged attacker to bring down an interface, leading to a Denial-of-Service.\n\nUsers with \"view\" permissions can run a specific request interface command which allows the user to shut down the interface.\nThis issue affects Junos OS: \n\n\n  *  All versions before 21.2R3-S9, \n  *  from 21.4 before 21.4R3-S11, \n  *  from 22.2 before 22.2R3-S7,\n  *  from 22.4 before 22.4R3-S7, \n  *  from 23.2 before 23.2R2-S4,\n  *  from 23.4 before 23.4R2-S5,  \n  *  from 24.2 before 24.2R2-S1, \n  *  from 24.4 before 24.4R1-S3, 24.4R2."}, {"lang": "es", "value": "Una vulnerabilidad de control de acceso inadecuado en la interfaz de usuario (IU) de Juniper Networks Junos OS permite a un atacante local con pocos privilegios desactivar una interfaz, lo que provoca una denegación de servicio (DPS). Los usuarios con permisos de vista pueden ejecutar un comando de solicitud de interfaz específico que permite desactivarla. Este problema afecta a Junos OS: * Todas las versiones anteriores a 21.2R3-S9, * desde 21.4 hasta 21.4R3-S11, * desde 22.2 hasta 22.2R3-S7, * desde 22.4 hasta 22.4R3-S7, * desde 23.2 hasta 23.2R2-S4, * desde 23.4 hasta 23.4R2-S5, * desde 24.2 hasta 24.2R2-S1, * desde 24.4 hasta 24.4R1-S3, 24.4R2."}], "references": [{"url": "https://supportportal.juniper.net/JSA100078", "source": "<EMAIL>", "tags": []}]}