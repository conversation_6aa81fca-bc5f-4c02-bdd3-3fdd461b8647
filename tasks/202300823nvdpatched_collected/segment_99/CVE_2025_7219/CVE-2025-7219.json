{"cve_id": "CVE-2025-7219", "published_date": "2025-07-09T06:15:25.690", "last_modified_date": "2025-07-11T16:27:22.900", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Campcodes Payroll Management System 1.0. It has been classified as critical. Affected is an unknown function of the file /ajax.php?action=delete_allowances. The manipulation of the argument ID leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en Campcodes Payroll Management System 1.0. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /ajax.php?action=delete_allowances. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/skyrainoh/CVE/issues/7", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.315168", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315168", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608263", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}