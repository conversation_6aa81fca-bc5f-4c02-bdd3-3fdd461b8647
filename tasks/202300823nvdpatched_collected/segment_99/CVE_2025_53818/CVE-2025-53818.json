{"cve_id": "CVE-2025-53818", "published_date": "2025-07-14T21:15:27.983", "last_modified_date": "2025-07-15T20:15:49.850", "descriptions": [{"lang": "en", "value": "GitHub Kanban MCP Server is a Model Context Protocol (MCP) server for managing GitHub issues in Kanban board format and streamlining LLM task management. Version 0.3.0 of the MCP Server is written in a way that is vulnerable to command injection vulnerability attacks as part of some of its MCP Server tool definition and implementation. The MCP Server exposes the tool `add_comment` which relies on Node.js child process API `exec` to execute the GitHub (`gh`) command, is an unsafe and vulnerable API if concatenated with untrusted user input. As of time of publication, no known patches are available."}, {"lang": "es", "value": "GitHub Kanban MCP Server es un servidor de Protocolo de Contexto de Modelo (MCP) para gestionar incidencias de GitHub en formato de tablero Kanban y optimizar la gestión de tareas LLM. Las versiones 0.3.0 y 0.4.0 del servidor MCP están escritas de forma que son vulnerables a ataques de vulnerabilidad de inyección de comandos como parte de la definición e implementación de algunas de sus herramientas. El servidor MCP expone la herramienta `add_comment`, que se basa en la API `exec` del proceso secundario de Node.js para ejecutar el comando de GitHub (`gh`). Esta API es insegura y vulnerable si se concatena con entradas de usuario no confiables. Al momento de la publicación, no se conocían parches disponibles."}], "references": [{"url": "https://github.com/Sunwood-ai-labs/github-kanban-mcp-server/blob/main/src/handlers/comment-handlers.ts#L8", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Sunwood-ai-labs/github-kanban-mcp-server/blob/main/src/handlers/tool-handlers.ts#L79", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Sunwood-ai-labs/github-kanban-mcp-server/blob/v0.4.0/src/handlers/comment-handlers.ts#L8-L23", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/Sunwood-ai-labs/github-kanban-mcp-server/security/advisories/GHSA-6jx8-rcjx-vmwf", "source": "<EMAIL>", "tags": []}]}