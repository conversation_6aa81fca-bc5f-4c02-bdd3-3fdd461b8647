{"cve_id": "CVE-2025-6057", "published_date": "2025-07-12T05:15:21.223", "last_modified_date": "2025-07-16T14:57:56.460", "descriptions": [{"lang": "en", "value": "The WPBookit plugin for WordPress is vulnerable to arbitrary file uploads due to missing file type validation in the handle_image_upload() function in all versions up to, and including, 1.0.4. This makes it possible for authenticated attackers, with Subscriber-level access and above, to upload arbitrary files on the affected site's server which may make remote code execution possible."}, {"lang": "es", "value": "El complemento WPBookit para WordPress es vulnerable a la carga de archivos arbitrarios debido a la falta de validación del tipo de archivo en la función handle_image_upload() en todas las versiones hasta la 1.0.4 incluida. Esto permite que atacantes autenticados, con acceso de suscriptor o superior, carguen archivos arbitrarios en el servidor del sitio afectado, lo que podría posibilitar la ejecución remota de código."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/wpbookit/trunk/core/admin/classes/controllers/class.wpb-profile-controller.php#L85", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3326098%40wpbookit&new=3326098%40wpbookit&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://wordpress.org/plugins/wpbookit/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/fac81cc0-c6c9-4009-aacb-52adc70c0261?source=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}