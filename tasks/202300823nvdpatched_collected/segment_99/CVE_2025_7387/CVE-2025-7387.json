{"cve_id": "CVE-2025-7387", "published_date": "2025-07-10T06:15:22.503", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "The Lana Downloads Manager plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the endpoint parameters in versions up to, and including, 1.10.0 due to insufficient input sanitization and output escaping on user supplied attributes. This makes it possible for authenticated attackers with administrator-level and above permissions to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Lana Downloads Manager para WordPress es vulnerable a Cross-Site Scripting almacenado a través de los parámetros del endpoint en versiones hasta la 1.10.0 (incluida), debido a una depuración de entrada insuficiente y al escape de salida en los atributos proporcionados por el usuario. Esto permite a atacantes autenticados con permisos de administrador o superiores inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/lana-downloads-manager/tags/1.10.0/lana-downloads-manager.php#L763", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset/3324923/lana-downloads-manager", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/be0db9ff-dc95-4c92-8dc4-472c5df9c0dd?source=cve", "source": "<EMAIL>", "tags": []}]}