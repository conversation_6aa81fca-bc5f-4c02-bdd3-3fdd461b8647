{"cve_id": "CVE-2025-7483", "published_date": "2025-07-12T18:15:21.230", "last_modified_date": "2025-07-15T18:07:12.243", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Vehicle Parking Management System 1.13. It has been rated as critical. This issue affects some unknown processing of the file /users/forgot-password.php. The manipulation of the argument email leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13. Se ha clasificado como crítica. Este problema afecta a un procesamiento desconocido del archivo /users/forgot-password.php. La manipulación del argumento \"email\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/114", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316133", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316133", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610571", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610581", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}