{"cve_id": "CVE-2025-7581", "published_date": "2025-07-14T07:15:24.787", "last_modified_date": "2025-07-16T14:34:37.467", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, has been found in code-projects Voting System 1.0. Affected by this issue is some unknown functionality of the file /admin/positions_edit.php. The manipulation of the argument ID leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad clasificada como crítica en code-projects Voting System 1.0. Este problema afecta a una funcionalidad desconocida del archivo /admin/positions_edit.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/i-Corner/cve/issues/8", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316280", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316280", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615029", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}