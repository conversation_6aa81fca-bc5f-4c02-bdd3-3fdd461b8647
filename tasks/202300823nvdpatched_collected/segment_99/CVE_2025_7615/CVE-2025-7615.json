{"cve_id": "CVE-2025-7615", "published_date": "2025-07-14T16:15:27.150", "last_modified_date": "2025-07-16T14:30:53.370", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical was found in TOTOLINK T6 4.1.5cu.748. Affected by this vulnerability is the function clearPairCfg of the file /cgi-bin/cstecgi.cgi of the component HTTP POST Request Handler. The manipulation of the argument ip leads to command injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en TOTOLINK T6 4.1.5cu.748. Esta vulnerabilidad afecta la función clearPairCfg del archivo /cgi-bin/cstecgi.cgi del componente HTTP POST Request Handler. La manipulación del argumento ip provoca la inyección de comandos. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/6.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/6.md#poc", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.316315", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316315", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615369", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}