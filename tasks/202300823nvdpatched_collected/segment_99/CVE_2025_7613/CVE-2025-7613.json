{"cve_id": "CVE-2025-7613", "published_date": "2025-07-14T15:15:25.183", "last_modified_date": "2025-07-16T14:32:05.310", "descriptions": [{"lang": "en", "value": "A vulnerability was found in TOTOLINK T6 4.1.5cu.748. It has been rated as critical. This issue affects the function CloudSrvVersionCheck of the file /cgi-bin/cstecgi.cgi of the component HTTP POST Request Handler. The manipulation of the argument ip leads to command injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en TOTOLINK T6 4.1.5cu.748. Se ha clasificado como crítica. Este problema afecta a la función CloudSrvVersionCheck del archivo /cgi-bin/cstecgi.cgi del componente HTTP POST Request Handler. La manipulación del argumento ip provoca la inyección de comandos. El ataque puede iniciarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/4.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/ElvisBlue/Public/blob/main/Vuln/4.md#poc", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://vuldb.com/?ctiid.316313", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316313", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615367", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.totolink.net/", "source": "<EMAIL>", "tags": ["Product"]}]}