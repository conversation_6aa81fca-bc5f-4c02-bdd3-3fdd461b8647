{"cve_id": "CVE-2025-7504", "published_date": "2025-07-12T09:15:26.187", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "The Friends plugin for WordPress is vulnerable to PHP Object Injection in version 3.5.1 via deserialization of untrusted input of the query_vars parameter This makes it possible for authenticated attackers, with subscriber-level access and above, to inject a PHP Object. No known POP chain is present in the vulnerable software, which means this vulnerability has no impact unless another plugin or theme containing a POP chain is installed on the site. If a POP chain is present via an additional plugin or theme installed on the target system, it may allow the attacker to perform actions like delete arbitrary files, retrieve sensitive data, or execute code depending on the POP chain present. This requires access to the sites SALT_NONCE and and SALT_KEY to exploit."}, {"lang": "es", "value": "El complemento Friends para WordPress es vulnerable a la inyección de objetos PHP en la versión 3.5.1 mediante la deserialización de la entrada no confiable del parámetro query_vars. Esto permite a atacantes autenticados, con acceso de suscriptor o superior, inyectar un objeto PHP. No existe ninguna cadena POP conocida en el software vulnerable, lo que significa que esta vulnerabilidad no tiene impacto a menos que se instale en el sitio otro complemento o tema que contenga una cadena POP. Si existe una cadena POP a través de un complemento o tema adicional instalado en el sistema objetivo, puede permitir al atacante realizar acciones como eliminar archivos arbitrarios, recuperar datos confidenciales o ejecutar código según la cadena POP presente. Esto requiere acceso a los sitios SALT_NONCE y SALT_KEY para explotarlo."}], "references": [{"url": "https://drive.google.com/file/d/1K-_AcDk9BhUa0kSQ_M-UUnLgmnYJTA0l/view", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/akirk/friends/pull/537", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3306684%40friends&new=3306684%40friends&sfp_email=&sfph_mail=", "source": "<EMAIL>", "tags": []}, {"url": "https://wordpress.org/plugins/friends/", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/cf91d75e-cef4-4154-aa16-6ca96db9c5bb?source=cve", "source": "<EMAIL>", "tags": []}]}