{"cve_id": "CVE-2025-53019", "published_date": "2025-07-14T20:15:29.043", "last_modified_date": "2025-07-15T20:15:48.003", "descriptions": [{"lang": "en", "value": "ImageMagick is free and open-source software used for editing and manipulating digital images. In versions prior to 7.1.2-0 and 6.9.13-26, in ImageMagick's `magick stream` command, specifying multiple consecutive `%d` format specifiers in a filename template causes a memory leak. Versions 7.1.2-0 and 6.9.13-26 fix the issue."}, {"lang": "es", "value": "ImageMagick es un software gratuito y de código abierto que se utiliza para editar y manipular imágenes digitales. En versiones anteriores a la 7.1.2-0 y la 6.9.13-26, en el comando `magick stream` de ImageMagick, especificar varios especificadores de formato `%d` consecutivos en una plantilla de nombre de archivo causaba una pérdida de memoria. Las versiones 7.1.2-0 y 6.9.13-26 corrigieron el problema."}], "references": [{"url": "https://github.com/ImageMagick/ImageMagick/security/advisories/GHSA-cfh4-9f7v-fhrc", "source": "<EMAIL>", "tags": []}]}