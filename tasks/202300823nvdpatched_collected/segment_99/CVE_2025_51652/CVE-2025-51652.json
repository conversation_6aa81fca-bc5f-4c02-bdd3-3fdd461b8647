{"cve_id": "CVE-2025-51652", "published_date": "2025-07-14T17:15:33.563", "last_modified_date": "2025-07-15T16:57:18.243", "descriptions": [{"lang": "en", "value": "SemCms v5.0 was discovered to contain a SQL injection vulnerability via the pid parameter at SEMCMS_Categories.php."}, {"lang": "es", "value": "Se descubrió que SemCms v5.0 contenía una vulnerabilidad de inyección SQL a través del parámetro pid en SEMCMS_Categories.php."}], "references": [{"url": "http://semcms.com", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://github.com/Y4y17/CVE/blob/main/SemCms/SQL_Injection_1.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.sem-cms.com/", "source": "<EMAIL>", "tags": ["Product"]}]}