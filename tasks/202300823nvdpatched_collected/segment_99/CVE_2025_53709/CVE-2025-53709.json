{"cve_id": "CVE-2025-53709", "published_date": "2025-07-10T19:15:27.220", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Secure-upload is a data submission service that validates single-use tokens when accepting submissions to channels. The service only installed on a small number of environments.\n\nUnder specific circumstances, privileged users of secure-upload could have selected email templates not necessarily created for their enrollment when sending data upload requests.\nAuthenticated and privileged users of one enrollment could have abused an endpoint to redirect existing submission channels to a dataset they control.\nAn endpoint handling domain validation allowed unauthenticated users to enumerate existing enrollments.\nFinally, other endpoints allowed enumerating if a resource with a known RID exists across enrollments.\n\nThe affected service has been patched with version 0.815.0 and automatically deployed to all Apollo-managed Foundry instances."}, {"lang": "es", "value": "Secure-upload es un servicio de envío de datos que valida tokens de un solo uso al aceptar envíos a canales. El servicio solo se instaló en un número reducido de entornos. En circunstancias específicas, usuarios privilegiados de secure-upload podrían haber seleccionado plantillas de correo electrónico no necesariamente creadas para su inscripción al enviar solicitudes de carga de datos. Usuarios autenticados y privilegiados de una inscripción podrían haber utilizado incorrectamente un endpoint para redirigir los canales de envío existentes a un conjunto de datos que controlan. Un endpoint que gestionaba la validación del dominio permitía a usuarios no autenticados enumerar las inscripciones existentes. Finalmente, otros endpoints permitían enumerar si un recurso con un RID conocido existía en las inscripciones. El servicio afectado se ha actualizado con la versión 0.815.0 y se ha implementado automáticamente en todas las instancias de Foundry administradas por Apollo."}], "references": [{"url": "https://cwe.mitre.org/data/definitions/285.html", "source": "<EMAIL>", "tags": []}]}