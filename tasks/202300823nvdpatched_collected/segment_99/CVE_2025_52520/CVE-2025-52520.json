{"cve_id": "CVE-2025-52520", "published_date": "2025-07-10T19:15:25.570", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "For some unlikely configurations of multipart upload, an Integer Overflow vulnerability in Apache Tomcat could lead to a DoS via bypassing of size limits.\n\nThis issue affects Apache Tomcat: from 11.0.0-M1 through 11.0.8, from 10.1.0-M1 through 10.1.42, from 9.0.0.M1 through 9.0.106.\n\nUsers are recommended to upgrade to version 11.0.9, 10.1.43 or 9.0.107, which fix the issue."}, {"lang": "es", "value": "En algunas configuraciones improbables de carga multiparte, una vulnerabilidad de desbordamiento de enteros en Apache Tomcat podría provocar un ataque de denegación de servicio (DoS) al eludir los límites de tamaño. Este problema afecta a Apache Tomcat: de 11.0.0-M1 a 11.0.8, de 10.1.0-M1 a 10.1.42, y de 9.0.0.M1 a 9.0.106. Se recomienda actualizar a las versiones 11.0.9, 10.1.43 o 9.0.107, que solucionan el problema."}], "references": [{"url": "https://lists.apache.org/thread/trqq01bbxw6c92zx69kx2mw2qgmfy0o5", "source": "<EMAIL>", "tags": []}]}