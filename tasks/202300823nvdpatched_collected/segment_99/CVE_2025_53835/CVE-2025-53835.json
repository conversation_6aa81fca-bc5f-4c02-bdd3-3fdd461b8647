{"cve_id": "CVE-2025-53835", "published_date": "2025-07-14T23:15:25.090", "last_modified_date": "2025-07-15T20:15:50.740", "descriptions": [{"lang": "en", "value": "XWiki Rendering is a generic rendering system that converts textual input in a given syntax (wiki syntax, HTML, etc) into another syntax (XHTML, etc). Starting in version 5.4.5 and prior to version 14.10, the XHTML syntax depended on the `xdom+xml/current` syntax which allows the creation of raw blocks that permit the insertion of arbitrary HTML content including JavaScript. This allows XSS attacks for users who can edit a document like their user profile (enabled by default). This has been fixed in version 14.10 by removing the dependency on the `xdom+xml/current` syntax from the XHTML syntax. Note that the `xdom+xml` syntax is still vulnerable to this attack. As it's main purpose is testing and its use is quite difficult, this syntax shouldn't be installed or used on a regular wiki. There are no known workarounds apart from upgrading."}, {"lang": "es", "value": "XWiki Rendering es un sistema de renderizado genérico que convierte la entrada de texto en una sintaxis dada (sintaxis wiki, HTML, etc.) en otra sintaxis (XHTML, etc.). A partir de la versión 5.4.5 y antes de la versión 14.10, la sintaxis XHTML dependía de la sintaxis `xdom+xml/current`, que permite la creación de bloques sin procesar que permiten la inserción de contenido HTML arbitrario, incluyendo JavaScript. Esto permite ataques XSS para usuarios que pueden editar un documento como su perfil de usuario (habilitado por defecto). Esto se ha corregido en la versión 14.10 eliminando la dependencia de la sintaxis `xdom+xml/current` de la sintaxis XHTML. Tenga en cuenta que la sintaxis `xdom+xml` sigue siendo vulnerable a este ataque. Como su propósito principal es la prueba y su uso es bastante difícil, esta sintaxis no debe instalarse ni usarse en una wiki normal. No hay soluciones alternativas conocidas aparte de la actualización."}], "references": [{"url": "https://github.com/xwiki/xwiki-rendering/commit/a4ca31f99f524b9456c64150d6f375984aa81ea7", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/xwiki/xwiki-rendering/security/advisories/GHSA-w3wh-g4m9-783p", "source": "<EMAIL>", "tags": []}, {"url": "https://jira.xwiki.org/browse/XRENDERING-660", "source": "<EMAIL>", "tags": []}]}