{"cve_id": "CVE-2025-53620", "published_date": "2025-07-09T19:15:24.427", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "@builder.io/qwik-city is the meta-framework for Qwik. When a Qwik Server Action QRL is executed it dynamically load the file containing the symbol. When an invalid qfunc is sent, the server does not handle the thrown error. The error then causes Node JS to exit. This vulnerability is fixed in 1.13.0."}, {"lang": "es", "value": "@builder.io/qwik-city es el metaframework de Qwik. Al ejecutar un QRL de acción de servidor de Qwik, este carga dinámicamente el archivo que contiene el símbolo. Si se envía una función qfunc no válida, el servidor no gestiona el error generado. Este error provoca la salida de NodeJS. Esta vulnerabilidad se corrigió en la versión 1.13.0."}], "references": [{"url": "https://github.com/QwikDev/qwik/security/advisories/GHSA-qr9h-j6xg-2j72", "source": "<EMAIL>", "tags": []}]}