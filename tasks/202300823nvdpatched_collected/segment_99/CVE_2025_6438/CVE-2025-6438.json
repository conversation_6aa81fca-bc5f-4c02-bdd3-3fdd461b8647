{"cve_id": "CVE-2025-6438", "published_date": "2025-07-11T09:15:24.987", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "A\n\n\n\n\n\n\n\nCWE-611: Improper Restriction of XML External Entity Reference vulnerability exists that could\ncause manipulation of SOAP API calls and XML external entities injection resulting in unauthorized file access\nwhen the server is accessed via the network using an application account."}, {"lang": "es", "value": "CWE-611: Existe una vulnerabilidad de restricción incorrecta de referencia de entidad externa XML que podría causar la manipulación de llamadas a la API SOAP y la inyección de entidades externas XML, lo que resulta en un acceso no autorizado a archivos cuando se accede al servidor a través de la red utilizando una cuenta de aplicación."}], "references": [{"url": "https://download.schneider-electric.com/files?p_Doc_Ref=SEVD-2025-189-01&p_enDocType=Security+and+Safety+Notice&p_File_Name=SEVD-2025-189-01.pdf", "source": "<EMAIL>", "tags": []}]}