{"cve_id": "CVE-2025-7413", "published_date": "2025-07-10T20:15:28.797", "last_modified_date": "2025-07-16T15:01:55.337", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in code-projects Library System 1.0. This affects an unknown part of the file /user/teacher/profile.php. The manipulation of the argument image leads to unrestricted upload. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en code-projects Library System 1.0. Esta afecta a una parte desconocida del archivo /user/teacher/profile.php. La manipulación del argumento \"image\" permite la carga sin restricciones. Es posible iniciar el ataque de forma remota. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/rom4j/cve/issues/16", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315873", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315873", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608533", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}