{"cve_id": "CVE-2025-7367", "published_date": "2025-07-15T05:15:30.247", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "The Strong Testimonials plugin for WordPress is vulnerable to Stored Cross-Site Scripting via the Testimonial Custom Fields in all versions up to, and including, 3.2.11 due to insufficient input sanitization and output escaping. This makes it possible for authenticated attackers, with Author-level access and above, to inject arbitrary web scripts in pages that will execute whenever a user accesses an injected page."}, {"lang": "es", "value": "El complemento Strong Testimonials para WordPress es vulnerable a Cross-Site Scripting Almacenado a través de los campos personalizados de testimonios en todas las versiones hasta la 3.2.11 incluida, debido a una depuración de entrada y un escape de salida insuficientes. Esto permite a atacantes autenticados, con acceso de autor o superior, inyectar scripts web arbitrarios en páginas que se ejecutarán al acceder un usuario a una página inyectada."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/strong-testimonials/tags/3.2.11/includes/functions-template.php#L317", "source": "<EMAIL>", "tags": []}, {"url": "https://plugins.trac.wordpress.org/browser/strong-testimonials/tags/3.2.11/includes/functions-template.php#L532", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/65395034-0b20-462c-93ee-e755e5c888a4?source=cve", "source": "<EMAIL>", "tags": []}]}