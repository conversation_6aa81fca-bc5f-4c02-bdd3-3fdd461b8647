{"cve_id": "CVE-2025-7612", "published_date": "2025-07-14T15:15:24.977", "last_modified_date": "2025-07-15T17:07:43.390", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Mobile Shop 1.0. It has been declared as critical. This vulnerability affects unknown code of the file /login.php. The manipulation of the argument email leads to sql injection. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Mobile Shop 1.0. Se ha declarado crítica. Esta vulnerabilidad afecta al código desconocido del archivo /login.php. La manipulación del argumento \"email\" provoca una inyección SQL. El ataque puede iniciarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/zzb1388/cve/issues/21", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316312", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316312", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615363", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}