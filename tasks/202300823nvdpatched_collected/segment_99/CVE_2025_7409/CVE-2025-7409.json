{"cve_id": "CVE-2025-7409", "published_date": "2025-07-10T17:15:48.763", "last_modified_date": "2025-07-16T15:03:03.007", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects Mobile Shop 1.0 and classified as critical. This issue affects some unknown processing of the file /LoginAsAdmin.php. The manipulation of the argument email leads to sql injection. The attack may be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects Mobile Shop 1.0, clasificada como crítica. Este problema afecta a un procesamiento desconocido del archivo /LoginAsAdmin.php. La manipulación del argumento \"email\" provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/rom4j/cve/issues/20", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.315869", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.315869", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.608518", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}