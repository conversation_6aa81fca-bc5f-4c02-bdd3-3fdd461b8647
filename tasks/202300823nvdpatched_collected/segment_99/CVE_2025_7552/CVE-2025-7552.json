{"cve_id": "CVE-2025-7552", "published_date": "2025-07-14T00:15:25.093", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Dromara Northstar up to 7.3.5. It has been rated as critical. Affected by this issue is the function preHandle of the file northstar-main/src/main/java/org/dromara/northstar/web/interceptor/AuthorizationInterceptor.java of the component Path Handler. The manipulation of the argument Request leads to improper access controls. The attack may be launched remotely. Upgrading to version 7.3.6 is able to address this issue. The patch is identified as 8d521bbf531de59b09b8629a9cbf667870ad2541. It is recommended to upgrade the affected component."}, {"lang": "es", "value": "Se detectó una vulnerabilidad en Dromara Northstar hasta la versión 7.3.5. Se ha clasificado como crítica. Este problema afecta a la función preHandle del archivo northstar-main/src/main/java/org/dromara/northstar/web/interceptor/AuthorizationInterceptor.java del componente Path Handler. La manipulación del argumento Request genera controles de acceso inadecuados. El ataque puede ejecutarse en remoto. Actualizar a la versión 7.3.6 puede solucionar este problema. El parche se identifica como 8d521bbf531de59b09b8629a9cbf667870ad2541. Se recomienda actualizar el componente afectado."}], "references": [{"url": "https://gitee.com/dromara/northstar/commit/8d521bbf531de59b09b8629a9cbf667870ad2541", "source": "<EMAIL>", "tags": []}, {"url": "https://gitee.com/dromara/northstar/issues/ICCQ4E", "source": "<EMAIL>", "tags": []}, {"url": "https://gitee.com/dromara/northstar/issues/ICCQ4E#note_42855013_link", "source": "<EMAIL>", "tags": []}, {"url": "https://gitee.com/dromara/northstar/releases/tag/v7.3.6", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?ctiid.316250", "source": "<EMAIL>", "tags": []}, {"url": "https://vuldb.com/?id.316250", "source": "<EMAIL>", "tags": []}]}