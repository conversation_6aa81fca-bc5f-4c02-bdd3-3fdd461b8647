{"cve_id": "CVE-2025-7548", "published_date": "2025-07-13T23:15:23.283", "last_modified_date": "2025-07-15T18:32:54.253", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in Tenda FH1201 ********(408) and classified as critical. This vulnerability affects the function formSafeEmailFilter of the file /goform/SafeEmailFilter. The manipulation of the argument page leads to stack-based buffer overflow. The attack can be initiated remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad en Tenda FH1201 ********(408), clasificada como crítica. Esta vulnerabilidad afecta a la función formSafeEmailFilter del archivo /goform/SafeEmailFilter. La manipulación de la página de argumentos provoca un desbordamiento del búfer en la pila. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/panda666-888/vuls/blob/main/tenda/fh1201/formSafeEmailFilter.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/panda666-888/vuls/blob/main/tenda/fh1201/formSafeEmailFilter.md#poc", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://vuldb.com/?ctiid.316246", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316246", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.614658", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.tenda.com.cn/", "source": "<EMAIL>", "tags": ["Product"]}]}