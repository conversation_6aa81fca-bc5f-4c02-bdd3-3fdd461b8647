{"cve_id": "CVE-2025-7605", "published_date": "2025-07-14T13:15:25.853", "last_modified_date": "2025-07-16T14:32:22.870", "descriptions": [{"lang": "en", "value": "A vulnerability was found in code-projects AVL Rooms 1.0. It has been rated as critical. Affected by this issue is some unknown functionality of the file /profile.php. The manipulation of the argument first_name leads to sql injection. The attack may be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en code-projects AVL Rooms 1.0. Se ha clasificado como crítica. Este problema afecta a una funcionalidad desconocida del archivo /profile.php. La manipulación del argumento first_name provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://code-projects.org/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/sunhuiHi666/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316304", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316304", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615340", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}