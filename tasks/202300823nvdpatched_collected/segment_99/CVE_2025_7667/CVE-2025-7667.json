{"cve_id": "CVE-2025-7667", "published_date": "2025-07-15T12:15:22.990", "last_modified_date": "2025-07-15T13:14:24.053", "descriptions": [{"lang": "en", "value": "The Restrict File Access plugin for WordPress is vulnerable to Cross-Site Request Forgery in all versions up to, and including, 1.1.2. This is due to missing or incorrect nonce validation on the 'restrict-file-access' page. This makes it possible for unauthenticated attackers to to delete arbitrary files on the server, which can easily lead to remote code execution when the right file is deleted (such as wp-config.php), via a forged request granted they can trick a site administrator into performing an action such as clicking on a link."}, {"lang": "es", "value": "El complemento Restrict File Access para WordPress es vulnerable a Cross-Site Request Forgery en todas las versiones hasta la 1.1.2 incluida. Esto se debe a la falta o la validación incorrecta de nonce en la página \"restrict-file-access\". Esto permite a atacantes no autenticados eliminar archivos arbitrarios en el servidor, lo que puede provocar fácilmente la ejecución remota de código al eliminar el archivo correcto (como wp-config.php). Mediante una solicitud falsificada, pueden engañar al administrador del sitio para que realice una acción como hacer clic en un enlace."}], "references": [{"url": "https://plugins.trac.wordpress.org/browser/restrict-file-access/trunk/admin/admin.php#L78", "source": "<EMAIL>", "tags": []}, {"url": "https://www.wordfence.com/threat-intel/vulnerabilities/id/e1105717-134b-48cc-960d-f78437c06793?source=cve", "source": "<EMAIL>", "tags": []}]}