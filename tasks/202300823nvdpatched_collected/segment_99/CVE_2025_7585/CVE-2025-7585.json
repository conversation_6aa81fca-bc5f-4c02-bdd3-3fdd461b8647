{"cve_id": "CVE-2025-7585", "published_date": "2025-07-14T08:15:24.527", "last_modified_date": "2025-07-15T18:30:04.437", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Fire Reporting System 1.2. It has been classified as critical. Affected is an unknown function of the file /admin/manage-site.php. The manipulation of the argument webtitle leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Fire Reporting System 1.2. Se ha clasificado como crítica. Se ve afectada una función desconocida del archivo /admin/manage-site.php. La manipulación del argumento \"webtitle\" provoca una inyección SQL. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/133", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316284", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316284", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615040", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}