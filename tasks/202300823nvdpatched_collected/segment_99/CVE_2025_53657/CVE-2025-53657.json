{"cve_id": "CVE-2025-53657", "published_date": "2025-07-09T16:15:25.137", "last_modified_date": "2025-07-18T17:34:28.230", "descriptions": [{"lang": "en", "value": "Jenkins ReadyAPI Functional Testing Plugin 1.11 and earlier does not mask SLM License Access Keys, client secrets, and passwords displayed on the job configuration form, increasing the potential for attackers to observe and capture them."}, {"lang": "es", "value": "Jenkins ReadyAPI Functional Testing Plugin 1.11 y versiones anteriores no enmascara las claves de acceso de licencia SLM, los secretos de cliente y las contraseñas que se muestran en el formulario de configuración del trabajo, lo que aumenta la posibilidad de que los atacantes los observen y capturen."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3556", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}