{"cve_id": "CVE-2025-53668", "published_date": "2025-07-09T16:15:26.260", "last_modified_date": "2025-07-18T18:46:26.840", "descriptions": [{"lang": "en", "value": "Jenkins VAddy Plugin 1.2.8 and earlier stores Vaddy API Auth Keys unencrypted in job config.xml files on the Jenkins controller, where they can be viewed by users with Item/Extended Read permission or access to the Jenkins controller file system."}, {"lang": "es", "value": "Jenkins VAddy Plugin 1.2.8 y versiones anteriores almacenan claves de autenticación de API de Vaddy sin cifrar en archivos job config.xml en el controlador de Jenkins, donde los usuarios con permiso de lectura extendida/de elemento o acceso al sistema de archivos del controlador de Jenkins pueden verlas."}], "references": [{"url": "https://www.jenkins.io/security/advisory/2025-07-09/#SECURITY-3527", "source": "jenkins<PERSON>-<EMAIL>", "tags": ["Vendor Advisory"]}]}