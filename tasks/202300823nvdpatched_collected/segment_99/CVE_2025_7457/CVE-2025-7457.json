{"cve_id": "CVE-2025-7457", "published_date": "2025-07-11T21:15:25.063", "last_modified_date": "2025-07-16T14:58:56.130", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in Campcodes Online Movie Theater Seat Reservation System 1.0. This affects an unknown part of the file /admin/manage_movie.php. The manipulation of the argument ID leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en Campcodes Online Movie Theater Seat Reservation System 1.0. Esta afecta a una parte desconocida del archivo /admin/manage_movie.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/N1n3b9S/cve/issues/5", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316101", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316101", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.609490", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}