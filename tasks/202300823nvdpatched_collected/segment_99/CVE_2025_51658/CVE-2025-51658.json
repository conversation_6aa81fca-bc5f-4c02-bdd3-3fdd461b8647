{"cve_id": "CVE-2025-51658", "published_date": "2025-07-14T17:15:34.487", "last_modified_date": "2025-07-15T16:42:05.260", "descriptions": [{"lang": "en", "value": "SemCms v5.0 was discovered to contain a SQL injection vulnerability via the ID parameter at SEMCMS_InquiryView.php."}, {"lang": "es", "value": "Se descubrió que SemCms v5.0 contenía una vulnerabilidad de inyección SQL a través del parámetro ID en SEMCMS_InquiryView.php."}], "references": [{"url": "http://semcms.com", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://github.com/Y4y17/CVE/blob/main/SemCms/SQL_Injection_7.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.sem-cms.com/", "source": "<EMAIL>", "tags": ["Product"]}]}