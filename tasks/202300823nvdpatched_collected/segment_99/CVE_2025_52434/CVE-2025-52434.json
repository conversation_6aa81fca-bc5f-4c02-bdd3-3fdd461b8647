{"cve_id": "CVE-2025-52434", "published_date": "2025-07-10T19:15:25.220", "last_modified_date": "2025-07-15T13:14:49.980", "descriptions": [{"lang": "en", "value": "Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition') vulnerability in Apache Tomcat when using the APR/Native connector. This was particularly noticeable with client initiated closes of HTTP/2 connections.\n\nThis issue affects Apache Tomcat: from 9.0.0.M1 through 9.0.106.\n\nUsers are recommended to upgrade to version 9.0.107, which fixes the issue."}, {"lang": "es", "value": "Vulnerabilidad de ejecución concurrente mediante recursos compartidos con sincronización incorrecta («Condición de ejecución») en Apache Tomcat al usar el conector APR/Nativo. Esto era especialmente evidente con el cierre de conexiones HTTP/2 iniciado por el cliente. Este problema afecta a Apache Tomcat desde la versión 9.0.0.M1 hasta la 9.0.106. Se recomienda actualizar a la versión 9.0.107, que soluciona el problema."}], "references": [{"url": "https://lists.apache.org/thread/gxgh65004f25y8519coth6w7vchww030", "source": "<EMAIL>", "tags": []}]}