{"cve_id": "CVE-2025-7204", "published_date": "2025-07-09T15:15:25.283", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "In ConnectWise PSA versions older than 2025.9, a\nvulnerability exists where authenticated users could gain access to sensitive\nuser information. Specific API requests were found to return an overly verbose\nuser object, which included encrypted password hashes for other users.\nAuthenticated users could then retrieve these hashes. \n\n\n\nAn\nattacker or privileged user could then use these exposed hashes to conduct\noffline brute-force or dictionary attacks. Such attacks could lead to\ncredential compromise, allowing unauthorized access to accounts, and\npotentially privilege escalation within the system."}, {"lang": "es", "value": "En versiones de ConnectWise PSA anteriores a la 2025.9, existe una vulnerabilidad que permite a los usuarios autenticados acceder a información confidencial. Se detectó que ciertas solicitudes de API devolvían un objeto de usuario excesivamente detallado, que incluía hashes de contraseñas cifradas de otros usuarios. Los usuarios autenticados podían recuperar estos hashes. Un atacante o un usuario con privilegios podría utilizar estos hashes expuestos para realizar ataques de fuerza bruta o de diccionario sin conexión. Estos ataques podrían comprometer las credenciales, lo que permitiría el acceso no autorizado a las cuentas y, potencialmente, la escalada de privilegios dentro del sistema."}], "references": [{"url": "https://www.connectwise.com/company/trust/security-bulletins/connectwise-psa-2025.9-security-fix", "source": "7d616e1a-3288-43b1-a0dd-0a65d3e70a49", "tags": []}, {"url": "https://www.themissinglink.com.au/security-advisories/cve-2025-7204", "source": "7d616e1a-3288-43b1-a0dd-0a65d3e70a49", "tags": []}]}