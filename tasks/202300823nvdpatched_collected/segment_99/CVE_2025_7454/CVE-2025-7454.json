{"cve_id": "CVE-2025-7454", "published_date": "2025-07-11T19:15:24.033", "last_modified_date": "2025-07-16T14:59:25.893", "descriptions": [{"lang": "en", "value": "A vulnerability classified as critical has been found in Campcodes Online Movie Theater Seat Reservation System 1.0. Affected is an unknown function of the file /admin/manage_theater.php. The manipulation of the argument ID leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha detectado una vulnerabilidad crítica en Campcodes Online Movie Theater Seat Reservation System 1.0. Se ve afectada una función desconocida del archivo /admin/manage_theater.php. La manipulación del ID del argumento provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/N1n3b9S/cve/issues/2", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://vuldb.com/?ctiid.316098", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316098", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.609487", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://www.campcodes.com/", "source": "<EMAIL>", "tags": ["Product"]}]}