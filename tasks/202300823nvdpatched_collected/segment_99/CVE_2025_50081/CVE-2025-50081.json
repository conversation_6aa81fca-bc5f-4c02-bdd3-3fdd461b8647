{"cve_id": "CVE-2025-50081", "published_date": "2025-07-15T20:15:43.840", "last_modified_date": "2025-07-17T12:30:54.713", "descriptions": [{"lang": "en", "value": "Vulnerability in the MySQL Client product of Oracle MySQL (component: Client: mysqldump).  Supported versions that are affected are 8.0.0-8.0.42, 8.4.0-8.4.5 and  9.0.0-9.3.0. Di<PERSON><PERSON><PERSON> to exploit vulnerability allows high privileged attacker with network access via multiple protocols to compromise MySQL Client.  Successful attacks require human interaction from a person other than the attacker. Successful attacks of this vulnerability can result in  unauthorized update, insert or delete access to some of MySQL Client accessible data as well as  unauthorized read access to a subset of MySQL Client accessible data. CVSS 3.1 Base Score 3.1 (Confidentiality and Integrity impacts).  CVSS Vector: (CVSS:3.1/AV:N/AC:H/PR:H/UI:R/S:U/C:L/I:L/A:N)."}, {"lang": "es", "value": "Vulnerabilidad en el producto MySQL Client de Oracle MySQL (componente: Cliente: mysqldump). Las versiones compatibles afectadas son 8.0.0-8.0.42, 8.4.0-8.4.5 y 9.0.0-9.3.0. <PERSON><PERSON> vulnerabilidad, difícil de explotar, permite a un atacante con privilegios elevados y acceso a la red a través de múltiples protocolos comprometer el MySQL Client. Los ataques exitosos requieren la interacción humana de una persona distinta al atacante. Los ataques exitosos de esta vulnerabilidad pueden resultar en acceso no autorizado a actualizaciones, inserciones o eliminaciones de datos accesibles del MySQL Client así como acceso no autorizado a lecturas de un subconjunto de dichos datos. Puntuación base CVSS 3.1: 3.1 (Afecta a la confidencialidad y la integridad). Vector CVSS: (CVSS:3.1/AV:N/AC:H/PR:H/UI:R/S:U/C:L/I:L/A:N)."}], "references": [{"url": "https://www.oracle.com/security-alerts/cpujul2025.html", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}