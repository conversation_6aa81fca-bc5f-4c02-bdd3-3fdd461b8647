{"cve_id": "CVE-2025-53621", "published_date": "2025-07-15T15:15:25.517", "last_modified_date": "2025-07-15T20:07:28.023", "descriptions": [{"lang": "en", "value": "DSpace open source software is a repository application which provides durable access to digital resources. Two related XML External Entity (XXE) injection possibilities impact all versions of DSpace prior to 7.6.4, 8.2, and 9.1. External entities are not disabled when parsing XML files during import of an archive (in Simple Archive Format), either from command-line (`./dspace import` command) or from the \"Batch Import (Zip)\" user interface feature. External entities are also not explicitly disabled when parsing XML responses from some upstream services (ArXiv, Crossref, OpenAIRE, Creative Commons) used in import from external sources via the user interface or REST API. An XXE injection in these files may result in a connection being made to an attacker's site or a local path readable by the Tomcat user, with content potentially being injected into a metadata field. In the latter case, this may result in sensitive content disclosure, including retrieving arbitrary files or configurations from the server where DSpace is running. The Simple Archive Format (SAF) importer / Batch Import (Zip) is only usable by site administrators (from user interface / REST API) or system administrators (from command-line). Therefore, to exploit this vulnerability, the malicious payload would have to be provided by an attacker and trusted by an administrator, who would trigger the import. The fix is included in DSpace 7.6.4, 8.2, and 9.1. Please upgrade to one of these versions. For those who cannot upgrade immediately, it is possible to manually patch the DSpace backend. One may also apply some best practices, though the protection provided is not as complete as upgrading. Administrators must carefully inspect any SAF archives (they did not construct themselves) before importing. As necessary, affected external services can be disabled to mitigate the ability for payloads to be delivered via external service APIs."}, {"lang": "es", "value": "El software de código abierto DSpace es una aplicación de repositorio que proporciona acceso duradero a recursos digitales. Dos posibilidades relacionadas de inyección de Entidades Externas XML (XXE) afectan a todas las versiones de DSpace anteriores a las 7.6.4, 8.2 y 9.1. Las entidades externas no se deshabilitan al analizar archivos XML durante la importación de un archivo (en formato de archivo simple), ya sea desde la línea de comandos (comando `./dspace import`) o desde la función de interfaz de usuario \"Importación por lotes (Zip)\". Las entidades externas tampoco se deshabilitan explícitamente al analizar respuestas XML de algunos servicios upstream (ArXiv, Crossref, OpenAIRE, Creative Commons) utilizados en la importación desde fuentes externas a través de la interfaz de usuario o la API REST. Una inyección XXE en estos archivos puede provocar una conexión al sitio web de un atacante o a una ruta local legible para el usuario de Tomcat, con la posibilidad de inyectar contenido en un campo de metadatos. En este último caso, esto puede provocar la divulgación de contenido sensible, incluyendo la recuperación de archivos o configuraciones arbitrarias del servidor donde se ejecuta DSpace. El importador de formato de archivo simple (SAF)/importación por lotes (Zip) solo puede ser utilizado por administradores de sitio (desde la interfaz de usuario/API REST) o administradores de sistema (desde la línea de comandos). Por lo tanto, para explotar esta vulnerabilidad, el payload malicioso tendría que ser proporcionado por un atacante y contar con la confianza de un administrador, quien activaría la importación. La solución está incluida en DSpace 7.6.4, 8.2 y 9.1. Actualice a una de estas versiones. Si no puede actualizar inmediatamente, puede aplicar un parche manual al backend de DSpace. También se pueden aplicar algunas prácticas recomendadas, aunque la protección proporcionada no es tan completa como la de una actualización. Los administradores deben inspeccionar cuidadosamente los archivos SAF (que no hayan creado ellos mismos) antes de importar. Según sea necesario, se pueden deshabilitar los servicios externos afectados para mitigar la posibilidad de que los payloads se entreguen a través de las API de servicios externos."}], "references": [{"url": "https://github.com/DSpace/DSpace/pull/11032", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11032.patch", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11034", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11034.patch", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11035", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/pull/11035.patch", "source": "<EMAIL>", "tags": []}, {"url": "https://github.com/DSpace/DSpace/security/advisories/GHSA-jjwr-5cfh-7xwh", "source": "<EMAIL>", "tags": []}]}