{"cve_id": "CVE-2025-7559", "published_date": "2025-07-14T02:15:22.143", "last_modified_date": "2025-07-15T20:15:54.533", "descriptions": [{"lang": "en", "value": "A vulnerability was found in PHPGurukul Online Fire Reporting System 1.2. It has been classified as critical. This affects an unknown part of the file /admin/bwdates-report-result.php. The manipulation of the argument fromdate/todate leads to sql injection. It is possible to initiate the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad en PHPGurukul Online Fire Reporting System 1.2. Se ha clasificado como crítica. Afecta una parte desconocida del archivo /admin/bwdates-report-result.php. La manipulación del argumento fromdate/todate provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/125", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316257", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316257", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615032", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}