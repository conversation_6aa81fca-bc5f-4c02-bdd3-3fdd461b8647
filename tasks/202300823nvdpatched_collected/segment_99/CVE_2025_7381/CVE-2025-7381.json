{"cve_id": "CVE-2025-7381", "published_date": "2025-07-09T16:15:27.890", "last_modified_date": "2025-07-10T13:17:30.017", "descriptions": [{"lang": "en", "value": "ImpactThis is an information disclosure vulnerability originating from PHP's base image. This vulnerability exposes the PHP version through an X-Powered-By header, which attackers could exploit to fingerprint the server and identify potential weaknesses.\n\nWorkaroundsThe mitigation requires changing the expose_php variable from \"On\" to \"Off\" in the file located at /usr/local/etc/php/php.ini."}, {"lang": "es", "value": "ImpactThis es una vulnerabilidad de divulgación de información que se origina en la imagen base de PHP. Esta vulnerabilidad expone la versión de PHP mediante un encabezado X-Powered-By, que los atacantes podrían explotar para identificar el servidor e identificar posibles vulnerabilidades. Soluciones alternativas: La mitigación requiere cambiar la variable expose_php de \"On\" a \"Off\" en el archivo ubicado en /usr/local/etc/php/php.ini."}], "references": [{"url": "https://github.com/mautic/docker-mautic/security/advisories/GHSA-89jm-p7jf-x8jx", "source": "<EMAIL>", "tags": []}]}