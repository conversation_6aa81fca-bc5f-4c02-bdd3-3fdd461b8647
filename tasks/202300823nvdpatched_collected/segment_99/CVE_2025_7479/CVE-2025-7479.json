{"cve_id": "CVE-2025-7479", "published_date": "2025-07-12T16:15:22.337", "last_modified_date": "2025-07-15T18:08:06.073", "descriptions": [{"lang": "en", "value": "A vulnerability has been found in PHPGurukul Vehicle Parking Management System 1.13 and classified as critical. Affected by this vulnerability is an unknown functionality of the file /users/view--detail.php. The manipulation of the argument viewid leads to sql injection. The attack can be launched remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en PHPGurukul Vehicle Parking Management System 1.13, clasificada como crítica. Esta vulnerabilidad afecta a una funcionalidad desconocida del archivo /users/view--detail.php. La manipulación del argumento viewid provoca una inyección SQL. El ataque puede ejecutarse en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/109", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316129", "source": "<EMAIL>", "tags": ["Permissions Required"]}, {"url": "https://vuldb.com/?id.316129", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.610567", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}