{"cve_id": "CVE-2025-7591", "published_date": "2025-07-14T09:15:24.580", "last_modified_date": "2025-07-15T18:28:38.160", "descriptions": [{"lang": "en", "value": "A vulnerability, which was classified as critical, was found in PHPGurukul Dairy Farm Shop Management System 1.3. Affected is an unknown function of the file view-invoice.php. The manipulation of the argument invid leads to sql injection. It is possible to launch the attack remotely. The exploit has been disclosed to the public and may be used."}, {"lang": "es", "value": "Se encontró una vulnerabilidad clasificada como crítica en PHPGurukul Dairy Farm Shop Management System 1.3. Se ve afectada una función desconocida del archivo view-invoice.php. La manipulación del argumento invid provoca una inyección SQL. Es posible ejecutar el ataque en remoto. Se ha hecho público el exploit y puede que sea utilizado."}], "references": [{"url": "https://github.com/f1rstb100d/myCVE/issues/137", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}, {"url": "https://phpgurukul.com/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://vuldb.com/?ctiid.316290", "source": "<EMAIL>", "tags": ["Permissions Required", "VDB Entry"]}, {"url": "https://vuldb.com/?id.316290", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "https://vuldb.com/?submit.615231", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}]}