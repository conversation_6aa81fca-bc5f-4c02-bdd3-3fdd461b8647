#!/bin/bash

# CVE补丁对提取器 - 处理所有segment
# 只保留C/C++项目，使用mystique标准

GITHUB_TOKEN="*********************************************************************************************"
OUTPUT_FILE="mystique_cpp_patch_pairs.json"

echo "🚀 开始运行C/C++补丁对提取器..."
echo "处理目标: 所有segment目录"
echo "输出文件: $OUTPUT_FILE"
echo "开始时间: $(date)"
echo ""

# 检查是否存在segment目录
SEGMENT_COUNT=$(ls -d segment_* 2>/dev/null | wc -l)
if [ "$SEGMENT_COUNT" -eq 0 ]; then
    echo "❌ 未找到任何segment目录"
    exit 1
fi

echo "📁 找到 $SEGMENT_COUNT 个segment目录:"
ls -d segment_* | while read dir; do
    CVE_COUNT=$(find "$dir" -name "trace_result.json" | wc -l)
    echo "  - $dir: $CVE_COUNT 个CVE"
done
echo ""

# 运行提取器
echo "🔍 开始处理所有CVE的补丁信息..."
python3 patch_pair_extractor.py \
    --all-segments \
    --github-token "$GITHUB_TOKEN" \
    --output "$OUTPUT_FILE" 2>&1 | tee extraction_output.log

# 检查结果
echo ""
echo "📊 处理结果："
if [ -f "$OUTPUT_FILE" ]; then
    echo "✅ 输出文件已生成: $OUTPUT_FILE"
    echo "📏 文件大小: $(du -h $OUTPUT_FILE | cut -f1)"
    
    # 统计补丁对数量
    if command -v jq >/dev/null 2>&1; then
        TOTAL_PAIRS=$(jq '.collection_info.total_patch_pairs // 0' "$OUTPUT_FILE" 2>/dev/null || echo "0")
        echo "🎯 C/C++补丁对数量: $TOTAL_PAIRS"
        
        # 显示前3个项目名称
        echo "📦 涉及的项目（前3个）:"
        jq -r '.patch_pairs[0:3][] | "  - \(.projects.source.name) (\(.projects.source.language))"' "$OUTPUT_FILE" 2>/dev/null || echo "  无法解析项目信息"
    else
        TOTAL_PAIRS=$(grep -o '"total_patch_pairs": [0-9]*' "$OUTPUT_FILE" | grep -o '[0-9]*' | head -1)
        echo "🎯 C/C++补丁对数量: ${TOTAL_PAIRS:-0}"
    fi
    
    echo ""
    echo "📋 补丁对样例（前2个）:"
    head -100 "$OUTPUT_FILE" | tail -80
    
else
    echo "❌ 输出文件不存在"
fi

echo ""
echo "⏰ 完成时间: $(date)"
echo "📝 详细日志: extraction_output.log" 