现在我希望你帮我收集一下时间线在2023年8月23日到2025年7月24日的补丁，要求如下：
1. 时间线：2023年8月23日到2025年7月24日

2. 输出格式：

```json

        {
        "unified_id": "{cve_id}_{source_project}_{target_project}_{target_version_hash}", // target_version_hash如果无，可以用所在版本号代替
        "cve_id": "CVE-2018-1118",
        "patch_type": "cross_repo|same_repo", // 明确标识补丁类型
        "projects": {
            "source": {
            "name": "Linux Kernel",
            "repo": "https://github.com/torvalds/linux",
            "language": "C"
            },
            "target": {
            "name": "Linux Kernel",
            "repo": "https://github.com/torvalds/linux",
            "language": "C"
            }
        },
        "versions": {
            "source": { // 有的项目vulnerable和patched只给出其中一个信息。若为空 使用unknown表示即可
            "vulnerable": {"version": "4.17", "commit": "55e49dc43a835b19567e62142cb1c87dc7db7b3c"}, // 有的项目version和commit只存在其一，如果为空用unknown标识
            "patched": {"version": "unknown", "commit": "unknown"} // 有的项目version和commit只存在其一，如果为空用unknown标识
            },
            "target": {
            "vulnerable": {"version": "unknown", "commit": "unknown"}, // 有的项目version和commit只存在其一，如果为空用unknown标识
            "patched": {"version": "4.9", "commit": "a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99"}
            }
        },
        "source_dataset": "FixMorph",
        "metadata": {
            // 该字段根据项目的特点走，不必统一
        }
        }

```

3. 等价补丁筛选标准：需要确定这个和已有等价补丁收集的代码是否一致或有冲突的地方，如果没冲突，那就通过已有代码得到等价补丁，然后根据我的以下要求再做整理和筛选即可。
    - **时间划分:** 根据 CodeLlama 的发布日期（2023 年 8 月 23 日），将在此日期**之前**的 CVE 补丁对用于**微调**，在此日期**之后**的用于**补丁移植评估**。这保证了评估数据对于微调模型是“未见过”的。
    - mystique原文描述:
        electing CVE Patch Pairs. Following PPatHF [35], we manually filtered commits unrelated to the vulnerability, and further refined the commits that involved files unrelated to C or modifications outside functions. After filtering, we sorted all commits by timestamp, and selected the earliest and latest commit for each vulnerability to form a CVE patch pair. The earliest commits are regarded as the original patch and the latest commits are regarded as the target patch. If the vulnerability only contains one single patch, we also excluded it. After this process, we finalized CVEs with their corresponding patch pairs. ([p11](zotero://open-pdf/library/items/XXN57HCB?page=11&annotation=EH3UC4BV)) 选举CVE补丁对。继PPatHF[35]之后，我们手动过滤了与漏洞无关的提交，并进一步细化了涉及与C无关的文件或函数外部修改的提交。过滤后，我们按时间戳对所有提交进行排序，并为每个漏洞选择最早和最新的提交，形成CVE补丁对。最早的提交被视为原始补丁，最新的提交被视为目标补丁。如果漏洞只包含单个补丁，我们也将其排除在外。经过这个过程，我们最终确定了CVE及其对应的补丁对。【👈 译】
4. 项目：C/C++项目