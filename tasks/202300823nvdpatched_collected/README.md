# NVD补丁收集器 - CVE补丁对收集任务

## 任务概述

本任务实现了基于时间范围的CVE补丁对收集功能，专门收集2023年8月23日到2025年7月24日期间的CVE补丁对，并按照mystique论文的标准进行筛选和格式化。

## 功能特点

### 1. 时间范围收集
- 收集时间范围：2023年8月23日到2025年7月24日
- 基于CVE发布日期进行筛选
- 支持自定义时间范围

### 2. 筛选标准
- **项目类型**：仅收集C/C++项目的补丁
- **补丁数量**：遵循mystique论文标准，只保留包含2个或以上补丁的CVE
- **补丁配对**：选择最早和最新的提交形成补丁对
  - 最早的提交作为原始补丁(source_patch)
  - 最新的提交作为目标补丁(target_patch)

### 3. 输出格式
严格按照需求文档中指定的JSON格式输出：

```json
{
  "unified_id": "{cve_id}_{source_project}_{target_project}_{target_version_hash}",
  "cve_id": "CVE-2018-1118",
  "patch_type": "cross_repo|same_repo",
  "projects": {
    "source": {
      "name": "Linux Kernel",
      "repo": "https://github.com/torvalds/linux",
      "language": "C"
    },
    "target": {
      "name": "Linux Kernel", 
      "repo": "https://github.com/torvalds/linux",
      "language": "C"
    }
  },
  "versions": {
    "source": {
      "vulnerable": {"version": "4.17", "commit": "55e49dc43a835b19567e62142cb1c87dc7db7b3c"},
      "patched": {"version": "unknown", "commit": "unknown"}
    },
    "target": {
      "vulnerable": {"version": "unknown", "commit": "unknown"},
      "patched": {"version": "4.9", "commit": "a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99"}
    }
  },
  "source_dataset": "NVD_PatchTracker",
  "metadata": {
    "total_patches_found": 5,
    "high_confidence_patches": 3,
    "collection_timestamp": "2025-01-21T12:00:00"
  }
}
```

## 文件结构

```
tasks/202300823nvdpatched_collected/
├── README.md                    # 本文档
├── 需求描述.md                  # 原始需求文档
├── nvd_patch_collector.py       # 主收集脚本
├── run_collection.sh            # 运行脚本
├── nvd_patch_pairs.json         # 输出结果文件
└── logs/                        # 日志目录
    ├── 2025-01-21.log          # 运行日志
    └── error_2025-01-21.log    # 错误日志
```

## 使用方法

### 1. 环境准备

确保系统已安装Python 3.7+和必要的依赖包。

### 2. 快速运行

```bash
# 进入任务目录
cd tasks/202300823nvdpatched_collected

# 给运行脚本执行权限
chmod +x run_collection.sh

# 执行收集任务
./run_collection.sh
```

### 3. 自定义参数运行

```bash
# 直接运行Python脚本
python3 nvd_patch_collector.py \
    --output-dir . \
    --start-date 2023-08-23 \
    --end-date 2025-07-24 \
    --max-cves 100 \
    --max-depth 2 \
    --confidence 0.7
```

### 4. 参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--output-dir` | 输出目录 | tasks/202300823nvdpatched_collected |
| `--start-date` | 开始日期 | 2023-08-23 |
| `--end-date` | 结束日期 | 2025-07-24 |
| `--max-cves` | 最大处理CVE数量 | 50 |
| `--max-depth` | 最大跟踪深度 | 2 |
| `--confidence` | 置信度阈值 | 0.7 |
| `--github-token` | GitHub API令牌 | 从配置文件读取 |
| `--config` | 配置文件路径 | auto |

## 技术实现

### 1. 依赖系统
- 基于现有的2024WL_SBOM系统
- 使用PatchTracer进行补丁发现
- 使用CVEReferenceFetcher获取CVE引用

### 2. 核心流程
1. **CVE获取**：从NVD等数据源获取指定时间范围内的CVE列表
2. **引用收集**：为每个CVE收集初始引用链接
3. **补丁跟踪**：使用补丁跟踪器发现相关补丁
4. **项目筛选**：过滤出C/C++项目的补丁
5. **补丁配对**：按照mystique标准选择补丁对
6. **格式转换**：将结果转换为指定的JSON格式

### 3. C/C++项目识别
通过以下方式识别C/C++项目：
- 项目名称关键词匹配（linux, kernel, gcc, openssl等）
- 仓库URL关键词匹配
- 修改文件扩展名检查（.c, .cpp, .h等）

## 输出说明

### 1. 主要输出文件
- `nvd_patch_pairs.json`：收集到的补丁对数据
- `logs/YYYY-MM-DD.log`：详细运行日志
- `logs/error_YYYY-MM-DD.log`：错误日志

### 2. 统计信息
输出文件包含以下统计信息：
- 总补丁对数量
- 收集时间
- 筛选标准
- 时间范围等

## 注意事项

1. **API限制**：GitHub API有速率限制，建议配置GitHub令牌
2. **运行时间**：完整收集可能需要较长时间，建议先用小数量测试
3. **网络依赖**：需要稳定的网络连接访问各种数据源
4. **存储空间**：确保有足够的磁盘空间存储结果和日志

## 故障排除

### 1. 常见错误
- **导入错误**：检查Python路径设置和依赖安装
- **网络超时**：检查网络连接，考虑增加超时时间
- **API限制**：配置正确的API令牌

### 2. 日志查看
```bash
# 查看最新运行日志
tail -f logs/$(date +%Y-%m-%d).log

# 查看错误日志
tail -f logs/error_$(date +%Y-%m-%d).log
```

## 扩展功能

### 1. 支持更多语言
可以修改`is_cpp_project`方法支持其他编程语言。

### 2. 自定义筛选标准
可以调整`extract_patch_pairs`方法实现不同的补丁选择策略。

### 3. 输出格式扩展
可以修改`format_to_required_json`方法添加更多元数据字段。 