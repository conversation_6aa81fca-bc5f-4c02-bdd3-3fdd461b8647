#!/usr/bin/env python3
"""
CVE补丁对提取器
根据mystique标准从trace_result.json文件中提取补丁对信息
使用GitHub API和NVD API补充缺失的信息
"""

import json
import re
import os
import sys
import requests
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatchPairExtractor:
    def __init__(self, github_token: str, output_file: str):
        """
        初始化补丁对提取器
        
        Args:
            github_token: GitHub API令牌
            output_file: 输出文件路径
        """
        self.github_token = github_token
        self.output_file = output_file
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'token {github_token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'CVE-Patch-Collector/1.0'
        })
        
        # 初始化输出文件
        self._init_output_file()
        
        # API访问统计
        self.api_calls_count = 0
        self.max_api_calls_per_hour = 4000  # GitHub API限制
        
    def _init_output_file(self):
        """初始化输出文件，写入JSON数组开始标记"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "collection_info": {
                    "created_at": datetime.now().isoformat(),
                    "mystique_standard": True,
                    "min_patches_per_cve": 2,
                    "format_version": "1.0"
                },
                "patch_pairs": []
            }, f, indent=2, ensure_ascii=False)
        logger.info(f"初始化输出文件: {self.output_file}")
    
    def extract_patches_from_summary(self, patch_summary: str) -> List[Dict]:
        """
        从patch_summary文本中提取补丁信息
        
        Args:
            patch_summary: 补丁摘要文本
            
        Returns:
            List[Dict]: 提取的补丁信息列表
        """
        patches = []
        
        # 使用正则表达式匹配补丁信息
        # 匹配格式：### N. https://github.com/...
        pattern = r'### (\d+)\. (https://github\.com/[^\n]+)'
        matches = re.findall(pattern, patch_summary)
        
        for match in matches:
            patch_number, url = match
            
            # 过滤掉非提交URL（如PR页面、tree页面等）
            if '/commit' in url and url.endswith(('.json', '.patch', '.diff')) == False:
                # 提取提交哈希
                commit_hash = self._extract_commit_hash(url)
                if commit_hash:
                    # 提取仓库信息
                    repo_info = self._extract_repo_from_url(url)
                    if repo_info:
                        patches.append({
                            'url': url,
                            'commit_hash': commit_hash,
                            'repo_owner': repo_info['owner'],
                            'repo_name': repo_info['name'],
                            'repo_full_name': f"{repo_info['owner']}/{repo_info['name']}"
                        })
        
        logger.info(f"从patch_summary中提取到 {len(patches)} 个有效补丁")
        return patches
    
    def is_cpp_project(self, repo_info: Dict, cve_data: Dict = None) -> bool:
        """
        判断是否为C/C++项目
        
        Args:
            repo_info: 仓库信息
            cve_data: CVE详细数据（可选）
            
        Returns:
            bool: 是否为C/C++项目
        """
        # GitHub API返回的主要语言
        language = repo_info.get('language', '').lower()
        if language in ['c', 'c++', 'cpp']:
            return True
        
        # 仓库名称关键词检查
        repo_name = repo_info.get('name', '').lower()
        repo_full_name = repo_info.get('full_name', '').lower()
        
        cpp_indicators = [
            # 系统级项目
            'linux', 'kernel', 'glibc', 'gcc', 'clang', 'llvm',
            # 数据库和服务器
            'mysql', 'postgresql', 'postgres', 'sqlite', 'redis', 'nginx', 'apache',
            # 加密和安全库
            'openssl', 'mbedtls', 'wolfssl', 'boringssl', 'crypto',
            # 网络和通信
            'curl', 'libcurl', 'ssh', 'openssh', 'net-snmp',
            # 图形和媒体
            'opencv', 'ffmpeg', 'imagemagick', 'gstreamer',
            # 工具和库
            'git', 'vim', 'emacs', 'zlib', 'libjpeg', 'libpng', 'pcre',
            # 嵌入式和IoT
            'firmware', 'embedded', 'microcontroller'
        ]
        
        for indicator in cpp_indicators:
            if indicator in repo_name or indicator in repo_full_name:
                return True
        
        # CVE描述关键词检查
        if cve_data:
            descriptions = cve_data.get('descriptions', [])
            for desc in descriptions:
                desc_text = desc.get('value', '').lower()
                # 检查C/C++相关的描述
                if any(keyword in desc_text for keyword in ['buffer overflow', 'memory corruption', 
                                                           'use after free', 'double free', 'heap overflow',
                                                           'stack overflow', 'null pointer dereference']):
                    return True
        
        return False
    
    def _extract_commit_hash(self, url: str) -> Optional[str]:
        """从GitHub URL中提取提交哈希"""
        # 匹配 /commit/hash 或 /commits/hash
        pattern = r'/commits?/([a-f0-9]{40})'
        match = re.search(pattern, url)
        return match.group(1) if match else None
    
    def _extract_repo_from_url(self, url: str) -> Optional[Dict]:
        """从GitHub URL中提取仓库信息"""
        # 匹配 github.com/owner/repo
        pattern = r'github\.com/([^/]+)/([^/]+)'
        match = re.search(pattern, url)
        if match:
            return {
                'owner': match.group(1),
                'name': match.group(2)
            }
        return None
    
    def get_commit_info(self, repo_full_name: str, commit_hash: str) -> Optional[Dict]:
        """
        从GitHub API获取提交信息
        
        Args:
            repo_full_name: 完整仓库名 (owner/repo)
            commit_hash: 提交哈希
            
        Returns:
            Dict: 提交信息
        """
        try:
            # API限制检查
            if self.api_calls_count >= self.max_api_calls_per_hour:
                logger.warning("已达到GitHub API调用限制，等待1小时...")
                time.sleep(3600)
                self.api_calls_count = 0
            
            url = f"https://api.github.com/repos/{repo_full_name}/commits/{commit_hash}"
            response = self.session.get(url)
            self.api_calls_count += 1
            
            if response.status_code == 200:
                commit_data = response.json()
                return {
                    'sha': commit_data['sha'],
                    'date': commit_data['commit']['committer']['date'],
                    'message': commit_data['commit']['message'],
                    'author': commit_data['commit']['author']['name'],
                    'url': commit_data['html_url'],
                    'stats': commit_data.get('stats', {}),
                    'files': len(commit_data.get('files', []))
                }
            elif response.status_code == 404:
                logger.warning(f"提交不存在: {repo_full_name}#{commit_hash}")
            else:
                logger.error(f"GitHub API错误 {response.status_code}: {repo_full_name}#{commit_hash}")
                
        except Exception as e:
            logger.error(f"获取提交信息失败: {repo_full_name}#{commit_hash} - {e}")
        
        return None
    
    def get_cve_data(self, cve_id: str) -> Optional[Dict]:
        """
        从CVE文件或NVD API获取CVE详细信息
        
        Args:
            cve_id: CVE ID
            
        Returns:
            Dict: CVE详细信息
        """
        # 首先尝试从本地文件读取
        try:
            # 查找CVE文件
            import glob
            cve_files = glob.glob(f"*/{cve_id}/*{cve_id}.json")
            if cve_files:
                with open(cve_files[0], 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.debug(f"从本地文件读取CVE数据失败: {e}")
        
        # 如果本地文件不存在，尝试从NVD API获取（这里可以扩展）
        logger.info(f"未找到 {cve_id} 的本地数据文件")
        return None
    
    def get_repo_languages(self, repo_full_name: str) -> Dict:
        """
        获取仓库的语言分布信息
        
        Args:
            repo_full_name: 完整仓库名 (owner/repo)
            
        Returns:
            Dict: 语言分布信息
        """
        try:
            if self.api_calls_count >= self.max_api_calls_per_hour:
                logger.warning("已达到GitHub API调用限制，等待1小时...")
                time.sleep(3600)
                self.api_calls_count = 0
            
            url = f"https://api.github.com/repos/{repo_full_name}/languages"
            response = self.session.get(url)
            self.api_calls_count += 1
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.debug(f"获取语言信息失败 {response.status_code}: {repo_full_name}")
                
        except Exception as e:
            logger.debug(f"获取语言信息失败: {repo_full_name} - {e}")
        
        return {}
    
    def get_repo_info(self, repo_full_name: str) -> Optional[Dict]:
        """
        从GitHub API获取仓库信息
        
        Args:
            repo_full_name: 完整仓库名 (owner/repo)
            
        Returns:
            Dict: 仓库信息
        """
        try:
            # API限制检查
            if self.api_calls_count >= self.max_api_calls_per_hour:
                logger.warning("已达到GitHub API调用限制，等待1小时...")
                time.sleep(3600)
                self.api_calls_count = 0
            
            url = f"https://api.github.com/repos/{repo_full_name}"
            response = self.session.get(url)
            self.api_calls_count += 1
            
            if response.status_code == 200:
                repo_data = response.json()
                
                # 获取语言分布
                languages = self.get_repo_languages(repo_full_name)
                
                return {
                    'name': repo_data['name'],
                    'full_name': repo_data['full_name'],
                    'description': repo_data['description'],
                    'language': repo_data['language'],
                    'languages': languages,
                    'clone_url': repo_data['clone_url'],
                    'html_url': repo_data['html_url'],
                    'created_at': repo_data['created_at'],
                    'updated_at': repo_data['updated_at'],
                    'size': repo_data['size'],
                    'stargazers_count': repo_data['stargazers_count'],
                    'forks_count': repo_data['forks_count'],
                    'topics': repo_data.get('topics', [])
                }
            else:
                logger.error(f"GitHub API错误 {response.status_code}: {repo_full_name}")
                
        except Exception as e:
            logger.error(f"获取仓库信息失败: {repo_full_name} - {e}")
        
        return None
    
    def apply_mystique_standard(self, patches: List[Dict]) -> Optional[Tuple[Dict, Dict]]:
        """
        应用mystique标准：选择最早和最新的提交
        
        Args:
            patches: 补丁列表（包含提交信息）
            
        Returns:
            Tuple[Dict, Dict]: (最早提交, 最新提交) 或 None
        """
        if len(patches) < 2:
            return None
        
        # 按时间排序
        sorted_patches = sorted(patches, key=lambda x: x.get('date', ''))
        
        earliest = sorted_patches[0]
        latest = sorted_patches[-1]
        
        # 确保两个提交不是同一个
        if earliest['sha'] == latest['sha']:
            return None
        
        return earliest, latest
    
    def create_patch_pair_record(self, cve_id: str, earliest_commit: Dict, latest_commit: Dict, 
                                repo_info: Dict, total_patches: int = 0) -> Dict:
        """
        创建补丁对记录
        
        Args:
            cve_id: CVE ID
            earliest_commit: 最早提交
            latest_commit: 最新提交
            repo_info: 仓库信息
            
        Returns:
            Dict: 格式化的补丁对记录
        """
        # 确定补丁类型
        patch_type = "same_repo"  # 当前实现中都是同一仓库
        
        # 生成统一ID
        target_version_hash = latest_commit['sha'][:8]
        unified_id = f"{cve_id}_{repo_info['name']}_{repo_info['name']}_{target_version_hash}"
        
        return {
            "unified_id": unified_id,
            "cve_id": cve_id,
            "patch_type": patch_type,
            "projects": {
                "source": {
                    "name": repo_info['name'],
                    "repo": repo_info['html_url'],
                    "language": repo_info.get('language', 'unknown')
                },
                "target": {
                    "name": repo_info['name'],
                    "repo": repo_info['html_url'],
                    "language": repo_info.get('language', 'unknown')
                }
            },
            "versions": {
                "source": {
                    "vulnerable": {"version": "unknown", "commit": "unknown"},
                    "patched": {"version": "unknown", "commit": earliest_commit['sha']}
                },
                "target": {
                    "vulnerable": {"version": "unknown", "commit": earliest_commit['sha']},
                    "patched": {"version": "unknown", "commit": latest_commit['sha']}
                }
            },
            "source_dataset": "mystique_nvd_collector",
            "metadata": {
                "earliest_commit": {
                    "sha": earliest_commit['sha'],
                    "date": earliest_commit['date'],
                    "message": earliest_commit['message'][:100] + "..." if len(earliest_commit['message']) > 100 else earliest_commit['message'],
                    "author": earliest_commit['author'],
                    "url": earliest_commit['url'],
                    "stats": earliest_commit.get('stats', {}),
                    "files_changed": earliest_commit.get('files', 0)
                },
                "latest_commit": {
                    "sha": latest_commit['sha'],
                    "date": latest_commit['date'],
                    "message": latest_commit['message'][:100] + "..." if len(latest_commit['message']) > 100 else latest_commit['message'],
                    "author": latest_commit['author'],
                    "url": latest_commit['url'],
                    "stats": latest_commit.get('stats', {}),
                    "files_changed": latest_commit.get('files', 0)
                },
                "time_span_days": self._calculate_time_span(earliest_commit['date'], latest_commit['date']),
                "total_patches_found": total_patches
            }
        }
    
    def _calculate_time_span(self, start_date: str, end_date: str) -> int:
        """计算时间跨度（天数）"""
        try:
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            return (end - start).days
        except:
            return 0
    
    def append_to_output(self, patch_pair: Dict):
        """将补丁对追加到输出文件"""
        try:
            # 读取现有数据
            with open(self.output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 追加新的补丁对
            data['patch_pairs'].append(patch_pair)
            
            # 更新统计信息
            data['collection_info']['total_patch_pairs'] = len(data['patch_pairs'])
            data['collection_info']['last_updated'] = datetime.now().isoformat()
            
            # 写回文件
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ 已保存补丁对: {patch_pair['cve_id']} -> {patch_pair['unified_id']}")
            
        except Exception as e:
            logger.error(f"保存补丁对失败: {e}")
    
    def process_trace_result(self, trace_result_path: str) -> bool:
        """
        处理单个trace_result.json文件
        
        Args:
            trace_result_path: trace_result.json文件路径
            
        Returns:
            bool: 是否成功处理并生成补丁对
        """
        try:
            with open(trace_result_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            cve_id = data.get('cve_id')
            patch_summary = data.get('patch_summary', '')
            
            if not cve_id or not patch_summary or patch_summary == "未发现补丁候选":
                logger.info(f"⏭️ 跳过 {cve_id}: 无有效补丁信息")
                return False
            
            logger.info(f"🔍 处理 {cve_id}")
            
            # 获取CVE详细数据
            cve_data = self.get_cve_data(cve_id)
            
            # 提取补丁信息
            patches = self.extract_patches_from_summary(patch_summary)
            
            if len(patches) < 2:
                logger.info(f"⏭️ 跳过 {cve_id}: 补丁数量不足 ({len(patches)} < 2)")
                return False
            
            # 获取提交信息
            enriched_patches = []
            repo_full_name = None
            
            for patch in patches:
                commit_info = self.get_commit_info(patch['repo_full_name'], patch['commit_hash'])
                if commit_info:
                    patch.update(commit_info)
                    enriched_patches.append(patch)
                    repo_full_name = patch['repo_full_name']
                
                # 添加延迟以避免API限制
                time.sleep(0.1)
            
            if len(enriched_patches) < 2:
                logger.info(f"⏭️ 跳过 {cve_id}: 获取提交信息后补丁数量不足")
                return False
            
            # 应用mystique标准
            mystique_result = self.apply_mystique_standard(enriched_patches)
            if not mystique_result:
                logger.info(f"⏭️ 跳过 {cve_id}: 不符合mystique标准")
                return False
            
            earliest_commit, latest_commit = mystique_result
            
            # 获取仓库信息
            repo_info = self.get_repo_info(repo_full_name)
            if not repo_info:
                logger.warning(f"⚠️ 无法获取仓库信息: {repo_full_name}")
                # 使用基本信息
                repo_info = {
                    'name': repo_full_name.split('/')[-1],
                    'html_url': f"https://github.com/{repo_full_name}",
                    'language': 'unknown'
                }
            
            # 检查是否为C/C++项目
            if not self.is_cpp_project(repo_info, cve_data):
                logger.info(f"⏭️ 跳过 {cve_id}: 不是C/C++项目 (语言: {repo_info.get('language', 'unknown')})")
                return False
            
            # 创建补丁对记录
            patch_pair = self.create_patch_pair_record(
                cve_id, earliest_commit, latest_commit, repo_info, len(enriched_patches)
            )
            
            # 保存到输出文件
            self.append_to_output(patch_pair)
            
            return True
            
        except Exception as e:
            logger.error(f"处理 {trace_result_path} 失败: {e}")
            return False
    
    def process_all_traces(self, base_dirs: List[str]):
        """
        处理指定目录列表下的所有trace_result.json文件
        
        Args:
            base_dirs: 基础目录路径列表
        """
        trace_files = []
        
        # 查找所有trace_result.json文件
        for base_dir in base_dirs:
            if os.path.exists(base_dir):
                for root, dirs, files in os.walk(base_dir):
                    for file in files:
                        if file == 'trace_result.json':
                            trace_files.append(os.path.join(root, file))
                logger.info(f"在 {base_dir} 中找到 {len([f for f in trace_files if base_dir in f])} 个trace_result.json文件")
            else:
                logger.warning(f"目录不存在: {base_dir}")
        
        logger.info(f"总共找到 {len(trace_files)} 个trace_result.json文件")
        
        success_count = 0
        cpp_count = 0
        total_count = len(trace_files)
        
        for i, trace_file in enumerate(trace_files, 1):
            logger.info(f"[{i}/{total_count}] 处理文件: {trace_file}")
            
            try:
                if self.process_trace_result(trace_file):
                    success_count += 1
                    cpp_count += 1
            except Exception as e:
                logger.error(f"处理文件失败 {trace_file}: {e}")
            
            # 添加进度报告
            if i % 20 == 0:
                logger.info(f"📊 进度: {i}/{total_count}, C/C++补丁对: {cpp_count}")
        
        logger.info(f"✅ 处理完成: {cpp_count}/{total_count} 个CVE成功生成C/C++补丁对")
        logger.info(f"📁 输出文件: {self.output_file}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CVE补丁对提取器')
    parser.add_argument('--base-dir', help='包含trace_result.json文件的基础目录')
    parser.add_argument('--all-segments', action='store_true', help='处理所有segment目录')
    parser.add_argument('--github-token', required=True, help='GitHub API令牌')
    parser.add_argument('--output', default='mystique_cpp_patch_pairs.json', help='输出文件路径')
    
    args = parser.parse_args()
    
    # 确定要处理的目录
    if args.all_segments:
        # 自动查找所有segment目录
        import glob
        base_dirs = glob.glob('segment_*')
        if not base_dirs:
            logger.error("未找到任何segment目录")
            return
        base_dirs.sort()
        logger.info(f"找到segment目录: {base_dirs}")
    elif args.base_dir:
        base_dirs = [args.base_dir]
    else:
        logger.error("请指定 --base-dir 或使用 --all-segments")
        return
    
    # 创建提取器
    extractor = PatchPairExtractor(args.github_token, args.output)
    
    # 处理所有文件
    extractor.process_all_traces(base_dirs)


if __name__ == '__main__':
    main() 