#!/bin/bash

echo "🔄 断点重传：继续处理剩余时间段的CVE收集"

# 停止现有进程
pkill -f "nvd_patch_collector.py" 2>/dev/null
pkill -f "restart_collection.sh" 2>/dev/null

# 配置参数
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/home/<USER>"
OUTPUT_DIR="$SCRIPT_DIR"
CONFIG_FILE="$PROJECT_ROOT/src/data_collection/config/sources.json"
START_DATE="2023-08-23"
END_DATE="2025-07-24"
MAX_DEPTH=5
CONFIDENCE=0.7
SEGMENT_DAYS=30

# 修复的GitHub Token
GITHUB_TOKEN="*********************************************************************************************"

# 修复网络配置 - 移除代理设置
unset http_proxy
unset https_proxy
unset HTTP_PROXY
unset HTTPS_PROXY
export NO_PROXY="*"

echo "🌐 网络配置："
echo "  - 已移除代理设置"
echo "  - 直连模式访问GitHub和NVD"

echo "📊 处理参数："
echo "  - 时间段: 每段${SEGMENT_DAYS}天"
echo "  - 深度设置: ${MAX_DEPTH}"
echo "  - 并发线程: 2个（进一步降低避免429错误）"
echo "  - NVD API密钥: 已启用，10秒间隔"
echo "  - 断点重传: 继续处理"

# 计算时间段
start_epoch=$(date -d "$START_DATE" +%s)
end_epoch=$(date -d "$END_DATE" +%s)
segment_seconds=$((SEGMENT_DAYS * 24 * 3600))

# 检查已完成的segment
echo ""
echo "📋 检查已完成的segment："
completed_segments=()
for i in {1..50}; do
    segment_dir="$OUTPUT_DIR/segment_${i}_fixed"
    if [ -d "$segment_dir" ]; then
        cve_count=$(find "$segment_dir" -name "trace_result.json" 2>/dev/null | wc -l)
        if [ $cve_count -gt 0 ]; then
            completed_segments+=($i)
            echo "  ✅ segment_${i}_fixed: $cve_count 个CVE已完成"
        fi
    fi
done

# 从下一个segment开始处理
if [ ${#completed_segments[@]} -eq 0 ]; then
    start_segment=1
    echo "  📍 将从segment_1开始处理"
else
    last_completed=${completed_segments[-1]}
    start_segment=$((last_completed + 1))
    echo "  📍 将从segment_${start_segment}开始处理"
fi

current_start=$((start_epoch + (start_segment - 1) * segment_seconds))
segment_count=$((start_segment - 1))

echo ""
echo "🚀 开始断点重传处理..."

while [ $current_start -lt $end_epoch ]; do
    segment_count=$((segment_count + 1))
    current_end=$((current_start + segment_seconds))

    if [ $current_end -gt $end_epoch ]; then
        current_end=$end_epoch
    fi

    segment_start_date=$(date -d "@$current_start" +%Y-%m-%d)
    segment_end_date=$(date -d "@$current_end" +%Y-%m-%d)

    echo ""
    echo "========== 段 $segment_count =========="
    echo "时间范围: $segment_start_date 到 $segment_end_date"
    echo "输出目录: segment_${segment_count}_fixed"

    # 检查是否已完成
    segment_dir="$OUTPUT_DIR/segment_${segment_count}_fixed"
    if [ -d "$segment_dir" ]; then
        existing_count=$(find "$segment_dir" -name "trace_result.json" 2>/dev/null | wc -l)
        if [ $existing_count -gt 0 ]; then
            echo "⏭️ 跳过已完成的段 $segment_count ($existing_count 个CVE)"
            current_start=$((current_end + 1))
            continue
        fi
    fi

    SEGMENT_ARGS=(
        --output-dir "$segment_dir"
        --start-date "$segment_start_date"
        --end-date "$segment_end_date"
        --max-depth "$MAX_DEPTH"
        --confidence "$CONFIDENCE"
    )

    if [ -n "$CONFIG_FILE" ]; then
        SEGMENT_ARGS+=(--config "$CONFIG_FILE")
    fi

    if [ -n "$GITHUB_TOKEN" ]; then
        SEGMENT_ARGS+=(--github-token "$GITHUB_TOKEN")
    fi

    mkdir -p "$segment_dir"

    echo "执行: python3 nvd_patch_collector.py ${SEGMENT_ARGS[*]}"
    
    # 添加重试机制和更长的超时时间
    max_retries=3
    for attempt in $(seq 1 $max_retries); do
        echo "尝试 $attempt/$max_retries ..."
        
        # 增加超时时间到60分钟，降低并发避免限流
        if timeout 3600 python3 nvd_patch_collector.py "${SEGMENT_ARGS[@]}" 2>&1 | tee -a "segment_${segment_count}_processing.log"; then
            echo "✅ 段 $segment_count 完成"
            cve_count=$(find "$segment_dir" -name "trace_result.json" 2>/dev/null | wc -l)
            echo "   处理了 $cve_count 个CVE"
            echo "   日志保存在: segment_${segment_count}_processing.log"
            break
        else
            echo "❌ 段 $segment_count 尝试 $attempt 失败"
            if [ $attempt -lt $max_retries ]; then
                echo "⏳ 等待120秒后重试..."
                sleep 120
                # 重置网络配置
                unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY
                export NO_PROXY="*"
            fi
        fi
    done

    # 检查是否需要停止（用户中断或连续失败）
    if [ ! -d "$segment_dir" ] || [ $(find "$segment_dir" -name "trace_result.json" 2>/dev/null | wc -l) -eq 0 ]; then
        echo "⚠️ 段 $segment_count 处理失败，可以手动重启继续"
        echo "重启命令: ./restart_collection_continue.sh"
        break
    fi

    current_start=$((current_end + 1))
    
    # 每处理完一个段就短暂休息，避免过于频繁的请求
    echo "😴 休息30秒..."
    sleep 30
done

echo ""
echo "🎉 断点重传处理完成！"
echo "📊 处理总结："
total_cves=0
for i in {1..50}; do
    segment_dir="$OUTPUT_DIR/segment_${i}_fixed"
    if [ -d "$segment_dir" ]; then
        cve_count=$(find "$segment_dir" -name "trace_result.json" 2>/dev/null | wc -l)
        if [ $cve_count -gt 0 ]; then
            echo "  segment_${i}_fixed: $cve_count 个CVE"
            total_cves=$((total_cves + cve_count))
        fi
    fi
done
echo "总计处理: $total_cves 个CVE"
echo "完成时间: $(date)" 