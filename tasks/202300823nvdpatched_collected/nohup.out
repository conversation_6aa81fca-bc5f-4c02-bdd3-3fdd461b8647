nohup: ignoring input
🚀 启动内存优化版CVE补丁收集器
处理所有可用CVE，支持多线程并发，优化内存使用
开始时间: Sat Jul 26 17:58:12 UTC 2025
内存限制: 4.0GB
批处理大小: 20
段大小: 7天
📦 检查Python依赖...
📊 计划处理 101 个段，每段 7 天

========== 段 1/101 ==========
时间范围: 2023-08-23 到 2023-08-30
模式: 内存优化 + 批处理 + 并发处理
段开始前内存状态:
               total        used        free      shared  buff/cache   available
Mem:           629Gi       5.3Gi       598Gi        15Mi        25Gi       619Gi
Swap:           30Gi          0B        30Gi
执行: python3 nvd_patch_collector.py --output-dir /home/<USER>/202300823nvdpatched_collected/segment_1 --start-date 2023-08-23 --end-date 2023-08-30 --max-depth 5 --confidence 0.7 --config /home/<USER>/src/data_collection/config/sources.json --github-token *********************************************************************************************
[32m2025-07-26 17:58:14.538[0m | [1mINFO    [0m | [36m__main__[0m:[36m__init__[0m:[36m173[0m | [1mNVD补丁收集器初始化完成[0m
[32m2025-07-26 17:58:14.539[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m初始化后 内存使用: RSS=223.4MB, VMS=3235.4MB, 百分比=0.0%, 可用=634473.5MB[0m
[32m2025-07-26 17:58:14.539[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m438[0m | [1m开始收集 2023-08-23 到 2023-08-30 期间的CVE补丁对[0m
[32m2025-07-26 17:58:14.540[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m收集开始前 内存使用: RSS=223.4MB, VMS=3235.4MB, 百分比=0.0%, 可用=634473.5MB[0m
[32m2025-07-26 17:58:14.540[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m442[0m | [1m📊 API调用参数:[0m
[32m2025-07-26 17:58:14.540[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m443[0m | [1m  - 开始日期: 2023-08-23T00:00:00.000[0m
[32m2025-07-26 17:58:14.541[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m444[0m | [1m  - 结束日期: 2023-08-30T00:00:00.999[0m
[32m2025-07-26 17:58:14.541[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m445[0m | [1m  - 日期类型: published[0m
[32m2025-07-26 17:58:14.541[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m446[0m | [1m  - 最大CVE数量: 无限制[0m
[32m2025-07-26 17:58:14.541[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m447[0m | [1m  - 批处理大小: 50[0m
[32m2025-07-26 17:58:14.541[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m450[0m | [1m🔍 正在调用NVD API获取CVE列表...[0m
[32m2025-07-26 17:58:30.848[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m462[0m | [1m找到 354 个CVE[0m
[32m2025-07-26 17:58:30.851[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m471[0m | [1m过滤后需要处理的CVE数量: 354[0m
[32m2025-07-26 17:58:30.851[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 1/8 (CVE 1-50)[0m
[32m2025-07-26 17:58:30.852[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 1 开始前 内存使用: RSS=232.8MB, VMS=3247.6MB, 百分比=0.0%, 可用=634462.0MB[0m
[32m2025-07-26 17:58:30.852[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 1 使用 16 个线程处理 50 个CVE[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:37.228[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:37.376[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 1/50] CVE-2023-3495: 未找到符合条件的补丁对[0m
[32m2025-07-26 17:58:37.384[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:37.754[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 3/50] CVE-2023-39984: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:38.601[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:38.768[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 4/50] CVE-2023-39985: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:39.417[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:39.597[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 5/50] CVE-2023-4041: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:40.900[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:41.088[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 6/50] CVE-2023-4404: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:42.308[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:42.487[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 7/50] CVE-2023-38585: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:42.761[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:42.882[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 8/50] CVE-2023-40144: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:43.117[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:43.226[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 9/50] CVE-2023-40158: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:44.255[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:44.396[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 10/50] CVE-2023-41100: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
[32m2025-07-26 17:58:44.415[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度10/50 内存使用: RSS=1282.1MB, VMS=5359.9MB, 百分比=0.2%, 可用=632853.9MB[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:45.931[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:46.062[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 11/50] CVE-2023-32119: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:48.319[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:48.483[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 12/50] CVE-2023-32236: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:49.505[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:49.613[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 13/50] CVE-2023-32496: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:50.477[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:50.613[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 14/50] CVE-2023-32497: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:50.826[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:51.079[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 15/50] CVE-2023-32498: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:53.139[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:53.316[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 16/50] CVE-2023-3899: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:53.705[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:53.895[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 17/50] CVE-2023-32499: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:54.439[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:54.596[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 18/50] CVE-2023-28994: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
/home/<USER>/src/data_collection/services/patch_tracer/reference_network.py:446: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`). Consider using `matplotlib.pyplot.close()`.
  plt.figure(figsize=(18, 12), dpi=300)  # 增加图像尺寸
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:56.615[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:56.747[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 19/50] CVE-2023-32300: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:56.965[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:57.090[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 20/50] CVE-2023-32509: 未找到符合条件的补丁对[0m
[32m2025-07-26 17:58:57.104[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度20/50 内存使用: RSS=2527.0MB, VMS=6601.0MB, 百分比=0.4%, 可用=631649.5MB[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:57.305[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:57.508[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 21/50] CVE-2023-4042: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:58:57.749[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:58:57.896[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 22/50] CVE-2023-32505: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:02.656[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:02.880[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 23/50] CVE-2023-1409: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:03.081[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:03.240[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 24/50] CVE-2023-20115: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:03.790[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:03.975[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 25/50] CVE-2023-20168: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:07.876[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:08.073[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 26/50] CVE-2023-20169: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:08.482[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:08.689[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 27/50] CVE-2023-20200: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:08.827[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:09.030[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 28/50] CVE-2023-20230: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:12.618[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:12.818[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 29/50] CVE-2023-20234: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:34.211[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 17:59:34.420[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 30/50] CVE-2023-41105: 未找到符合条件的补丁对[0m
[32m2025-07-26 17:59:34.434[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度30/50 内存使用: RSS=2863.3MB, VMS=6900.4MB, 百分比=0.4%, 可用=631625.1MB[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:37.458[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 17:59:37.685[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 31/50] CVE-2023-41104: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/a089e98f9eeb83524286dc4399d8186956af8f20
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/aaa8c26fc40af65e2c51c0f12ad9c5f6fa2e0622
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/2708578da6cf6f7feff2d5a79b783e6cf2469821
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/a089e98f9eeb83524286dc4399d8186956af8f20
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:50.029[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:50.246[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 32/50] CVE-2023-41098: 未找到符合条件的补丁对[0m
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/aaa8c26fc40af65e2c51c0f12ad9c5f6fa2e0622
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/2708578da6cf6f7feff2d5a79b783e6cf2469821
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:53.470[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 17:59:53.686[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 33/50] CVE-2023-40612: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 17:59:54.802[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 17:59:55.069[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 34/50] CVE-2023-40273: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/6adc0fc1f427af3518cab2bc0b9ff8415e904ba9
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/4078fa66677547434098b16b9a9fc5560ef6564c
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/6adc0fc1f427af3518cab2bc0b9ff8415e904ba9
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/4078fa66677547434098b16b9a9fc5560ef6564c
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:00:02.832[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:00:03.083[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 35/50] CVE-2023-39441: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:00:05.331[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:00:05.557[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 36/50] CVE-2023-40025: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:00:10.362[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:00:10.614[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 37/50] CVE-2023-40176: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:00:16.036[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:00:16.283[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 38/50] CVE-2023-37379: 未找到符合条件的补丁对[0m
连接错误: https://crbug.com/1470668 - HTTPSConnectionPool(host='crbug.com', port=443): Max retries exceeded with url: /1470668 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
连接错误: https://crbug.com/1469348 - HTTPSConnectionPool(host='crbug.com', port=443): Max retries exceeded with url: /1469348 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:05:25.961[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:05:26.201[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 39/50] CVE-2023-4427: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:05:26.954[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:05:27.165[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 40/50] CVE-2023-4431: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:05:27.167[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度40/50 内存使用: RSS=3592.8MB, VMS=7590.5MB, 百分比=0.6%, 可用=630894.2MB[0m
连接错误: https://network.mobile.rakuten.co.jp/product/internet/rakuten-wifi-pocket/support - HTTPSConnectionPool(host='network.mobile.rakuten.co.jp', port=443): Max retries exceeded with url: /product/internet/rakuten-wifi-pocket/support (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x78948ff028b0>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:06:00.740[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:06:00.955[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 41/50] CVE-2023-40282: 未找到符合条件的补丁对[0m
连接错误: https://chromereleases.googleblog.com/2023/08/chrome-desktop-stable-update.html - HTTPSConnectionPool(host='chromereleases.googleblog.com', port=443): Max retries exceeded with url: /2023/08/chrome-desktop-stable-update.html (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7894503aed90>, 'Connection to chromereleases.googleblog.com timed out. (connect timeout=15)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
连接错误: https://chromereleases.googleblog.com/2023/08/chrome-desktop-stable-update.html - HTTPSConnectionPool(host='chromereleases.googleblog.com', port=443): Max retries exceeded with url: /2023/08/chrome-desktop-stable-update.html (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7894502e0df0>, 'Connection to chromereleases.googleblog.com timed out. (connect timeout=15)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
连接错误: https://chromereleases.googleblog.com/2023/08/chrome-desktop-stable-update.html - HTTPSConnectionPool(host='chromereleases.googleblog.com', port=443): Max retries exceeded with url: /2023/08/chrome-desktop-stable-update.html (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x78948fc4ef70>, 'Connection to chromereleases.googleblog.com timed out. (connect timeout=15)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:06:07.770[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:06:07.994[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 42/50] CVE-2023-4430: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:06:08.053[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:06:08.271[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 43/50] CVE-2023-4428: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:06:08.987[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:06:09.216[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 44/50] CVE-2023-4429: 未找到符合条件的补丁对[0m
连接错误: https://news.ycombinator.com/item - HTTPSConnectionPool(host='news.ycombinator.com', port=443): Max retries exceeded with url: /item (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x789450c7c6d0>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
连接错误: https://support.lenovo.com/us/en/product_security/len-103710 - HTTPSConnectionPool(host='support.lenovo.com', port=443): Max retries exceeded with url: /us/en/product_security/len-103710 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='support.lenovo.com', port=443): Read timed out. (read timeout=15)"))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:06:38.456[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:06:38.742[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 45/50] CVE-2022-3742: 未找到符合条件的补丁对[0m
连接错误: https://support.lenovo.com/us/en/product_security/len-103710 - HTTPSConnectionPool(host='support.lenovo.com', port=443): Max retries exceeded with url: /us/en/product_security/len-103710 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='support.lenovo.com', port=443): Read timed out. (read timeout=15)"))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:06:42.088[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:06:42.341[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 46/50] CVE-2022-3743: 未找到符合条件的补丁对[0m
连接错误: https://support.lenovo.com/us/en/product_security/len-103710 - HTTPSConnectionPool(host='support.lenovo.com', port=443): Max retries exceeded with url: /us/en/product_security/len-103710 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='support.lenovo.com', port=443): Read timed out. (read timeout=15)"))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:07:03.726[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:04.015[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 47/50] CVE-2022-3744: 未找到符合条件的补丁对[0m
连接错误: https://support.lenovo.com/us/en/product_security/len-103710 - HTTPSConnectionPool(host='support.lenovo.com', port=443): Max retries exceeded with url: /us/en/product_security/len-103710 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='support.lenovo.com', port=443): Read timed out. (read timeout=15)"))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:07:07.199[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:07.455[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 48/50] CVE-2022-3745: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:07:17.491[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:17.790[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 49/50] CVE-2023-38831: 未找到符合条件的补丁对[0m
连接错误: https://support.lenovo.com/us/en/product_security/len-103710 - HTTPSConnectionPool(host='support.lenovo.com', port=443): Max retries exceeded with url: /us/en/product_security/len-103710 (Caused by ReadTimeoutError("HTTPSConnectionPool(host='support.lenovo.com', port=443): Read timed out. (read timeout=15)"))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:07:19.734[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:19.994[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 50/50] CVE-2022-3746: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:19.996[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度50/50 内存使用: RSS=4379.3MB, VMS=8339.0MB, 百分比=0.7%, 可用=629897.6MB[0m
[32m2025-07-26 18:07:20.005[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 1 完成后 内存使用: RSS=4286.9MB, VMS=8258.9MB, 百分比=0.7%, 可用=629981.9MB[0m
[32m2025-07-26 18:07:22.008[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 2/8 (CVE 51-100)[0m
[32m2025-07-26 18:07:22.009[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 2 开始前 内存使用: RSS=4286.9MB, VMS=8226.9MB, 百分比=0.7%, 可用=630030.4MB[0m
[32m2025-07-26 18:07:22.010[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 2 使用 16 个线程处理 50 个CVE[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:07:43.575[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:43.864[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 1/50] CVE-2023-32202: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:44.545[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:44.919[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 3/50] CVE-2023-41028: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:45.697[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:46.029[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 4/50] CVE-2023-38422: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:07:46.212[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:46.518[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 5/50] CVE-2023-4228: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:48.374[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:48.374[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:48.755[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 8/50] CVE-2023-32559: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:48.755[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 8/50] CVE-2023-32559: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:51.520[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:51.913[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 9/50] CVE-2023-40177: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:53.020[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:53.396[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 10/50] CVE-2023-40572: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:53.688[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:54.003[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 11/50] CVE-2023-40573: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:07:54.153[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:07:54.428[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 12/50] CVE-2023-36317: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:02.310[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:02.619[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 13/50] CVE-2023-4230: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:05.564[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:05.940[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 14/50] CVE-2023-32510: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:06.680[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:06.680[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:07.054[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 15/50] CVE-2023-32511: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:07.054[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 15/50] CVE-2023-32511: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
[32m2025-07-26 18:08:07.881[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:08.208[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 16/50] CVE-2023-32516: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:08:10.439[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:10.955[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 17/50] CVE-2023-4512: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:11.835[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:12.212[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 18/50] CVE-2023-4511: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:12.377[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:12.830[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 19/50] CVE-2023-4513: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:14.355[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:14.898[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 20/50] CVE-2023-34040: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:15.045[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:15.513[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:15.938[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 21/50] CVE-2023-40371: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:15.939[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:15.982[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 22/50] CVE-2023-40874: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
[32m2025-07-26 18:08:16.647[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:16.647[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:16.647[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:17.187[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 24/50] CVE-2023-40035: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:17.187[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 24/50] CVE-2023-40035: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:17.187[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 24/50] CVE-2023-40035: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:19.489[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
GitHub API未返回有效数据
[32m2025-07-26 18:08:19.911[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 25/50] CVE-2023-40185: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:20.297[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:20.773[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 26/50] CVE-2023-40178: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:20.297[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:20.773[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 26/50] CVE-2023-40178: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:20.890[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:21.322[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 27/50] CVE-2023-40876: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:23.678[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:24.071[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 28/50] CVE-2023-40877: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:27.021[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:27.441[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 29/50] CVE-2022-46884: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:28.065[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:28.569[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 30/50] CVE-2023-34971: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:29.357[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:29.816[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 31/50] CVE-2023-34972: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
[32m2025-07-26 18:08:31.407[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:31.841[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 32/50] CVE-2023-34973: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:32.294[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:32.753[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 33/50] CVE-2023-40706: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:36.203[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:36.610[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 34/50] CVE-2023-40709: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:08:37.504[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
GitHub API未返回有效数据
[32m2025-07-26 18:08:38.015[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 36/50] CVE-2023-40707: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:38.118[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:38.558[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 37/50] CVE-2023-40710: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:39.193[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:39.745[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 38/50] CVE-2023-40708: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:08:39.989[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:40.544[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 39/50] CVE-2023-40891: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:41.031[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
GitHub API未返回有效数据
[32m2025-07-26 18:08:41.520[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 40/50] CVE-2023-40893: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:41.546[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次2 进度40/50 内存使用: RSS=8245.1MB, VMS=12163.2MB, 百分比=1.3%, 可用=626257.5MB[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:42.426[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:42.911[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 41/50] CVE-2023-40892: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:43.089[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:43.517[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 42/50] CVE-2023-40894: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:45.493[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:46.009[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 43/50] CVE-2023-40895: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:46.275[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:46.771[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 44/50] CVE-2023-40896: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:08:48.071[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:48.517[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 45/50] CVE-2023-40897: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:08:48.964[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:49.436[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 46/50] CVE-2023-40898: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:08:49.610[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:50.106[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 47/50] CVE-2023-40899: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:08:54.324[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:08:54.798[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 48/50] CVE-2023-40900: 未找到符合条件的补丁对[0m
连接错误: https://www.cert-in.org.in/s2cmainservlet - HTTPSConnectionPool(host='www.cert-in.org.in', port=443): Max retries exceeded with url: /s2cmainservlet (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789407e3fca0>, 'Connection to www.cert-in.org.in timed out. (connect timeout=15)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:19:33.386[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:19:33.855[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 49/50] CVE-2023-3704: 未找到符合条件的补丁对[0m
连接错误: https://www.cert-in.org.in/s2cmainservlet - HTTPSConnectionPool(host='www.cert-in.org.in', port=443): Max retries exceeded with url: /s2cmainservlet (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789407e3f400>, 'Connection to www.cert-in.org.in timed out. (connect timeout=15)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:19:58.400[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:19:58.904[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次2 50/50] CVE-2023-3705: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:19:58.904[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次2 进度50/50 内存使用: RSS=8612.0MB, VMS=12464.9MB, 百分比=1.3%, 可用=626066.7MB[0m
[32m2025-07-26 18:19:58.907[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 2 完成后 内存使用: RSS=8612.0MB, VMS=12376.8MB, 百分比=1.3%, 可用=626066.7MB[0m
[32m2025-07-26 18:19:58.907[0m | [33m[1mWARNING [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m499[0m | [33m[1m⚠️ 内存使用超限，执行强制清理[0m
[32m2025-07-26 18:19:58.917[0m | [34m[1mDEBUG   [0m | [36m__main__[0m:[36mforce_gc[0m:[36m114[0m | [34m[1m执行垃圾回收...[0m
[32m2025-07-26 18:19:59.484[0m | [34m[1mDEBUG   [0m | [36m__main__[0m:[36m_cleanup_tracer[0m:[36m203[0m | [34m[1m已清理补丁跟踪器[0m
[32m2025-07-26 18:19:59.485[0m | [34m[1mDEBUG   [0m | [36m__main__[0m:[36mforce_gc[0m:[36m114[0m | [34m[1m执行垃圾回收...[0m
[32m2025-07-26 18:20:00.052[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m清理后 内存使用: RSS=8564.1MB, VMS=12280.8MB, 百分比=1.3%, 可用=626109.8MB[0m
[32m2025-07-26 18:20:02.055[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 3/8 (CVE 101-150)[0m
[32m2025-07-26 18:20:02.057[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 3 开始前 内存使用: RSS=8564.1MB, VMS=12280.8MB, 百分比=1.3%, 可用=626117.4MB[0m
[32m2025-07-26 18:20:02.057[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 3 使用 16 个线程处理 50 个CVE[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:20:28.167[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:20:28.665[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 1/50] CVE-2023-31412: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:20:28.674[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:20:29.302[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:20:30.089[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:20:30.578[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 4/50] CVE-2023-4420: 未找到符合条件的补丁对[0m
连接错误: https://github.com/peris-navince/founded-0-days/blob/main/ac10/SetIpMacBind/1.md - HTTPSConnectionPool(host='github.com', port=443): Read timed out.
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
连接错误: https://github.com/Enalean/tuleap/commit/307c1c8044522a2dcc711062b18a3b3f9059a6c3 - HTTPSConnectionPool(host='github.com', port=443): Read timed out.
[32m2025-07-26 18:20:41.045[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:20:41.542[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 5/50] CVE-2023-40902: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
连接错误: https://github.com/CloudExplorer-Dev/CloudExplorer-Lite/security/advisories/GHSA-hh2g-77xq-x4vq - HTTPSConnectionPool(host='github.com', port=443): Read timed out.
连接错误: https://drive.google.com/file/d/1nkql4ysjapypy8b-zdc7ve-qmbqau8ou - HTTPSConnectionPool(host='drive.google.com', port=443): Max retries exceeded with url: /file/d/1nkql4ysjapypy8b-zdc7ve-qmbqau8ou (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x789407286820>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:28:01.887[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:28:02.394[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 6/50] CVE-2023-39699: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
连接错误: https://drive.google.com/file/d/1ql_517ubtfjox4cxkqpp9fehr1yxrj-y - HTTPSConnectionPool(host='drive.google.com', port=443): Max retries exceeded with url: /file/d/1ql_517ubtfjox4cxkqpp9fehr1yxrj-y (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x78941b668970>: Failed to establish a new connection: [Errno 101] Network is unreachable'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:28:27.046[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:28:27.559[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 7/50] CVE-2023-39700: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
连接错误: https://github.com/gravitl/netmaker/commit/b3be57c65bf0bbfab43b66853c8e3637a43e2839 - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /gravitl/netmaker/commit/b3be57c65bf0bbfab43b66853c8e3637a43e2839 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f72790>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/gravitl/netmaker/security/advisories/GHSA-826j-8wp2-4x6q - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /gravitl/netmaker/security/advisories/GHSA-826j-8wp2-4x6q (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f195e0>, 'Connection to github.com timed out. (connect timeout=22.5)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
连接错误: https://github.com/Enalean/tuleap/commit/93d10654b1d95c5bf500204666310418b01b8a8d - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /Enalean/tuleap/commit/93d10654b1d95c5bf500204666310418b01b8a8d (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f204c0>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/peris-navince/founded-0-days/blob/main/ac10/formSetMacFilterCfg/1.md - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /peris-navince/founded-0-days/blob/main/ac10/formSetMacFilterCfg/1.md (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f20580>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/gravitl/netmaker/commit/9362c39a9a822f0e07361aa7c77af2610597e657 - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /gravitl/netmaker/commit/9362c39a9a822f0e07361aa7c77af2610597e657 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f20790>, 'Connection to github.com timed out. (connect timeout=22.5)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
连接错误: https://github.com/GeoNode/geonode/commit/a9eebae80cb362009660a1fd49e105e7cdb499b9 - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /GeoNode/geonode/commit/a9eebae80cb362009660a1fd49e105e7cdb499b9 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f20910>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/peris-navince/founded-0-days/blob/main/ac10/fromSetStaticRouteCfg/1.md - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /peris-navince/founded-0-days/blob/main/ac10/fromSetStaticRouteCfg/1.md (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f20a90>, 'Connection to github.com timed out. (connect timeout=22.5)'))
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:28:49.783[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:28:50.290[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 8/50] CVE-2023-32079: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:28:50.521[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:28:51.028[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 9/50] CVE-2023-40904: 未找到符合条件的补丁对[0m
连接错误: https://github.com/IceWhaleTech/CasaOS/commit/af440eac5563644854ff33f72041e52d3fd1f47c - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /IceWhaleTech/CasaOS/commit/af440eac5563644854ff33f72041e52d3fd1f47c (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x789451f724f0>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/zj3t/Automotive-vulnerabilities/blob/main/RENAULT/ZOE_EV_2021/Vuln - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /zj3t/Automotive-vulnerabilities/blob/main/RENAULT/ZOE_EV_2021/Vuln (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7894083cfa90>, 'Connection to github.com timed out. (connect timeout=22.5)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:28:55.439[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:28:55.941[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 10/50] CVE-2023-39801: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:28:55.955[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次3 进度10/50 内存使用: RSS=9231.5MB, VMS=13112.8MB, 百分比=1.4%, 可用=625341.9MB[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
连接错误: https://github.com/Enalean/tuleap/security/advisories/GHSA-h637-g4xp-2992 - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /Enalean/tuleap/security/advisories/GHSA-h637-g4xp-2992 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7894506a6460>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/rizinorg/rz-libdemangle/commit/51d016750e704b27ab8ace23c0f72acabca67018 - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /rizinorg/rz-libdemangle/commit/51d016750e704b27ab8ace23c0f72acabca67018 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x7894072ca7c0>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/gerbv/gerbv/commit/5517e22250e935dc7f86f64ad414aeae3dbcb36a - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /gerbv/gerbv/commit/5517e22250e935dc7f86f64ad414aeae3dbcb36a (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x78941b6a0af0>, 'Connection to github.com timed out. (connect timeout=22.5)'))
连接错误: https://github.com/rust-lang/cargo/commit/f975722a0eac934c0722f111f107c4ea2f5c4365 - HTTPSConnectionPool(host='github.com', port=443): Max retries exceeded with url: /rust-lang/cargo/commit/f975722a0eac934c0722f111f107c4ea2f5c4365 (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x78941b67a490>, 'Connection to github.com timed out. (connect timeout=22.5)'))
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:29:20.295[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:29:20.809[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 11/50] CVE-2023-40217: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:29:21.589[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:29:22.108[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 12/50] CVE-2023-38508: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:05.154[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:05.790[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 13/50] CVE-2023-40182: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:32:08.016[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:08.602[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 14/50] CVE-2023-38973: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:18.103[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:18.730[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 15/50] CVE-2023-40577: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:23.305[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:23.969[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 16/50] CVE-2023-40599: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:32:24.674[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:25.244[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 17/50] CVE-2023-40030: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:32:26.398[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:32:27.132[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:27.645[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 19/50] CVE-2023-39519: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:32.368[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:32.949[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 20/50] CVE-2023-38974: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:32:32.975[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次3 进度20/50 内存使用: RSS=10056.8MB, VMS=13862.8MB, 百分比=1.6%, 可用=624380.6MB[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:35.862[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:32:36.416[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 21/50] CVE-2023-40022: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:41.533[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:42.121[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 22/50] CVE-2023-40901: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:45.628[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:46.255[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 23/50] CVE-2023-40017: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:32:46.927[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:32:47.567[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 24/50] CVE-2023-40570: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:48.181[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:48.811[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 25/50] CVE-2023-39521: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
[32m2025-07-26 18:32:51.453[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:52.088[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 26/50] CVE-2023-41173: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:32:52.427[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:53.009[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 27/50] CVE-2023-32518: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:55.435[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:56.060[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 28/50] CVE-2023-32576: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:32:56.294[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:56.899[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 29/50] CVE-2023-37469: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:32:57.072[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:57.684[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 30/50] CVE-2023-40179: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:32:57.760[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次3 进度30/50 内存使用: RSS=10876.2MB, VMS=14707.6MB, 百分比=1.7%, 可用=623627.4MB[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:32:59.379[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:32:59.956[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 31/50] CVE-2023-32577: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:33:05.326[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:05.955[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 33/50] CVE-2023-32077: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:33:06.076[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
GitHub API未返回有效数据
[32m2025-07-26 18:33:06.896[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:07.522[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 35/50] CVE-2023-32591: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:33:09.431[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:10.062[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 36/50] CVE-2023-3406: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:33:12.211[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:12.814[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 37/50] CVE-2023-25649: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:33:13.428[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:14.088[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 38/50] CVE-2023-3425: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:33:15.111[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:15.752[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 39/50] CVE-2023-4478: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:33:16.177[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:16.855[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 40/50] CVE-2023-25981: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:33:16.857[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次3 进度40/50 内存使用: RSS=11794.7MB, VMS=15612.1MB, 百分比=1.8%, 可用=622676.7MB[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:33:17.680[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:18.295[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 41/50] CVE-2023-24394: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 30秒
============================================
[32m2025-07-26 18:33:18.662[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:19.272[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 42/50] CVE-2023-32575: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:33:23.811[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:24.469[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 43/50] CVE-2023-32596: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:33:24.864[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:25.476[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 44/50] CVE-2023-32595: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:33:26.561[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:33:27.174[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 45/50] CVE-2023-32598: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:34:27.922[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:34:28.548[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次3 46/50] CVE-2023-4520: 未找到符合条件的补丁对[0m
🧹 清理资源...
