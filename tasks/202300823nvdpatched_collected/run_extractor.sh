#!/bin/bash

# CVE补丁对提取器运行脚本

GITHUB_TOKEN="*********************************************************************************************"
BASE_DIR="segment_1"
OUTPUT_FILE="mystique_patch_pairs.json"

echo "开始运行CVE补丁对提取器..."
echo "基础目录: $BASE_DIR"
echo "输出文件: $OUTPUT_FILE"
echo "时间: $(date)"

python3 patch_pair_extractor.py \
    --base-dir "$BASE_DIR" \
    --github-token "$GITHUB_TOKEN" \
    --output "$OUTPUT_FILE"

echo "处理完成，检查结果..."
if [ -f "$OUTPUT_FILE" ]; then
    echo "输出文件大小: $(du -h $OUTPUT_FILE)"
    echo "总补丁对数量: $(grep -o '"total_patch_pairs": [0-9]*' $OUTPUT_FILE | grep -o '[0-9]*')"
else
    echo "输出文件不存在"
fi 