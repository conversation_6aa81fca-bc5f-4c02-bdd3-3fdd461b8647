#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD补丁收集器 - 基于时间范围的CVE补丁对收集 (优化内存版本)

功能说明：
1. 收集2023年8月23日到2025年7月24日期间的CVE补丁对
2. 按照指定的JSON格式输出
3. 实现C/C++项目过滤
4. 遵循mystique论文的筛选标准：选择最早和最新的补丁形成补丁对
5. 新增内存监控和管理功能，防止内存溢出

依赖：
- 基于现有的2024WL_SBOM系统的补丁跟踪功能
- 使用PatchTracer进行补丁发现
- 使用CVEReferenceFetcher获取CVE引用
"""

import os
import sys
import json
import logging
import argparse
import re
import gc
import psutil
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import urllib3
import concurrent.futures
import threading
from pathlib import Path

# 禁用SSL验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 添加项目根目录到Python路径
project_root = "/home/<USER>"
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入现有系统的模块
from src.data_collection.services.patch_tracer import (
    PatchTracer,
    CVEReferenceFetcher,
    get_initial_references,
    prepare_references_for_tracer,
    prioritize_initial_references
)
from src.data_collection.utils.logger import setup_logger

class MemoryMonitor:
    """
    内存监控器类
    用于监控和管理程序内存使用情况
    """
    
    def __init__(self, logger, max_memory_gb: float = 8.0):
        """
        初始化内存监控器
        
        Args:
            logger: 日志器
            max_memory_gb: 最大内存使用限制（GB）
        """
        self.logger = logger
        self.max_memory_bytes = max_memory_gb * 1024 * 1024 * 1024
        self.process = psutil.Process()
        
    def get_memory_usage(self) -> Dict[str, float]:
        """
        获取当前内存使用情况
        
        Returns:
            Dict: 内存使用信息
        """
        memory_info = self.process.memory_info()
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,  # 物理内存
            'vms_mb': memory_info.vms / 1024 / 1024,  # 虚拟内存
            'percent': self.process.memory_percent(),
            'available_mb': psutil.virtual_memory().available / 1024 / 1024
        }
    
    def log_memory_usage(self, prefix: str = ""):
        """
        记录内存使用情况到日志
        
        Args:
            prefix: 日志前缀
        """
        memory = self.get_memory_usage()
        self.logger.info(f"{prefix}内存使用: RSS={memory['rss_mb']:.1f}MB, "
                        f"VMS={memory['vms_mb']:.1f}MB, "
                        f"百分比={memory['percent']:.1f}%, "
                        f"可用={memory['available_mb']:.1f}MB")
    
    def is_memory_limit_exceeded(self) -> bool:
        """
        检查是否超过内存限制
        
        Returns:
            bool: 是否超过限制
        """
        memory_info = self.process.memory_info()
        return memory_info.rss > self.max_memory_bytes
    
    def force_gc(self):
        """
        强制垃圾回收
        """
        self.logger.debug("执行垃圾回收...")
        gc.collect()
        time.sleep(0.1)  # 给垃圾回收一点时间

class NVDPatchCollector:
    """
    NVD补丁收集器 (优化内存版本)
    
    基于现有的补丁跟踪系统，收集指定时间范围内的CVE补丁对
    并按照mystique论文的标准进行筛选和格式化输出
    新增内存管理功能防止内存溢出
    """
    
    def __init__(self, 
                 output_dir: str,
                 github_token: Optional[str] = None,
                 config_path: Optional[str] = None,
                 max_depth: int = 2,
                 confidence_threshold: float = 0.7,
                 max_memory_gb: float = 6.0,  # 降低内存限制
                 batch_size: int = 50):        # 添加批处理大小
        """
        初始化收集器
        
        Args:
            output_dir: 输出目录
            github_token: GitHub API令牌
            config_path: 配置文件路径
            max_depth: 最大跟踪深度
            confidence_threshold: 置信度阈值
            max_memory_gb: 最大内存使用限制（GB）
            batch_size: 批处理大小
        """
        self.output_dir = output_dir
        self.github_token = github_token
        self.config_path = config_path
        self.max_depth = max_depth
        self.confidence_threshold = confidence_threshold
        self.batch_size = batch_size
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置日志
        self.logger = setup_logger(
            log_dir=os.path.join(output_dir, "logs"),
            console_level="INFO",
            file_level="DEBUG"
        )
        
        # 初始化内存监控器
        self.memory_monitor = MemoryMonitor(self.logger, max_memory_gb)
        
        # 初始化补丁跟踪器（延迟初始化以节省内存）
        self.tracer = None
        
        # 初始化CVE引用获取器
        self.fetcher = CVEReferenceFetcher(config_path)
        
        self.logger.info("NVD补丁收集器初始化完成")
        self.memory_monitor.log_memory_usage("初始化后 ")
    
    def _get_tracer(self):
        """
        获取补丁跟踪器（懒加载）
        
        Returns:
            PatchTracer: 补丁跟踪器实例
        """
        if self.tracer is None:
            self.tracer = PatchTracer(
                output_dir=self.output_dir,
                max_depth=self.max_depth,
                github_token=self.github_token,
                confidence_threshold=self.confidence_threshold,
                verify_ssl=False
            )
        return self.tracer
    
    def _cleanup_tracer(self):
        """
        清理补丁跟踪器以释放内存
        """
        if self.tracer is not None:
            # 清理tracer可能的缓存
            if hasattr(self.tracer, 'cache'):
                self.tracer.cache.clear()
            self.tracer = None
            self.memory_monitor.force_gc()
            self.logger.debug("已清理补丁跟踪器")
    
    def is_cpp_project(self, patch_info: Dict[str, Any]) -> bool:
        """
        判断补丁是否属于C/C++项目 (增强版本)

        Args:
            patch_info: 补丁信息字典

        Returns:
            bool: 是否为C/C++项目
        """
        # 检查项目名称、仓库URL、修改的文件等信息
        project_name = patch_info.get('project_name', '').lower()
        repo_url = patch_info.get('repository', '').lower()
        files_changed = patch_info.get('files_changed', [])
        commit_message = patch_info.get('commit_message', '').lower()

        # C/C++项目的常见标识 (扩展列表)
        cpp_indicators = [
            'linux', 'kernel', 'gcc', 'clang', 'openssl', 'sqlite',
            'postgres', 'mysql', 'nginx', 'apache', 'curl', 'git',
            'opencv', 'ffmpeg', 'chromium', 'firefox', 'webkit',
            'glibc', 'musl', 'binutils', 'gdb', 'llvm', 'cmake',
            'boost', 'qt', 'gtk', 'gnome', 'kde', 'xorg', 'mesa',
            'systemd', 'dbus', 'pulseaudio', 'alsa', 'bluez',
            'wireshark', 'tcpdump', 'libpcap', 'zlib', 'bzip2',
            'xz', 'lz4', 'snappy', 'protobuf', 'grpc', 'redis',
            'memcached', 'haproxy', 'squid', 'bind', 'openssh',
            'openvpn', 'strongswan', 'ipsec', 'libxml2', 'libxslt',
            'libjpeg', 'libpng', 'libtiff', 'imagemagick', 'graphicsmagick'
        ]

        # 检查项目名称和仓库URL
        for indicator in cpp_indicators:
            if indicator in project_name or indicator in repo_url:
                return True

        # 检查文件扩展名 (扩展列表)
        cpp_extensions = ['.c', '.cpp', '.cc', '.cxx', '.c++', '.h', '.hpp', '.hxx', '.h++', '.hh']
        cpp_file_count = 0
        total_files = len(files_changed)

        for file_path in files_changed:
            file_lower = file_path.lower()
            if any(file_lower.endswith(ext) for ext in cpp_extensions):
                cpp_file_count += 1
            # 检查常见的C/C++相关文件
            elif any(pattern in file_lower for pattern in ['makefile', 'cmake', '.mk', '.am', '.ac', '.in']):
                cpp_file_count += 0.5  # 构建文件给予部分权重

        # 如果超过30%的文件是C/C++相关，认为是C/C++项目
        if total_files > 0 and (cpp_file_count / total_files) >= 0.3:
            return True

        # 检查提交信息中的C/C++关键词
        cpp_keywords = ['c++', 'cpp', 'gcc', 'clang', 'cmake', 'makefile', 'segfault', 'memory leak', 'buffer overflow']
        for keyword in cpp_keywords:
            if keyword in commit_message:
                return True

        return False
    
    def extract_patch_pairs(self, patch_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        从补丁跟踪结果中提取符合mystique标准的补丁对
        
        按照mystique论文的描述：
        - 选择每个漏洞的最早和最新提交形成CVE补丁对
        - 最早的提交视为原始补丁，最新的提交视为目标补丁
        - 如果漏洞只包含单个补丁，则排除
        
        Args:
            patch_results: 补丁跟踪结果
            
        Returns:
            List[Dict]: 补丁对列表
        """
        patch_pairs = []
        
        # 获取所有补丁
        all_patches = patch_results.get('all_patches', [])
        high_confidence_patches = patch_results.get('high_confidence_patches', [])
        
        if len(all_patches) < 1:
            self.logger.info("未找到任何补丁，排除")
            return patch_pairs
        
        # 按时间戳排序补丁
        patches_with_time = []
        for patch in all_patches:
            commit_date = patch.get('commit_date')
            if commit_date:
                try:
                    # 尝试解析时间戳
                    if isinstance(commit_date, str):
                        timestamp = datetime.fromisoformat(commit_date.replace('Z', '+00:00'))
                    else:
                        timestamp = commit_date
                    patches_with_time.append((timestamp, patch))
                except Exception as e:
                    self.logger.warning(f"无法解析补丁时间戳: {commit_date}, 错误: {e}")
        
        if len(patches_with_time) < 2:
            self.logger.info("有效时间戳的补丁数量少于2个，排除")
            return patch_pairs
        
        # 按时间排序
        patches_with_time.sort(key=lambda x: x[0])
        
        # 选择最早和最新的补丁
        earliest_patch = patches_with_time[0][1]
        latest_patch = patches_with_time[-1][1]
        
        # 确保两个补丁不是同一个
        if earliest_patch.get('sha') == latest_patch.get('sha'):
            self.logger.info("最早和最新补丁是同一个，排除")
            return patch_pairs
        
        patch_pair = {
            'source_patch': earliest_patch,
            'target_patch': latest_patch,
            'total_patches': len(all_patches),
            'high_confidence_patches': len(high_confidence_patches)
        }
        
        patch_pairs.append(patch_pair)
        return patch_pairs
    
    def format_to_required_json(self, cve_id: str, patch_pair: Dict[str, Any]) -> Dict[str, Any]:
        """
        将补丁对格式化为需求指定的JSON格式
        
        Args:
            cve_id: CVE ID
            patch_pair: 补丁对信息
            
        Returns:
            Dict: 格式化后的JSON对象
        """
        source_patch = patch_pair['source_patch']
        target_patch = patch_pair['target_patch']
        
        # 提取项目信息
        source_repo = source_patch.get('repository', 'unknown')
        target_repo = target_patch.get('repository', 'unknown')
        
        # 提取项目名称
        source_project_name = self.extract_project_name(source_repo)
        target_project_name = self.extract_project_name(target_repo)
        
        # 判断是否为跨仓库补丁
        patch_type = "cross_repo" if source_repo != target_repo else "same_repo"
        
        # 生成unified_id
        target_version_hash = target_patch.get('sha', target_patch.get('version', 'unknown'))
        unified_id = f"{cve_id}_{source_project_name}_{target_project_name}_{target_version_hash}"
        
        # 构建JSON对象
        json_obj = {
            "unified_id": unified_id,
            "cve_id": cve_id,
            "patch_type": patch_type,
            "projects": {
                "source": {
                    "name": source_project_name,
                    "repo": source_repo,
                    "language": "C"  # 根据筛选条件，默认为C
                },
                "target": {
                    "name": target_project_name,
                    "repo": target_repo,
                    "language": "C"  # 根据筛选条件，默认为C
                }
            },
            "versions": {
                "source": {
                    "vulnerable": {
                        "version": source_patch.get('version', 'unknown'),
                        "commit": source_patch.get('parent_sha', 'unknown')
                    },
                    "patched": {
                        "version": source_patch.get('version', 'unknown'),
                        "commit": source_patch.get('sha', 'unknown')
                    }
                },
                "target": {
                    "vulnerable": {
                        "version": target_patch.get('version', 'unknown'),
                        "commit": target_patch.get('parent_sha', 'unknown')
                    },
                    "patched": {
                        "version": target_patch.get('version', 'unknown'),
                        "commit": target_patch.get('sha', 'unknown')
                    }
                }
            },
            "source_dataset": "NVD_PatchTracker",
            "metadata": {
                "total_patches_found": patch_pair.get('total_patches', 0),
                "high_confidence_patches": patch_pair.get('high_confidence_patches', 0),
                "source_patch_confidence": source_patch.get('confidence', 0),
                "target_patch_confidence": target_patch.get('confidence', 0),
                "source_patch_date": source_patch.get('commit_date', 'unknown'),
                "target_patch_date": target_patch.get('commit_date', 'unknown'),
                "collection_timestamp": datetime.now().isoformat()
            }
        }
        
        return json_obj
    
    def extract_project_name(self, repo_url: str) -> str:
        """
        从仓库URL中提取项目名称
        
        Args:
            repo_url: 仓库URL
            
        Returns:
            str: 项目名称
        """
        if not repo_url or repo_url == 'unknown':
            return 'unknown'
        
        # 从GitHub URL提取项目名
        github_match = re.search(r'github\.com/([^/]+)/([^/]+)', repo_url)
        if github_match:
            return github_match.group(2).replace('.git', '')
        
        # 从其他Git URL提取项目名
        git_match = re.search(r'/([^/]+)\.git$', repo_url)
        if git_match:
            return git_match.group(1)
        
        # 最后尝试从URL路径提取
        try:
            from urllib.parse import urlparse
            parsed = urlparse(repo_url)
            path_parts = [p for p in parsed.path.split('/') if p]
            if path_parts:
                return path_parts[-1].replace('.git', '')
        except Exception:
            pass
        
        return 'unknown'
    
    def collect_patches_for_timerange(self,
                                    start_date: datetime,
                                    end_date: datetime,
                                    max_cves: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        收集指定时间范围内的CVE补丁对 (优化内存版本)

        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_cves: 最大处理CVE数量限制

        Returns:
            List[Dict]: 收集到的补丁对列表
        """
        self.logger.info(f"开始收集 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 期间的CVE补丁对")
        self.memory_monitor.log_memory_usage("收集开始前 ")

        # 详细日志：显示API调用参数
        self.logger.info(f"📊 API调用参数:")
        self.logger.info(f"  - 开始日期: {start_date.strftime('%Y-%m-%dT%H:%M:%S.000')}")
        self.logger.info(f"  - 结束日期: {end_date.strftime('%Y-%m-%dT%H:%M:%S.999')}")
        self.logger.info(f"  - 日期类型: published")
        self.logger.info(f"  - 最大CVE数量: {max_cves if max_cves else '无限制'}")
        self.logger.info(f"  - 批处理大小: {self.batch_size}")

        # 获取时间范围内的CVE列表 - 无限制模式
        self.logger.info("🔍 正在调用NVD API获取CVE列表...")
        cve_list = self.fetcher.get_references_by_timerange(
            start_date=start_date,
            end_date=end_date,
            date_type='published',
            save_individual=False
        )

        if not cve_list:
            self.logger.warning("⚠️ 在指定时间范围内未找到CVE")
            return []
        
        self.logger.info(f"找到 {len(cve_list)} 个CVE")
        
        # 应用CVE数量限制
        if max_cves and max_cves < len(cve_list):
            cve_list = cve_list[:max_cves]
            self.logger.info(f"由于数量限制，只处理前 {max_cves} 个CVE")
        
        # 过滤已存在的CVE
        cve_list = self._filter_existing_cves(cve_list)
        self.logger.info(f"过滤后需要处理的CVE数量: {len(cve_list)}")
        
        # 分批处理CVE以减少内存使用
        all_patch_pairs = []
        total_batches = (len(cve_list) + self.batch_size - 1) // self.batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * self.batch_size
            end_idx = min(start_idx + self.batch_size, len(cve_list))
            batch_cves = cve_list[start_idx:end_idx]
            
            self.logger.info(f"🔄 处理批次 {batch_idx + 1}/{total_batches} (CVE {start_idx+1}-{end_idx})")
            self.memory_monitor.log_memory_usage(f"批次 {batch_idx + 1} 开始前 ")
            
            # 处理当前批次
            batch_results = self._process_cve_batch(batch_cves, batch_idx + 1, total_batches)
            
            if batch_results:
                all_patch_pairs.extend(batch_results)
                
                # 保存中间结果
                self._save_intermediate_results(batch_results, batch_idx + 1)
            
            # 内存管理
            self.memory_monitor.log_memory_usage(f"批次 {batch_idx + 1} 完成后 ")
            
            # 检查内存使用并清理
            if self.memory_monitor.is_memory_limit_exceeded():
                self.logger.warning("⚠️ 内存使用超限，执行强制清理")
                self._cleanup_tracer()
                self.memory_monitor.force_gc()
                self.memory_monitor.log_memory_usage("清理后 ")
            
            # 批次间暂停，让系统缓解压力
            if batch_idx < total_batches - 1:
                time.sleep(2)
        
        self.logger.info(f"补丁对收集完成: 总共收集到 {len(all_patch_pairs)} 个补丁对")
        self.memory_monitor.log_memory_usage("收集完成后 ")
        return all_patch_pairs
    
    def _process_cve_batch(self, batch_cves: List[Dict[str, Any]], 
                          batch_num: int, total_batches: int) -> List[Dict[str, Any]]:
        """
        处理一批CVE
        
        Args:
            batch_cves: 当前批次的CVE列表
            batch_num: 当前批次号
            total_batches: 总批次数
            
        Returns:
            List[Dict[str, Any]]: 当前批次收集到的补丁对
        """
        collected_patch_pairs = []
        processed_count = 0
        success_count = 0
        
        # 提高并发数以加快处理速度
        max_workers = min(16, len(batch_cves))  # 提高到16个并发线程
        self.logger.info(f"批次 {batch_num} 使用 {max_workers} 个线程处理 {len(batch_cves)} 个CVE")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_cve = {
                executor.submit(self._process_single_cve_with_cleanup, cve_info): cve_info 
                for cve_info in batch_cves
            }
            
            # 收集结果
            for future in concurrent.futures.as_completed(future_to_cve):
                cve_info = future_to_cve[future]
                cve_id = cve_info.get('cve_id', 'Unknown')
                processed_count += 1
                
                try:
                    patch_pairs = future.result()
                    if patch_pairs:
                        collected_patch_pairs.extend(patch_pairs)
                        success_count += 1
                        self.logger.info(f"✅ [批次{batch_num} {processed_count}/{len(batch_cves)}] {cve_id}: {len(patch_pairs)} 个补丁对")
                    else:
                        self.logger.info(f"⏭️ [批次{batch_num} {processed_count}/{len(batch_cves)}] {cve_id}: 未找到符合条件的补丁对")
                        
                except Exception as e:
                    self.logger.error(f"❌ [批次{batch_num} {processed_count}/{len(batch_cves)}] {cve_id} 失败: {e}")
                    continue
                
                # 定期检查内存
                if processed_count % 10 == 0:
                    self.memory_monitor.log_memory_usage(f"批次{batch_num} 进度{processed_count}/{len(batch_cves)} ")
        
        return collected_patch_pairs
    
    def _process_single_cve_with_cleanup(self, cve_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理单个CVE的线程安全函数（带内存清理）
        
        Args:
            cve_info: CVE信息
            
        Returns:
            List[Dict[str, Any]]: 收集到的补丁对
        """
        cve_id = cve_info.get('cve_id')
        if not cve_id:
            return []
            
        try:
            # 获取CVE的补丁信息
            result = self.collect_patches_for_cve(cve_id)
            
            # 立即清理可能的临时数据
            gc.collect()
            
            return result
        except Exception as e:
            self.logger.error(f"处理CVE {cve_id} 时发生错误: {e}")
            return []
    
    def _save_intermediate_results(self, patch_pairs: List[Dict[str, Any]], batch_num: int):
        """
        保存中间结果
        
        Args:
            patch_pairs: 补丁对列表
            batch_num: 批次号
        """
        if not patch_pairs:
            return
            
        filename = f"batch_{batch_num:03d}_patch_pairs.json"
        output_path = os.path.join(self.output_dir, "intermediate", filename)
        
        # 确保中间结果目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存当前批次结果
        batch_data = {
            "batch_info": {
                "batch_number": batch_num,
                "patch_pairs_count": len(patch_pairs),
                "timestamp": datetime.now().isoformat()
            },
            "patch_pairs": patch_pairs
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(batch_data, f, indent=2, ensure_ascii=False)
        
        self.logger.debug(f"中间结果已保存: {output_path}")
    
    def _filter_existing_cves(self, cve_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤已经存在的CVE，避免重复处理
        
        Args:
            cve_list: CVE列表
            
        Returns:
            List[Dict[str, Any]]: 过滤后的CVE列表
        """
        filtered_list = []
        skipped_count = 0
        
        for cve_info in cve_list:
            cve_id = cve_info.get('cve_id')
            if not cve_id:
                continue
                
            # 检查CVE目录是否已存在
            cve_dir = os.path.join(self.output_dir, cve_id)
            trace_result_file = os.path.join(cve_dir, 'trace_result.json')
            
            if os.path.exists(trace_result_file):
                # 检查文件是否完整
                try:
                    with open(trace_result_file, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                        if result.get('cve_id') == cve_id:
                            skipped_count += 1
                            self.logger.debug(f"跳过已存在的CVE: {cve_id}")
                            continue
                except:
                    # 文件损坏，重新处理
                    pass
            
            filtered_list.append(cve_info)
        
        if skipped_count > 0:
            self.logger.info(f"跳过 {skipped_count} 个已存在的CVE")
        
        return filtered_list
    
    def collect_patches_for_cve(self, cve_id: str) -> List[Dict[str, Any]]:
        """
        为单个CVE收集补丁对
        
        Args:
            cve_id: CVE ID
            
        Returns:
            List[Dict]: 补丁对列表
        """
        try:
            # 获取CVE的初始引用
            references_by_source = get_initial_references(
                cve_id=cve_id,
                data_dir=self.output_dir,
                config_path=self.config_path,
                use_cache=True
            )
            
            if not any(references_by_source.values()):
                self.logger.warning(f"{cve_id} 未找到任何引用")
                return []
            
            # 准备引用链接
            initial_references, source_mapping = prepare_references_for_tracer(references_by_source)
            
            if not initial_references:
                self.logger.warning(f"{cve_id} 没有有效的引用链接")
                return []
            
            # 执行补丁跟踪
            result = self._get_tracer().trace(
                cve_id=cve_id,
                initial_references=initial_references,
                sources=source_mapping,
                language="c,cpp,c++",  # 只关注C/C++
                skip_url_check=True
            )
            
            # 提取补丁对
            patch_pairs = self.extract_patch_pairs(result)
            
            # 格式化为需求的JSON格式
            formatted_pairs = []
            for patch_pair in patch_pairs:
                # 检查是否为C/C++项目
                if (self.is_cpp_project(patch_pair['source_patch']) or 
                    self.is_cpp_project(patch_pair['target_patch'])):
                    
                    formatted_pair = self.format_to_required_json(cve_id, patch_pair)
                    formatted_pairs.append(formatted_pair)
            
            return formatted_pairs
            
        except Exception as e:
            self.logger.error(f"收集 {cve_id} 补丁时出错: {str(e)}")
            return []
    
    def save_results(self, patch_pairs: List[Dict[str, Any]], filename: str = "nvd_patch_pairs.json"):
        """
        保存收集结果 (优化版本)
        
        Args:
            patch_pairs: 补丁对列表
            filename: 输出文件名
        """
        output_path = os.path.join(self.output_dir, filename)
        
        # 合并所有中间结果
        intermediate_dir = os.path.join(self.output_dir, "intermediate")
        if os.path.exists(intermediate_dir):
            self.logger.info("合并中间结果文件...")
            all_intermediate_pairs = []
            
            for batch_file in sorted(os.listdir(intermediate_dir)):
                if batch_file.endswith('.json'):
                    batch_path = os.path.join(intermediate_dir, batch_file)
                    try:
                        with open(batch_path, 'r', encoding='utf-8') as f:
                            batch_data = json.load(f)
                            batch_pairs = batch_data.get('patch_pairs', [])
                            all_intermediate_pairs.extend(batch_pairs)
                    except Exception as e:
                        self.logger.error(f"读取中间文件 {batch_file} 失败: {e}")
            
            # 合并当前结果和中间结果
            if all_intermediate_pairs:
                self.logger.info(f"从中间文件合并了 {len(all_intermediate_pairs)} 个补丁对")
                patch_pairs.extend(all_intermediate_pairs)
        
        # 创建完整的输出结构
        output_data = {
            "collection_info": {
                "total_patch_pairs": len(patch_pairs),
                "collection_date": datetime.now().isoformat(),
                "time_range": {
                    "start": "2023-08-23",
                    "end": "2025-07-24"
                },
                "criteria": {
                    "language": "C/C++",
                    "patch_selection": "mystique_standard",
                    "min_patches_per_cve": 2
                },
                "memory_optimization": {
                    "batch_processing": True,
                    "max_concurrent_threads": 8,
                    "intermediate_saves": True
                }
            },
            "patch_pairs": patch_pairs
        }
        
        # 分块写入大文件
        self.logger.info(f"保存 {len(patch_pairs)} 个补丁对到 {output_path}")
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"结果已保存到: {output_path}")
            self.memory_monitor.log_memory_usage("保存完成后 ")
            
            return output_path
        except Exception as e:
            self.logger.error(f"保存结果时出错: {e}")
            raise


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="NVD补丁收集器 - 收集指定时间范围的CVE补丁对")
    
    parser.add_argument('--output-dir', '-o', type=str, 
                        default='tasks/202300823nvdpatched_collected',
                        help='输出目录 (默认: tasks/202300823nvdpatched_collected)')
    
    parser.add_argument('--start-date', type=str, 
                        default='2023-08-23',
                        help='开始日期 (YYYY-MM-DD格式, 默认: 2023-08-23)')
    
    parser.add_argument('--end-date', type=str,
                        default='2025-07-29',
                        help='结束日期 (YYYY-MM-DD格式, 默认: 2025-07-29)')
    
    parser.add_argument('--max-cves', type=int, 
                        help='最大处理CVE数量限制')
    
    parser.add_argument('--github-token', type=str,
                        help='GitHub API令牌')
    
    parser.add_argument('--config', type=str,
                        help='配置文件路径')
    
    parser.add_argument('--max-depth', type=int, default=2,
                        help='最大跟踪深度 (默认: 2)')
    
    parser.add_argument('--confidence', type=float, default=0.7,
                        help='置信度阈值 (默认: 0.7)')
    
    args = parser.parse_args()
    
    try:
        # 解析日期
        start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
        end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
        
        # 初始化收集器
        collector = NVDPatchCollector(
            output_dir=args.output_dir,
            github_token=args.github_token,
            config_path=args.config,
            max_depth=args.max_depth,
            confidence_threshold=args.confidence
        )
        
        # 收集补丁对
        patch_pairs = collector.collect_patches_for_timerange(
            start_date=start_date,
            end_date=end_date,
            max_cves=args.max_cves
        )
        
        # 保存结果
        output_path = collector.save_results(patch_pairs)
        
        print(f"\n=== 收集完成 ===")
        print(f"总共收集到 {len(patch_pairs)} 个补丁对")
        print(f"结果保存在: {output_path}")
        
        return 0
        
    except Exception as e:
        print(f"收集过程中出错: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main()) 