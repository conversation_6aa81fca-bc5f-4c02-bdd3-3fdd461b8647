nohup: ignoring input
🚀 启动内存优化版CVE补丁收集器
处理所有可用CVE，支持多线程并发，优化内存使用
开始时间: Sat Jul 26 18:49:00 UTC 2025
内存限制: 4.0GB
批处理大小: 20
段大小: 7天
📦 检查Python依赖...
📊 计划处理 101 个段，每段 7 天

========== 段 1/101 ==========
时间范围: 2023-08-23 到 2023-08-30
模式: 内存优化 + 批处理 + 并发处理
段开始前内存状态:
               total        used        free      shared  buff/cache   available
Mem:           629Gi       5.5Gi       598Gi        15Mi        25Gi       619Gi
Swap:           30Gi          0B        30Gi
执行: python3 nvd_patch_collector.py --output-dir /home/<USER>/202300823nvdpatched_collected/segment_1 --start-date 2023-08-23 --end-date 2023-08-30 --max-depth 5 --confidence 0.7 --config /home/<USER>/src/data_collection/config/sources.json --github-token *********************************************************************************************
[32m2025-07-26 18:49:02.912[0m | [1mINFO    [0m | [36m__main__[0m:[36m__init__[0m:[36m173[0m | [1mNVD补丁收集器初始化完成[0m
[32m2025-07-26 18:49:02.912[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m初始化后 内存使用: RSS=224.6MB, VMS=3236.9MB, 百分比=0.0%, 可用=634368.0MB[0m
[32m2025-07-26 18:49:02.913[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m438[0m | [1m开始收集 2023-08-23 到 2023-08-30 期间的CVE补丁对[0m
[32m2025-07-26 18:49:02.913[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m收集开始前 内存使用: RSS=224.6MB, VMS=3236.9MB, 百分比=0.0%, 可用=634368.0MB[0m
[32m2025-07-26 18:49:02.913[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m442[0m | [1m📊 API调用参数:[0m
[32m2025-07-26 18:49:02.913[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m443[0m | [1m  - 开始日期: 2023-08-23T00:00:00.000[0m
[32m2025-07-26 18:49:02.914[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m444[0m | [1m  - 结束日期: 2023-08-30T00:00:00.999[0m
[32m2025-07-26 18:49:02.914[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m445[0m | [1m  - 日期类型: published[0m
[32m2025-07-26 18:49:02.914[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m446[0m | [1m  - 最大CVE数量: 无限制[0m
[32m2025-07-26 18:49:02.914[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m447[0m | [1m  - 批处理大小: 50[0m
[32m2025-07-26 18:49:02.915[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m450[0m | [1m🔍 正在调用NVD API获取CVE列表...[0m
[32m2025-07-26 18:49:14.846[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m462[0m | [1m找到 354 个CVE[0m
[32m2025-07-26 18:49:14.849[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m471[0m | [1m过滤后需要处理的CVE数量: 354[0m
[32m2025-07-26 18:49:14.849[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 1/8 (CVE 1-50)[0m
[32m2025-07-26 18:49:14.850[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 1 开始前 内存使用: RSS=232.7MB, VMS=3248.0MB, 百分比=0.0%, 可用=634385.7MB[0m
[32m2025-07-26 18:49:14.850[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 1 使用 16 个线程处理 50 个CVE[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:20.510[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:20.608[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 1/50] CVE-2023-4041: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:20.681[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:20.769[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 2/50] CVE-2023-39986: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:20.779[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:20.863[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 3/50] CVE-2023-3495: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:21.625[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:21.808[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 4/50] CVE-2023-39984: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:22.606[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:22.770[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 5/50] CVE-2023-39985: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:24.027[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:24.175[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 6/50] CVE-2023-4404: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:24.392[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:24.541[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 7/50] CVE-2023-40144: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:24.879[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:24.980[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 8/50] CVE-2023-40158: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:25.478[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:25.582[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 9/50] CVE-2023-38585: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:25.978[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:26.116[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 10/50] CVE-2023-41100: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:26.124[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度10/50 内存使用: RSS=1270.3MB, VMS=5292.7MB, 百分比=0.2%, 可用=632992.7MB[0m
[32m2025-07-26 18:49:26.938[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:27.041[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 11/50] CVE-2023-4428: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:27.766[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:27.914[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 12/50] CVE-2023-32119: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:29.698[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:29.857[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 13/50] CVE-2023-32236: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:31.006[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:31.171[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 14/50] CVE-2023-32496: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:31.260[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:31.441[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 15/50] CVE-2023-32497: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:32.038[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:32.197[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 16/50] CVE-2023-32498: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:33.042[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:33.208[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 17/50] CVE-2023-32499: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
/home/<USER>/src/data_collection/services/patch_tracer/reference_network.py:446: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`). Consider using `matplotlib.pyplot.close()`.
  plt.figure(figsize=(18, 12), dpi=300)  # 增加图像尺寸
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:33.773[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:33.900[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 18/50] CVE-2023-3899: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:34.207[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:34.357[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 19/50] CVE-2023-4042: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:35.236[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:35.400[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 20/50] CVE-2023-28994: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:35.406[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度20/50 内存使用: RSS=2514.8MB, VMS=6359.5MB, 百分比=0.4%, 可用=631580.5MB[0m
[32m2025-07-26 18:49:35.499[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:35.691[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 21/50] CVE-2023-32300: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:36.234[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:36.383[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 22/50] CVE-2023-32505: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:36.550[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:36.711[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 23/50] CVE-2023-32509: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
连接错误: https://github.com/MISP/MISP/commit/09fb0cba65eab9341e81f1cbebc2ae10be34a2b7 - HTTPSConnectionPool(host='github.com', port=443): Read timed out.
[32m2025-07-26 18:49:39.776[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:39.938[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 24/50] CVE-2023-20115: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:40.768[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:40.904[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 25/50] CVE-2023-1409: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:41.166[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:41.338[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 26/50] CVE-2023-20169: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:41.356[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:41.496[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 27/50] CVE-2023-20168: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:49:43.060[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:43.245[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 28/50] CVE-2023-20200: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:43.604[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:43.751[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 29/50] CVE-2023-41098: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:44.508[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:44.654[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 30/50] CVE-2023-20230: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:49:44.657[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度30/50 内存使用: RSS=2887.5MB, VMS=6712.9MB, 百分比=0.4%, 可用=631313.1MB[0m
[32m2025-07-26 18:49:44.845[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:49:44.991[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 31/50] CVE-2023-20234: 未找到符合条件的补丁对[0m
连接错误: https://github.com/apache/airflow/pull/32052 - HTTPSConnectionPool(host='github.com', port=443): Read timed out.
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:50:26.085[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:50:26.233[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 32/50] CVE-2023-37379: 未找到符合条件的补丁对[0m
🧹 清理资源...
