#!/bin/bash

echo "🔄 优化重启CVE收集器（解决SSL问题）"

# 停止现有进程
pkill -f "run_concurrent_unlimited.sh" 2>/dev/null
pkill -f "nvd_patch_collector.py" 2>/dev/null

# 配置参数 - 优化设置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/home/<USER>"
OUTPUT_DIR="$SCRIPT_DIR"
CONFIG_FILE="$PROJECT_ROOT/src/data_collection/config/sources.json"
START_DATE="2023-08-23"
END_DATE="2025-07-24"
MAX_DEPTH=5
CONFIDENCE=0.7
SEGMENT_DAYS=30  # 缩短时间段，减少单次请求量

GITHUB_TOKEN="*********************************************************************************************"

echo "📊 优化参数："
echo "  - 时间段缩短: 每段${SEGMENT_DAYS}天 (原90天)"
echo "  - 深度设置: ${MAX_DEPTH}"
echo "  - 并发线程: 8个"
echo "  - 错误重试: 启用"

# 分段处理
start_epoch=$(date -d "$START_DATE" +%s)
end_epoch=$(date -d "$END_DATE" +%s)
segment_seconds=$((SEGMENT_DAYS * 24 * 3600))

current_start=$start_epoch
segment_count=0

while [ $current_start -lt $end_epoch ]; do
    segment_count=$((segment_count + 1))
    current_end=$((current_start + segment_seconds))

    if [ $current_end -gt $end_epoch ]; then
        current_end=$end_epoch
    fi

    segment_start_date=$(date -d "@$current_start" +%Y-%m-%d)
    segment_end_date=$(date -d "@$current_end" +%Y-%m-%d)

    echo ""
    echo "========== 段 $segment_count =========="
    echo "时间范围: $segment_start_date 到 $segment_end_date"
    echo "优化模式: 小时间段 + SSL错误处理"

    SEGMENT_ARGS=(
        --output-dir "$OUTPUT_DIR/segment_${segment_count}"
        --start-date "$segment_start_date"
        --end-date "$segment_end_date"
        --max-depth "$MAX_DEPTH"
        --confidence "$CONFIDENCE"
    )

    if [ -n "$CONFIG_FILE" ]; then
        SEGMENT_ARGS+=(--config "$CONFIG_FILE")
    fi

    if [ -n "$GITHUB_TOKEN" ]; then
        SEGMENT_ARGS+=(--github-token "$GITHUB_TOKEN")
    fi

    mkdir -p "$OUTPUT_DIR/segment_${segment_count}"

    echo "执行: python3 nvd_patch_collector.py ${SEGMENT_ARGS[*]}"
    
    # 添加重试机制
    max_retries=3
    for attempt in $(seq 1 $max_retries); do
        echo "尝试 $attempt/$max_retries ..."
        
        if timeout 1800 python3 nvd_patch_collector.py "${SEGMENT_ARGS[@]}"; then
            echo "✅ 段 $segment_count 完成"
            cve_count=$(find "$OUTPUT_DIR/segment_${segment_count}" -name "trace_result.json" 2>/dev/null | wc -l)
            echo "   处理了 $cve_count 个CVE"
            break
        else
            echo "❌ 段 $segment_count 尝试 $attempt 失败"
            if [ $attempt -lt $max_retries ]; then
                echo "⏳ 等待60秒后重试..."
                sleep 60
            fi
        fi
    done

    current_start=$((current_end + 1))
done

echo ""
echo "🎉 优化收集完成！总共 $segment_count 个段"
echo "完成时间: $(date)" 