#!/bin/bash

# 无限制并发CVE补丁收集脚本 (内存优化版本)
# 支持跳过已存在的CVE，多线程并发处理，无CVE数量限制
# 新增内存监控和管理功能，防止Docker崩溃

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="/home/<USER>"

# 配置参数 (优化版本)
OUTPUT_DIR="$SCRIPT_DIR"
CONFIG_FILE="$PROJECT_ROOT/src/data_collection/config/sources.json"
START_DATE="2023-08-23"
END_DATE="2025-07-24"
MAX_DEPTH=2
CONFIDENCE=0.7
SEGMENT_DAYS=7                    # 从30天改为7天，减少每段的数据量
MAX_MEMORY_GB=4.0                 # 添加内存限制 (4GB)
BATCH_SIZE=20                     # 每批处理的CVE数量

# GitHub Token (建议从环境变量读取)
if [ -z "$GITHUB_TOKEN" ]; then
    GITHUB_TOKEN="*********************************************************************************************"
fi

echo "🚀 启动内存优化版CVE补丁收集器"
echo "处理所有可用CVE，支持多线程并发，优化内存使用"
echo "开始时间: $(date)"
echo "内存限制: ${MAX_MEMORY_GB}GB"
echo "批处理大小: ${BATCH_SIZE}"
echo "段大小: ${SEGMENT_DAYS}天"

# 临时禁用代理环境变量 (修复host.docker.internal连接问题)
if [ ! -z "$http_proxy" ] || [ ! -z "$https_proxy" ]; then
    echo "🔧 检测到代理设置，临时禁用以修复连接问题..."
    echo "原代理设置: http_proxy=$http_proxy, https_proxy=$https_proxy"
    export OLD_HTTP_PROXY="$http_proxy"
    export OLD_HTTPS_PROXY="$https_proxy"
    unset http_proxy
    unset https_proxy
    echo "✅ 已临时禁用代理设置"
fi

# 检查依赖
echo "📦 检查Python依赖..."
python3 -c "import psutil" 2>/dev/null || {
    echo "⚠️ 缺少psutil依赖，正在安装..."
    pip3 install psutil
}

# 设置Python路径
export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"

# 创建输出目录
mkdir -p "$OUTPUT_DIR/logs"
mkdir -p "$OUTPUT_DIR/intermediate"

# 内存监控函数
monitor_memory() {
    while true; do
        memory_usage=$(free -m | awk 'NR==2{printf "%.1f", $3*100/$2}')
        available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 系统内存使用: ${memory_usage}%, 可用: ${available_memory}MB" >> "$OUTPUT_DIR/logs/memory_monitor.log"
        sleep 30
    done
}

# 启动内存监控 (后台运行)
monitor_memory &
MONITOR_PID=$!

# 设置清理函数
cleanup() {
    echo "🧹 清理资源..."
    if [ ! -z "$MONITOR_PID" ]; then
        kill $MONITOR_PID 2>/dev/null
    fi
    
    # 恢复原代理设置
    if [ ! -z "$OLD_HTTP_PROXY" ]; then
        export http_proxy="$OLD_HTTP_PROXY"
        echo "🔄 已恢复http_proxy设置"
    fi
    if [ ! -z "$OLD_HTTPS_PROXY" ]; then
        export https_proxy="$OLD_HTTPS_PROXY"
        echo "🔄 已恢复https_proxy设置"
    fi
}
trap cleanup EXIT

# 分段处理
start_epoch=$(date -d "$START_DATE" +%s)
end_epoch=$(date -d "$END_DATE" +%s)
segment_seconds=$((SEGMENT_DAYS * 24 * 3600))

current_start=$start_epoch
segment_count=0
total_segments=$(( (end_epoch - start_epoch + segment_seconds - 1) / segment_seconds ))

echo "📊 计划处理 $total_segments 个段，每段 $SEGMENT_DAYS 天"

while [ $current_start -lt $end_epoch ]; do
    segment_count=$((segment_count + 1))
    current_end=$((current_start + segment_seconds))

    if [ $current_end -gt $end_epoch ]; then
        current_end=$end_epoch
    fi

    segment_start_date=$(date -d "@$current_start" +%Y-%m-%d)
    segment_end_date=$(date -d "@$current_end" +%Y-%m-%d)

    echo ""
    echo "========== 段 $segment_count/$total_segments =========="
    echo "时间范围: $segment_start_date 到 $segment_end_date"
    echo "模式: 内存优化 + 批处理 + 并发处理"

    # 段开始前检查内存
    echo "段开始前内存状态:"
    free -h

    SEGMENT_ARGS=(
        --output-dir "$OUTPUT_DIR/segment_${segment_count}"
        --start-date "$segment_start_date"
        --end-date "$segment_end_date"
        --max-depth "$MAX_DEPTH"
        --confidence "$CONFIDENCE"
    )

    if [ -n "$CONFIG_FILE" ]; then
        SEGMENT_ARGS+=(--config "$CONFIG_FILE")
    fi

    if [ -n "$GITHUB_TOKEN" ]; then
        SEGMENT_ARGS+=(--github-token "$GITHUB_TOKEN")
    fi

    mkdir -p "$OUTPUT_DIR/segment_${segment_count}"

    echo "执行: python3 nvd_patch_collector.py ${SEGMENT_ARGS[*]}"
    
    # 使用超时机制，防止单个段运行太久
    timeout 7200 python3 nvd_patch_collector.py "${SEGMENT_ARGS[@]}"
    exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ 段 $segment_count 完成"
        cve_count=$(find "$OUTPUT_DIR/segment_${segment_count}" -name "trace_result.json" | wc -l)
        intermediate_count=$(find "$OUTPUT_DIR/segment_${segment_count}/intermediate" -name "*.json" 2>/dev/null | wc -l)
        echo "   处理了 $cve_count 个CVE，中间文件 $intermediate_count 个"
    elif [ $exit_code -eq 124 ]; then
        echo "⏰ 段 $segment_count 超时，跳过到下一段"
    else
        echo "❌ 段 $segment_count 失败 (退出码: $exit_code)"
    fi

    # 段完成后的内存清理
    echo "执行段间内存清理..."
    python3 -c "
import gc
import time
print('清理Python缓存...')
gc.collect()
time.sleep(2)
print('清理完成')
"
    
    # 显示段完成后内存状态
    echo "段完成后内存状态:"
    free -h
    
    # 段间暂停
    if [ $segment_count -lt $total_segments ]; then
        echo "⏸️ 段间暂停5秒..."
        sleep 5
    fi

    current_start=$((current_end + 1))
done

echo ""
echo "🎉 所有段处理完成，总共 $segment_count 个段"
echo "完成时间: $(date)"

# 合并所有段的结果
echo "📝 合并所有段的结果..."
python3 -c "
import os
import json
import glob
from datetime import datetime

output_dir = '$OUTPUT_DIR'
all_patch_pairs = []
total_cves = 0

# 收集所有段的结果
for segment_dir in sorted(glob.glob(os.path.join(output_dir, 'segment_*'))):
    segment_json = os.path.join(segment_dir, 'nvd_patch_pairs.json')
    if os.path.exists(segment_json):
        try:
            with open(segment_json, 'r', encoding='utf-8') as f:
                data = json.load(f)
                pairs = data.get('patch_pairs', [])
                all_patch_pairs.extend(pairs)
                print(f'从 {os.path.basename(segment_dir)} 合并了 {len(pairs)} 个补丁对')
        except Exception as e:
            print(f'合并 {segment_dir} 时出错: {e}')
    
    # 统计CVE数量
    cve_count = len(glob.glob(os.path.join(segment_dir, '*/trace_result.json')))
    total_cves += cve_count

# 保存最终结果
final_result = {
    'collection_info': {
        'total_patch_pairs': len(all_patch_pairs),
        'total_cves_processed': total_cves,
        'collection_date': datetime.now().isoformat(),
        'time_range': {
            'start': '$START_DATE',
            'end': '$END_DATE'
        },
        'optimization': {
            'memory_limit_gb': $MAX_MEMORY_GB,
            'batch_size': $BATCH_SIZE,
            'segment_days': $SEGMENT_DAYS,
            'total_segments': $segment_count
        }
    },
    'patch_pairs': all_patch_pairs
}

final_output = os.path.join(output_dir, 'final_nvd_patch_pairs.json')
with open(final_output, 'w', encoding='utf-8') as f:
    json.dump(final_result, f, indent=2, ensure_ascii=False)

print(f'✅ 最终结果已保存: {final_output}')
print(f'📊 总计: {len(all_patch_pairs)} 个补丁对，{total_cves} 个CVE')
"

# 显示最终统计
echo ""
echo "📊 最终统计："
echo "- 处理段数: $segment_count"
echo "- 处理时间: $(($(date +%s) - $(date -d "$(echo $0 | head -n1)" +%s 2>/dev/null || echo 0))) 秒"
echo "- 日志文件: $OUTPUT_DIR/logs/"
echo "- 内存监控: $OUTPUT_DIR/logs/memory_monitor.log"

exit 0
