nohup: ignoring input
🚀 启动内存优化版CVE补丁收集器
处理所有可用CVE，支持多线程并发，优化内存使用
开始时间: Sat Jul 26 18:35:09 UTC 2025
内存限制: 4.0GB
批处理大小: 20
段大小: 7天
📦 检查Python依赖...
📊 计划处理 101 个段，每段 7 天

========== 段 1/101 ==========
时间范围: 2023-08-23 到 2023-08-30
模式: 内存优化 + 批处理 + 并发处理
段开始前内存状态:
               total        used        free      shared  buff/cache   available
Mem:           629Gi       5.4Gi       598Gi        15Mi        25Gi       619Gi
Swap:           30Gi          0B        30Gi
执行: python3 nvd_patch_collector.py --output-dir /home/<USER>/202300823nvdpatched_collected/segment_1 --start-date 2023-08-23 --end-date 2023-08-30 --max-depth 5 --confidence 0.7 --config /home/<USER>/src/data_collection/config/sources.json --github-token *********************************************************************************************
[32m2025-07-26 18:35:11.594[0m | [1mINFO    [0m | [36m__main__[0m:[36m__init__[0m:[36m173[0m | [1mNVD补丁收集器初始化完成[0m
[32m2025-07-26 18:35:11.595[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m初始化后 内存使用: RSS=222.6MB, VMS=3235.4MB, 百分比=0.0%, 可用=634451.0MB[0m
[32m2025-07-26 18:35:11.595[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m438[0m | [1m开始收集 2023-08-23 到 2023-08-30 期间的CVE补丁对[0m
[32m2025-07-26 18:35:11.595[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m收集开始前 内存使用: RSS=222.6MB, VMS=3235.4MB, 百分比=0.0%, 可用=634451.0MB[0m
[32m2025-07-26 18:35:11.596[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m442[0m | [1m📊 API调用参数:[0m
[32m2025-07-26 18:35:11.596[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m443[0m | [1m  - 开始日期: 2023-08-23T00:00:00.000[0m
[32m2025-07-26 18:35:11.596[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m444[0m | [1m  - 结束日期: 2023-08-30T00:00:00.999[0m
[32m2025-07-26 18:35:11.596[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m445[0m | [1m  - 日期类型: published[0m
[32m2025-07-26 18:35:11.597[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m446[0m | [1m  - 最大CVE数量: 无限制[0m
[32m2025-07-26 18:35:11.597[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m447[0m | [1m  - 批处理大小: 50[0m
[32m2025-07-26 18:35:11.597[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m450[0m | [1m🔍 正在调用NVD API获取CVE列表...[0m
[32m2025-07-26 18:35:23.563[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m462[0m | [1m找到 354 个CVE[0m
[32m2025-07-26 18:35:23.565[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m471[0m | [1m过滤后需要处理的CVE数量: 354[0m
[32m2025-07-26 18:35:23.565[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 1/8 (CVE 1-50)[0m
[32m2025-07-26 18:35:23.566[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 1 开始前 内存使用: RSS=232.1MB, VMS=3247.6MB, 百分比=0.0%, 可用=634454.3MB[0m
[32m2025-07-26 18:35:23.567[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 1 使用 16 个线程处理 50 个CVE[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:30.144[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:30.250[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 1/50] CVE-2023-39984: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:35:30.253[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:30.755[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:30.755[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:30.839[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 4/50] CVE-2023-39986: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:35:30.839[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 4/50] CVE-2023-39986: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:31.501[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:31.637[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 5/50] CVE-2023-3495: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:35.359[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:35.494[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 6/50] CVE-2023-41098: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:35.840[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:35.945[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 7/50] CVE-2023-38585: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:36.404[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:36.517[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 8/50] CVE-2023-40144: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:36.869[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:37.002[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 9/50] CVE-2023-40158: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:38.247[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:38.384[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 10/50] CVE-2023-41100: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:35:38.398[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度10/50 内存使用: RSS=1419.0MB, VMS=5751.9MB, 百分比=0.2%, 可用=632750.4MB[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:38.606[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:38.707[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 11/50] CVE-2023-4404: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:38.930[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:39.107[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 12/50] CVE-2023-32119: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:42.804[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:42.955[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 13/50] CVE-2023-32236: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:44.222[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:44.385[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 14/50] CVE-2023-32496: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:46.004[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:46.135[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 15/50] CVE-2023-32497: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:46.351[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:46.546[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 16/50] CVE-2023-28994: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:47.009[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:47.190[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 17/50] CVE-2023-32498: 未找到符合条件的补丁对[0m
/home/<USER>/src/data_collection/services/patch_tracer/reference_network.py:446: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`). Consider using `matplotlib.pyplot.close()`.
  plt.figure(figsize=(18, 12), dpi=300)  # 增加图像尺寸
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:48.196[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:48.338[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 18/50] CVE-2023-3899: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:48.604[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:48.767[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 19/50] CVE-2023-32499: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:48.916[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:49.088[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 20/50] CVE-2023-4042: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:35:49.106[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度20/50 内存使用: RSS=2051.8MB, VMS=6217.1MB, 百分比=0.3%, 可用=632168.9MB[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:50.633[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:35:50.794[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 21/50] CVE-2023-41104: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:51.001[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:51.136[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 22/50] CVE-2023-32300: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:52.009[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
GitHub API未返回有效数据
[32m2025-07-26 18:35:52.217[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 23/50] CVE-2023-32505: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:53.179[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:53.365[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 24/50] CVE-2023-32509: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:56.645[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:56.911[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 25/50] CVE-2023-1409: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:58.119[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:35:58.301[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 26/50] CVE-2023-20115: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:58.643[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:58.843[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 27/50] CVE-2023-20168: 未找到符合条件的补丁对[0m
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:35:59.090[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:35:59.335[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 28/50] CVE-2023-20169: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:00.502[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
GitHub API未返回有效数据
[32m2025-07-26 18:36:00.755[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 29/50] CVE-2023-20200: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:03.143[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:03.501[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 30/50] CVE-2023-20230: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:36:03.513[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度30/50 内存使用: RSS=2811.4MB, VMS=6921.3MB, 百分比=0.4%, 可用=631471.9MB[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:05.068[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:05.264[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 31/50] CVE-2023-20234: 未找到符合条件的补丁对[0m
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/a089e98f9eeb83524286dc4399d8186956af8f20
GitHub API未返回有效数据
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/aaa8c26fc40af65e2c51c0f12ad9c5f6fa2e0622
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/2708578da6cf6f7feff2d5a79b783e6cf2469821
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/a089e98f9eeb83524286dc4399d8186956af8f20
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/aaa8c26fc40af65e2c51c0f12ad9c5f6fa2e0622
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/2e8f658a8ce0d0ee4047b9cfd8f626e3159b2f42
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/2708578da6cf6f7feff2d5a79b783e6cf2469821
获取GitHub提交信息时出错: 422 Client Error: Unprocessable Entity for url: https://api.github.com/repos/apache/airflow/commits/2e8f658a8ce0d0ee4047b9cfd8f626e3159b2f42
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:13.515[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:13.701[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 32/50] CVE-2023-40273: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:14.567[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:14.755[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 33/50] CVE-2023-39441: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:21.996[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:22.196[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 34/50] CVE-2023-37379: 未找到符合条件的补丁对[0m
GitHub API未返回有效数据
========== GitHub漏洞采集器已启动 ==========
每页记录数: 100, 请求间隔: 2秒
============================================
[32m2025-07-26 18:36:25.366[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:25.545[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 35/50] CVE-2023-40025: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:36:35.360[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:36:35.563[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 36/50] CVE-2023-40176: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:36:39.463[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:36:39.653[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 37/50] CVE-2023-40612: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:37:06.040[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m281[0m | [1m有效时间戳的补丁数量少于2个，排除[0m
[32m2025-07-26 18:37:06.230[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 38/50] CVE-2023-41105: 未找到符合条件的补丁对[0m
🧹 清理资源...
