nohup: ignoring input
🚀 启动内存优化版CVE补丁收集器
处理所有可用CVE，支持多线程并发，优化内存使用
开始时间: Sat Jul 26 18:44:10 UTC 2025
内存限制: 4.0GB
批处理大小: 20
段大小: 7天
📦 检查Python依赖...
📊 计划处理 101 个段，每段 7 天

========== 段 1/101 ==========
时间范围: 2023-08-23 到 2023-08-30
模式: 内存优化 + 批处理 + 并发处理
段开始前内存状态:
               total        used        free      shared  buff/cache   available
Mem:           629Gi       5.5Gi       598Gi        15Mi        25Gi       619Gi
Swap:           30Gi          0B        30Gi
执行: python3 nvd_patch_collector.py --output-dir /home/<USER>/202300823nvdpatched_collected/segment_1 --start-date 2023-08-23 --end-date 2023-08-30 --max-depth 5 --confidence 0.7 --config /home/<USER>/src/data_collection/config/sources.json
[32m2025-07-26 18:44:12.688[0m | [1mINFO    [0m | [36m__main__[0m:[36m__init__[0m:[36m173[0m | [1mNVD补丁收集器初始化完成[0m
[32m2025-07-26 18:44:12.689[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m初始化后 内存使用: RSS=224.9MB, VMS=3236.9MB, 百分比=0.0%, 可用=634355.9MB[0m
[32m2025-07-26 18:44:12.689[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m438[0m | [1m开始收集 2023-08-23 到 2023-08-30 期间的CVE补丁对[0m
[32m2025-07-26 18:44:12.690[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m收集开始前 内存使用: RSS=224.9MB, VMS=3236.9MB, 百分比=0.0%, 可用=634355.9MB[0m
[32m2025-07-26 18:44:12.690[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m442[0m | [1m📊 API调用参数:[0m
[32m2025-07-26 18:44:12.690[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m443[0m | [1m  - 开始日期: 2023-08-23T00:00:00.000[0m
[32m2025-07-26 18:44:12.691[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m444[0m | [1m  - 结束日期: 2023-08-30T00:00:00.999[0m
[32m2025-07-26 18:44:12.691[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m445[0m | [1m  - 日期类型: published[0m
[32m2025-07-26 18:44:12.691[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m446[0m | [1m  - 最大CVE数量: 无限制[0m
[32m2025-07-26 18:44:12.691[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m447[0m | [1m  - 批处理大小: 50[0m
[32m2025-07-26 18:44:12.691[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m450[0m | [1m🔍 正在调用NVD API获取CVE列表...[0m
[32m2025-07-26 18:44:24.500[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m462[0m | [1m找到 354 个CVE[0m
[32m2025-07-26 18:44:24.502[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m471[0m | [1m过滤后需要处理的CVE数量: 354[0m
[32m2025-07-26 18:44:24.502[0m | [1mINFO    [0m | [36m__main__[0m:[36mcollect_patches_for_timerange[0m:[36m482[0m | [1m🔄 处理批次 1/8 (CVE 1-50)[0m
[32m2025-07-26 18:44:24.503[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次 1 开始前 内存使用: RSS=233.0MB, VMS=3247.8MB, 百分比=0.0%, 可用=634391.2MB[0m
[32m2025-07-26 18:44:24.503[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m531[0m | [1m批次 1 使用 16 个线程处理 50 个CVE[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:26.174[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:26.275[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 1/50] CVE-2023-41098: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:30.601[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:30.723[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 2/50] CVE-2023-39986: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:30.730[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:30.878[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 3/50] CVE-2023-39984: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:30.988[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:31.103[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 4/50] CVE-2023-39985: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:31.118[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:31.213[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 5/50] CVE-2023-3495: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:31.469[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:31.599[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 6/50] CVE-2023-4041: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:33.031[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:33.135[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 7/50] CVE-2023-41100: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:34.014[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:34.149[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 8/50] CVE-2023-40158: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:34.313[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:34.449[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 9/50] CVE-2023-4404: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:34.687[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:34.879[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 10/50] CVE-2023-40144: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:34.934[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度10/50 内存使用: RSS=1618.5MB, VMS=5373.2MB, 百分比=0.3%, 可用=632681.9MB[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:35.427[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:35.713[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 11/50] CVE-2023-38585: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:36.231[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:36.405[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 12/50] CVE-2023-32119: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
/home/<USER>/src/data_collection/services/patch_tracer/reference_network.py:446: RuntimeWarning: More than 20 figures have been opened. Figures created through the pyplot interface (`matplotlib.pyplot.figure`) are retained until explicitly closed and may consume too much memory. (To control this warning, see the rcParam `figure.max_open_warning`). Consider using `matplotlib.pyplot.close()`.
  plt.figure(figsize=(18, 12), dpi=300)  # 增加图像尺寸
[32m2025-07-26 18:44:39.194[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:39.392[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 13/50] CVE-2023-32236: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:39.993[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:40.219[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 14/50] CVE-2023-41105: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:40.945[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:41.140[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 15/50] CVE-2023-32496: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:41.464[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:41.655[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 16/50] CVE-2023-32497: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:42.175[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:42.333[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 17/50] CVE-2023-32498: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:42.405[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:42.571[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 18/50] CVE-2023-32499: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:42.807[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:43.015[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 19/50] CVE-2023-41104: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:44.256[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:44.507[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 20/50] CVE-2023-28994: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:44.518[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度20/50 内存使用: RSS=3021.2MB, VMS=6785.3MB, 百分比=0.5%, 可用=631292.0MB[0m
[32m2025-07-26 18:44:44.801[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:45.006[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 21/50] CVE-2023-4042: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:45.544[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:45.749[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 22/50] CVE-2023-3899: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:45.909[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:46.125[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 23/50] CVE-2023-32300: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:46.294[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:46.468[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 24/50] CVE-2023-32505: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:46.640[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:46.781[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 25/50] CVE-2023-32509: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:49.588[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:49.804[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 26/50] CVE-2023-1409: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:52.124[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:52.403[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 27/50] CVE-2023-20115: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:52.415[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:52.650[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:52.897[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 28/50] CVE-2023-40273: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:52.904[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 29/50] CVE-2023-20230: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:52.914[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:53.139[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 30/50] CVE-2023-20169: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:53.143[0m | [1mINFO    [0m | [36m__main__[0m:[36mlog_memory_usage[0m:[36m95[0m | [1m批次1 进度30/50 内存使用: RSS=3538.7MB, VMS=7241.3MB, 百分比=0.5%, 可用=630813.0MB[0m
[32m2025-07-26 18:44:53.298[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:53.299[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:53.314[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:53.756[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 31/50] CVE-2023-20168: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:53.934[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 32/50] CVE-2023-39441: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:53.937[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 33/50] CVE-2023-37379: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:54.195[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:54.401[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 34/50] CVE-2023-20200: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:55.888[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:56.117[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 35/50] CVE-2023-20234: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:56.366[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:56.546[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 36/50] CVE-2023-40025: 未找到符合条件的补丁对[0m
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
使用规则过滤后没有找到补丁节点，回退到使用所有补丁节点
[32m2025-07-26 18:44:57.717[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:57.961[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 37/50] CVE-2023-40612: 未找到符合条件的补丁对[0m
[32m2025-07-26 18:44:58.209[0m | [1mINFO    [0m | [36m__main__[0m:[36mextract_patch_pairs[0m:[36m262[0m | [1m未找到任何补丁，排除[0m
[32m2025-07-26 18:44:58.416[0m | [1mINFO    [0m | [36m__main__[0m:[36m_process_cve_batch[0m:[36m553[0m | [1m⏭️ [批次1 38/50] CVE-2023-40176: 未找到符合条件的补丁对[0m
🧹 清理资源...
