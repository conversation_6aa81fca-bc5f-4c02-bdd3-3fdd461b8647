#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版等价补丁收集器
基于2024WL_SBOM系统，专门用于收集C/C++项目的等价补丁对
按照mystique paper标准进行补丁对选择和输出格式化
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import time
import re

# 添加2024WL_SBOM路径到Python路径
sys.path.insert(0, '/home/<USER>/src')

from data_collection.services.patch_tracer.reference_fetcher import CVEReferenceFetcher
from data_collection.services.patch_tracer.tracer import PatchTracer
from data_collection.utils.logger import setup_logger

class EnhancedPatchCollector:
    """增强版补丁收集器，专门用于C/C++项目"""
    
    def __init__(self, 
                 output_dir: str,
                 github_token: str,
                 config_path: str = "/home/<USER>/src/data_collection/config/sources.json",
                 max_depth: int = 2,
                 confidence_threshold: float = 0.7):
        """
        初始化收集器
        
        Args:
            output_dir: 输出目录
            github_token: GitHub API令牌
            config_path: 配置文件路径
            max_depth: 最大跟踪深度
            confidence_threshold: 置信度阈值
        """
        self.output_dir = output_dir
        self.github_token = github_token
        self.config_path = config_path
        self.max_depth = max_depth
        self.confidence_threshold = confidence_threshold
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 设置日志
        self.logger = setup_logger(
            log_dir=os.path.join(output_dir, "logs"),
            console_level="INFO",
            file_level="DEBUG"
        )
        
        # 初始化CVE引用获取器
        self.fetcher = CVEReferenceFetcher(config_path)
        
        # 初始化补丁跟踪器
        self.tracer = PatchTracer(
            output_dir=output_dir,
            max_depth=max_depth,
            github_token=github_token,
            confidence_threshold=confidence_threshold,
            verify_ssl=False
        )
        
        self.logger.info("增强版补丁收集器初始化完成")
    
    def is_cpp_project_early_filter(self, cve_data: Dict[str, Any]) -> bool:
        """
        早期C/C++项目过滤，基于CVE描述和引用URL
        
        Args:
            cve_data: CVE数据
            
        Returns:
            bool: 是否可能是C/C++项目
        """
        # 获取CVE描述
        descriptions = cve_data.get('descriptions', [])
        description_text = ""
        for desc in descriptions:
            if desc.get('lang') == 'en':
                description_text = desc.get('value', '').lower()
                break
        
        # 获取引用URL
        references = cve_data.get('references', [])
        reference_urls = [ref.get('url', '').lower() for ref in references]
        
        # C/C++项目的强指示符
        cpp_strong_indicators = [
            'linux', 'kernel', 'gcc', 'clang', 'openssl', 'sqlite',
            'postgres', 'mysql', 'nginx', 'apache', 'curl', 'git',
            'opencv', 'ffmpeg', 'chromium', 'firefox', 'webkit',
            'glibc', 'musl', 'binutils', 'gdb', 'llvm', 'cmake',
            'boost', 'qt', 'gtk', 'gnome', 'kde', 'xorg', 'mesa',
            'systemd', 'dbus', 'pulseaudio', 'alsa', 'bluez',
            'wireshark', 'tcpdump', 'libpcap', 'zlib', 'bzip2',
            'buffer overflow', 'memory corruption', 'segmentation fault',
            'use after free', 'double free', 'heap overflow',
            'stack overflow', 'null pointer dereference'
        ]
        
        # 检查描述中的关键词
        for indicator in cpp_strong_indicators:
            if indicator in description_text:
                self.logger.debug(f"CVE {cve_data.get('id', 'unknown')} 匹配C/C++指示符: {indicator}")
                return True
        
        # 检查引用URL中的项目名称
        for url in reference_urls:
            for indicator in cpp_strong_indicators:
                if indicator in url:
                    self.logger.debug(f"CVE {cve_data.get('id', 'unknown')} URL匹配C/C++指示符: {indicator}")
                    return True
        
        # 检查是否包含明显的非C/C++指示符
        non_cpp_indicators = [
            'python', 'java', 'javascript', 'node.js', 'php', 'ruby',
            'go', 'rust', 'kotlin', 'swift', 'c#', 'dotnet', '.net',
            'wordpress', 'drupal', 'joomla', 'django', 'flask',
            'spring', 'hibernate', 'maven', 'gradle', 'npm', 'pip'
        ]
        
        for indicator in non_cpp_indicators:
            if indicator in description_text:
                self.logger.debug(f"CVE {cve_data.get('id', 'unknown')} 匹配非C/C++指示符: {indicator}")
                return False
        
        # 如果没有明确指示符，返回True以进行进一步检查
        return True
    
    def collect_patches_for_timerange(self, 
                                    start_date: datetime, 
                                    end_date: datetime,
                                    max_cves: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        收集指定时间范围内的C/C++项目补丁对
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            max_cves: 最大处理CVE数量
            
        Returns:
            List[Dict]: 补丁对列表
        """
        self.logger.info(f"开始收集 {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')} 的C/C++补丁对")
        
        # 分段处理时间范围（每次处理30天）
        segment_days = 30
        current_start = start_date
        all_patch_pairs = []
        processed_cves = 0
        
        while current_start < end_date:
            current_end = min(current_start + timedelta(days=segment_days), end_date)
            
            self.logger.info(f"处理时间段: {current_start.strftime('%Y-%m-%d')} 到 {current_end.strftime('%Y-%m-%d')}")
            
            # 获取CVE数据
            cve_data = self.fetcher.get_references_by_timerange(
                start_date=current_start,
                end_date=current_end,
                date_type="published"
            )
            
            self.logger.info(f"获取到 {len(cve_data)} 个CVE")
            
            # 早期过滤C/C++项目
            cpp_cves = []
            for cve in cve_data:
                if self.is_cpp_project_early_filter(cve):
                    cpp_cves.append(cve)
                else:
                    self.logger.debug(f"跳过非C/C++项目CVE: {cve.get('id', 'unknown')}")
            
            self.logger.info(f"早期过滤后剩余 {len(cpp_cves)} 个可能的C/C++项目CVE")
            
            # 处理每个CVE
            for cve in cpp_cves:
                if max_cves and processed_cves >= max_cves:
                    self.logger.info(f"已达到最大CVE处理数量限制: {max_cves}")
                    break
                
                cve_id = cve.get('id', '')
                if not cve_id:
                    continue
                
                self.logger.info(f"处理CVE: {cve_id} ({processed_cves + 1})")
                
                try:
                    # 获取CVE引用
                    references = [ref.get('url', '') for ref in cve.get('references', [])]
                    if not references:
                        self.logger.warning(f"CVE {cve_id} 没有引用URL，跳过")
                        continue
                    
                    # 使用C++语言过滤进行补丁跟踪
                    trace_result = self.tracer.trace(
                        cve_id=cve_id,
                        initial_references=references,
                        language="c,c++,cpp",  # 指定C/C++语言过滤
                        skip_url_check=False
                    )
                    
                    # 检查是否找到了补丁
                    if not trace_result.get('high_confidence_patches'):
                        self.logger.info(f"CVE {cve_id} 未找到高置信度补丁，跳过")
                        continue
                    
                    # 提取补丁对
                    patch_pairs = self.extract_patch_pairs_mystique_style(cve_id, trace_result)
                    if patch_pairs:
                        all_patch_pairs.extend(patch_pairs)
                        self.logger.info(f"CVE {cve_id} 提取到 {len(patch_pairs)} 个补丁对")
                    
                    processed_cves += 1
                    
                    # 添加延迟避免API限制
                    time.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"处理CVE {cve_id} 时出错: {str(e)}")
                    continue
            
            if max_cves and processed_cves >= max_cves:
                break
            
            current_start = current_end
        
        self.logger.info(f"收集完成，总共处理 {processed_cves} 个CVE，获得 {len(all_patch_pairs)} 个补丁对")
        return all_patch_pairs
    
    def extract_patch_pairs_mystique_style(self, cve_id: str, trace_result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        按照mystique paper标准提取补丁对（最早和最晚的补丁）
        
        Args:
            cve_id: CVE ID
            trace_result: 跟踪结果
            
        Returns:
            List[Dict]: 补丁对列表
        """
        equivalent_patches = trace_result.get('equivalent_patches', {})
        patch_pairs = []
        
        for base_patch_url, equiv_patches in equivalent_patches.items():
            if not equiv_patches:
                continue
            
            # 获取基础补丁信息
            base_patch_info = None
            for patch in trace_result.get('high_confidence_patches', []):
                if patch.get('url') == base_patch_url:
                    base_patch_info = patch
                    break
            
            if not base_patch_info:
                continue
            
            # 按照mystique标准：选择最早和最晚的等价补丁
            if len(equiv_patches) >= 2:
                # 按日期排序
                sorted_patches = sorted(equiv_patches, key=lambda x: x.get('commit_date', ''))
                earliest_patch = sorted_patches[0]
                latest_patch = sorted_patches[-1]
                
                # 创建补丁对
                patch_pair = {
                    "cve_id": cve_id,
                    "base_patch": {
                        "url": base_patch_info.get('url', ''),
                        "confidence": base_patch_info.get('confidence', 0),
                        "type": base_patch_info.get('type', ''),
                        "metadata": base_patch_info.get('metadata', {})
                    },
                    "equivalent_patches": {
                        "earliest": earliest_patch,
                        "latest": latest_patch
                    },
                    "total_equivalent_patches": len(equiv_patches),
                    "collection_time": datetime.now().isoformat()
                }
                
                patch_pairs.append(patch_pair)
        
        return patch_pairs
